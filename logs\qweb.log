{"level":"warn","timestamp":"2025-09-02T18:59:53.899+0800","caller":"logger/logger.go:194","msg":"Using default encryption key, please change it in production"}
{"level":"warn","timestamp":"2025-09-02T18:59:53.919+0800","caller":"logger/logger.go:194","msg":"Using default secret key, please change it in production"}
{"level":"warn","timestamp":"2025-09-02T18:59:53.920+0800","caller":"logger/logger.go:194","msg":"Using default JWT secret, please change it in production"}
{"level":"info","timestamp":"2025-09-02T18:59:53.924+0800","caller":"logger/logger.go:190","msg":"Starting application","name":"Qweb网络验证系统","version":"1.0.0","config":"configs/config.yaml"}
{"level":"info","timestamp":"2025-09-02T18:59:53.925+0800","caller":"logger/logger.go:190","msg":"Starting application components..."}
{"level":"info","timestamp":"2025-09-02T18:59:53.964+0800","caller":"logger/logger.go:190","msg":"Database initialized","path":"data\\verification.db"}
{"level":"info","timestamp":"2025-09-02T18:59:53.965+0800","caller":"logger/logger.go:190","msg":"Security manager initialized"}
{"level":"info","timestamp":"2025-09-02T18:59:53.965+0800","caller":"logger/logger.go:190","msg":"Authentication service initialized"}
{"level":"info","timestamp":"2025-09-02T18:59:53.965+0800","caller":"logger/logger.go:190","msg":"Agent service initialized"}
{"level":"info","timestamp":"2025-09-02T18:59:53.966+0800","caller":"logger/logger.go:190","msg":"Starting metrics collector"}
{"level":"info","timestamp":"2025-09-02T18:59:53.966+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_cpu_usage","alert_name":"High CPU Usage"}
{"level":"info","timestamp":"2025-09-02T18:59:53.966+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_memory_usage","alert_name":"High Memory Usage"}
{"level":"info","timestamp":"2025-09-02T18:59:53.966+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_connection_count","alert_name":"High Connection Count"}
{"level":"info","timestamp":"2025-09-02T18:59:53.966+0800","caller":"logger/logger.go:190","msg":"Metrics collector started"}
{"level":"info","timestamp":"2025-09-02T18:59:54.266+0800","caller":"logger/logger.go:190","msg":"TCP server started","port":9999}
{"level":"info","timestamp":"2025-09-02T18:59:54.351+0800","caller":"logger/logger.go:190","msg":"Default admin account initialized","username":"admin"}
{"level":"info","timestamp":"2025-09-02T18:59:54.352+0800","caller":"logger/logger.go:190","msg":"Web server started","port":8080}
{"level":"info","timestamp":"2025-09-02T18:59:54.352+0800","caller":"logger/logger.go:190","msg":"Starting web server","address":"0.0.0.0:8080","tls_enabled":false}
{"level":"info","timestamp":"2025-09-02T18:59:54.353+0800","caller":"logger/logger.go:190","msg":"Application started successfully","tcp_port":9999,"web_port":8080}
{"level":"info","timestamp":"2025-09-02T18:59:54.353+0800","caller":"logger/logger.go:190","msg":"WebSocket real-time updates started"}
{"level":"info","timestamp":"2025-09-02T19:01:18.592+0800","caller":"logger/logger.go:190","msg":"Admin login attempt","username":"***********","ip":"::1"}
{"level":"warn","timestamp":"2025-09-02T19:01:18.593+0800","caller":"logger/logger.go:194","msg":"Admin login failed","username":"***********","ip":"::1","error":"invalid credentials"}
{"level":"info","timestamp":"2025-09-02T19:03:14.894+0800","caller":"logger/logger.go:190","msg":"Received shutdown signal","signal":"terminated"}
{"level":"info","timestamp":"2025-09-02T19:03:14.894+0800","caller":"logger/logger.go:190","msg":"Stopping application..."}
{"level":"info","timestamp":"2025-09-02T19:03:14.894+0800","caller":"logger/logger.go:190","msg":"Shutting down web server"}
{"level":"error","timestamp":"2025-09-02T19:03:14.895+0800","caller":"logger/logger.go:198","msg":"Web server error","error":"http: Server closed"}
{"level":"info","timestamp":"2025-09-02T19:03:14.895+0800","caller":"logger/logger.go:190","msg":"Stopping metrics collector"}
{"level":"info","timestamp":"2025-09-02T19:03:14.905+0800","caller":"logger/logger.go:190","msg":"Application stopped"}
{"level":"warn","timestamp":"2025-09-02T19:21:05.197+0800","caller":"logger/logger.go:194","msg":"Using default encryption key, please change it in production"}
{"level":"warn","timestamp":"2025-09-02T19:21:05.258+0800","caller":"logger/logger.go:194","msg":"Using default secret key, please change it in production"}
{"level":"warn","timestamp":"2025-09-02T19:21:05.259+0800","caller":"logger/logger.go:194","msg":"Using default JWT secret, please change it in production"}
{"level":"info","timestamp":"2025-09-02T19:21:05.261+0800","caller":"logger/logger.go:190","msg":"Starting application","name":"Qweb网络验证系统","version":"1.0.0","config":"configs/config.yaml"}
{"level":"info","timestamp":"2025-09-02T19:21:05.261+0800","caller":"logger/logger.go:190","msg":"Starting application components..."}
{"level":"info","timestamp":"2025-09-02T19:21:09.494+0800","caller":"logger/logger.go:190","msg":"Database initialized","path":"data\\verification.db"}
{"level":"info","timestamp":"2025-09-02T19:21:09.496+0800","caller":"logger/logger.go:190","msg":"Security manager initialized"}
{"level":"info","timestamp":"2025-09-02T19:21:09.498+0800","caller":"logger/logger.go:190","msg":"Authentication service initialized"}
{"level":"info","timestamp":"2025-09-02T19:21:09.500+0800","caller":"logger/logger.go:190","msg":"Agent service initialized"}
{"level":"info","timestamp":"2025-09-02T19:21:09.500+0800","caller":"logger/logger.go:190","msg":"Starting metrics collector"}
{"level":"info","timestamp":"2025-09-02T19:21:09.501+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_cpu_usage","alert_name":"High CPU Usage"}
{"level":"info","timestamp":"2025-09-02T19:21:09.502+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_memory_usage","alert_name":"High Memory Usage"}
{"level":"info","timestamp":"2025-09-02T19:21:09.502+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_connection_count","alert_name":"High Connection Count"}
{"level":"info","timestamp":"2025-09-02T19:21:09.504+0800","caller":"logger/logger.go:190","msg":"Metrics collector started"}
{"level":"info","timestamp":"2025-09-02T19:21:09.714+0800","caller":"logger/logger.go:190","msg":"TCP server started","port":9999}
{"level":"info","timestamp":"2025-09-02T19:21:09.719+0800","caller":"logger/logger.go:190","msg":"Web server started","port":8080}
{"level":"info","timestamp":"2025-09-02T19:21:09.721+0800","caller":"logger/logger.go:190","msg":"Application started successfully","tcp_port":9999,"web_port":8080}
{"level":"info","timestamp":"2025-09-02T19:21:09.720+0800","caller":"logger/logger.go:190","msg":"Starting web server","address":"0.0.0.0:8080","tls_enabled":false}
{"level":"info","timestamp":"2025-09-02T19:21:09.724+0800","caller":"logger/logger.go:190","msg":"WebSocket real-time updates started"}
{"level":"info","timestamp":"2025-09-02T19:38:39.709+0800","caller":"logger/logger.go:190","msg":"Received shutdown signal","signal":"terminated"}
{"level":"info","timestamp":"2025-09-02T19:38:39.710+0800","caller":"logger/logger.go:190","msg":"Stopping application..."}
{"level":"info","timestamp":"2025-09-02T19:38:39.710+0800","caller":"logger/logger.go:190","msg":"Shutting down web server"}
{"level":"error","timestamp":"2025-09-02T19:38:39.711+0800","caller":"logger/logger.go:198","msg":"Web server error","error":"http: Server closed"}
{"level":"info","timestamp":"2025-09-02T19:38:39.712+0800","caller":"logger/logger.go:190","msg":"Stopping metrics collector"}
{"level":"info","timestamp":"2025-09-02T19:38:39.729+0800","caller":"logger/logger.go:190","msg":"Application stopped"}
{"level":"warn","timestamp":"2025-09-02T20:04:03.647+0800","caller":"logger/logger.go:194","msg":"Using default encryption key, please change it in production"}
{"level":"warn","timestamp":"2025-09-02T20:04:03.665+0800","caller":"logger/logger.go:194","msg":"Using default secret key, please change it in production"}
{"level":"warn","timestamp":"2025-09-02T20:04:03.665+0800","caller":"logger/logger.go:194","msg":"Using default JWT secret, please change it in production"}
{"level":"info","timestamp":"2025-09-02T20:04:03.665+0800","caller":"logger/logger.go:190","msg":"Starting application","name":"Qweb网络验证系统","version":"1.0.0","config":"configs/config.yaml"}
{"level":"info","timestamp":"2025-09-02T20:04:03.665+0800","caller":"logger/logger.go:190","msg":"Starting application components..."}
{"level":"info","timestamp":"2025-09-02T20:04:03.709+0800","caller":"logger/logger.go:190","msg":"Database initialized","path":"data\\verification.db"}
{"level":"info","timestamp":"2025-09-02T20:04:03.710+0800","caller":"logger/logger.go:190","msg":"Security manager initialized"}
{"level":"info","timestamp":"2025-09-02T20:04:03.710+0800","caller":"logger/logger.go:190","msg":"Authentication service initialized"}
{"level":"info","timestamp":"2025-09-02T20:04:03.710+0800","caller":"logger/logger.go:190","msg":"Agent service initialized"}
{"level":"info","timestamp":"2025-09-02T20:04:03.711+0800","caller":"logger/logger.go:190","msg":"Starting metrics collector"}
{"level":"info","timestamp":"2025-09-02T20:04:03.711+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_cpu_usage","alert_name":"High CPU Usage"}
{"level":"info","timestamp":"2025-09-02T20:04:03.711+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_memory_usage","alert_name":"High Memory Usage"}
{"level":"info","timestamp":"2025-09-02T20:04:03.711+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_connection_count","alert_name":"High Connection Count"}
{"level":"info","timestamp":"2025-09-02T20:04:03.711+0800","caller":"logger/logger.go:190","msg":"Metrics collector started"}
{"level":"info","timestamp":"2025-09-02T20:04:03.941+0800","caller":"logger/logger.go:190","msg":"TCP server started","port":9999}
{"level":"info","timestamp":"2025-09-02T20:04:03.945+0800","caller":"logger/logger.go:190","msg":"Web server started","port":8080}
{"level":"info","timestamp":"2025-09-02T20:04:03.945+0800","caller":"logger/logger.go:190","msg":"Starting web server","address":"0.0.0.0:8080","tls_enabled":false}
{"level":"info","timestamp":"2025-09-02T20:04:03.945+0800","caller":"logger/logger.go:190","msg":"Application started successfully","tcp_port":9999,"web_port":8080}
{"level":"info","timestamp":"2025-09-02T20:04:03.945+0800","caller":"logger/logger.go:190","msg":"WebSocket real-time updates started"}
{"level":"warn","timestamp":"2025-09-02T20:07:02.297+0800","caller":"logger/logger.go:194","msg":"Using default encryption key, please change it in production"}
{"level":"warn","timestamp":"2025-09-02T20:07:02.318+0800","caller":"logger/logger.go:194","msg":"Using default secret key, please change it in production"}
{"level":"warn","timestamp":"2025-09-02T20:07:02.318+0800","caller":"logger/logger.go:194","msg":"Using default JWT secret, please change it in production"}
{"level":"info","timestamp":"2025-09-02T20:07:02.319+0800","caller":"logger/logger.go:190","msg":"Starting application","name":"Qweb网络验证系统","version":"1.0.0","config":"configs/config.yaml"}
{"level":"info","timestamp":"2025-09-02T20:07:02.319+0800","caller":"logger/logger.go:190","msg":"Starting application components..."}
{"level":"info","timestamp":"2025-09-02T20:07:02.379+0800","caller":"logger/logger.go:190","msg":"Database initialized","path":"data\\verification.db"}
{"level":"info","timestamp":"2025-09-02T20:07:02.380+0800","caller":"logger/logger.go:190","msg":"Security manager initialized"}
{"level":"info","timestamp":"2025-09-02T20:07:02.380+0800","caller":"logger/logger.go:190","msg":"Authentication service initialized"}
{"level":"info","timestamp":"2025-09-02T20:07:02.380+0800","caller":"logger/logger.go:190","msg":"Agent service initialized"}
{"level":"info","timestamp":"2025-09-02T20:07:02.380+0800","caller":"logger/logger.go:190","msg":"Starting metrics collector"}
{"level":"info","timestamp":"2025-09-02T20:07:02.381+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_cpu_usage","alert_name":"High CPU Usage"}
{"level":"info","timestamp":"2025-09-02T20:07:02.381+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_memory_usage","alert_name":"High Memory Usage"}
{"level":"info","timestamp":"2025-09-02T20:07:02.381+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_connection_count","alert_name":"High Connection Count"}
{"level":"info","timestamp":"2025-09-02T20:07:02.382+0800","caller":"logger/logger.go:190","msg":"Metrics collector started"}
