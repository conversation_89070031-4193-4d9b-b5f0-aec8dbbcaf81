<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qweb网络验证系统 - 管理员登录</title>
    <meta name="description" content="Qweb网络验证系统管理员登录页面，安全可靠的身份验证">
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="shortcut icon" href="/static/img/favicon.ico">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
</head>
<body class="modern-login-page">
    <!-- 动态背景 -->
    <div class="login-background">
        <div class="bg-gradient-orb bg-orb-1"></div>
        <div class="bg-gradient-orb bg-orb-2"></div>
        <div class="bg-gradient-orb bg-orb-3"></div>
        <div class="bg-particles">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
        </div>
    </div>

    <div class="modern-login-container">
        <!-- 返回首页链接 -->
        <div class="login-nav">
            <a href="/" class="back-home-link">
                <svg class="back-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                <span>返回首页</span>
            </a>
        </div>

        <div class="modern-login-box">
            <!-- 现代化登录表单头部 -->
            <div class="modern-login-header">
                <div class="login-logo">
                    <div class="logo-icon-container">
                        <svg class="logo-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                        </svg>
                    </div>
                    <div class="logo-text">
                        <h1 class="logo-title">Qweb验证系统</h1>
                        <p class="logo-subtitle">管理员控制台</p>
                    </div>
                </div>
                <div class="login-welcome">
                    <h2 class="welcome-title">欢迎回来</h2>
                    <p class="welcome-description">请登录您的管理员账户以继续</p>
                </div>
            </div>

            <!-- 现代化登录表单 -->
            <form id="login-form" class="modern-login-form">
                <div class="form-group">
                    <label for="username" class="form-label">
                        <svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                        </svg>
                        用户名
                    </label>
                    <div class="input-group">
                        <svg class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                        </svg>
                        <input type="text" id="username" name="username" class="form-input"
                               placeholder="请输入您的用户名" required autocomplete="username">
                    </div>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">
                        <svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                            <circle cx="12" cy="16" r="1"/>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                        </svg>
                        密码
                    </label>
                    <div class="input-group">
                        <svg class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                            <circle cx="12" cy="16" r="1"/>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                        </svg>
                        <input type="password" id="password" name="password" class="form-input"
                               placeholder="请输入您的密码" required autocomplete="current-password">
                        <button type="button" class="password-toggle" onclick="togglePassword()" aria-label="切换密码显示">
                            <svg class="eye-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                <circle cx="12" cy="12" r="3"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="form-group form-options">
                    <label class="checkbox-container">
                        <input type="checkbox" id="remember" name="remember">
                        <span class="checkbox-checkmark"></span>
                        <span class="checkbox-label">记住我的登录状态</span>
                    </label>
                    <a href="#" class="forgot-password-link">忘记密码？</a>
                </div>

                <button type="submit" class="btn btn-primary btn-full btn-login" id="login-btn">
                    <span class="btn-text">
                        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                        </svg>
                        立即登录
                    </span>
                    <div class="btn-loading" style="display: none;">
                        <div class="spinner"></div>
                        <span>登录中...</span>
                    </div>
                </button>
            </form>

            <!-- 现代化错误提示 -->
            <div id="error-message" class="modern-error-message" style="display: none;">
                <div class="error-content">
                    <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="10"/>
                        <line x1="15" y1="9" x2="9" y2="15"/>
                        <line x1="9" y1="9" x2="15" y2="15"/>
                    </svg>
                    <div class="error-text-wrapper">
                        <h4 class="error-title">登录失败</h4>
                        <p class="error-text"></p>
                    </div>
                </div>
                <button class="error-close" onclick="hideError()" aria-label="关闭错误提示">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>

            <!-- 登录选项 -->
            <div class="login-options">
                <div class="divider">
                    <span class="divider-text">或者</span>
                </div>
                <div class="alternative-actions">
                    <a href="#" class="alt-action-link">
                        <svg class="alt-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <span>需要帮助？</span>
                    </a>
                    <a href="#" class="alt-action-link">
                        <svg class="alt-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        <span>查看文档</span>
                    </a>
                </div>
            </div>

            <!-- 现代化登录页面底部 -->
            <div class="modern-login-footer">
                <div class="footer-links">
                    <a href="#" class="footer-link">隐私政策</a>
                    <span class="footer-separator">•</span>
                    <a href="#" class="footer-link">服务条款</a>
                    <span class="footer-separator">•</span>
                    <a href="#" class="footer-link">技术支持</a>
                </div>
                <p class="footer-copyright">
                    &copy; 2024 Qweb网络验证系统. 保留所有权利.
                </p>
            </div>
        </div>

        <!-- 登录页面装饰元素 -->
        <div class="login-decorations">
            <div class="decoration-circle decoration-1"></div>
            <div class="decoration-circle decoration-2"></div>
            <div class="decoration-circle decoration-3"></div>
        </div>
    </div>

    <script>
        // 登录页面JavaScript
        class LoginPage {
            constructor() {
                this.form = document.getElementById('login-form');
                this.loginBtn = document.getElementById('login-btn');
                this.errorMessage = document.getElementById('error-message');
                
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.checkExistingAuth();
            }

            setupEventListeners() {
                this.form.addEventListener('submit', (e) => this.handleLogin(e));
                
                // 回车键登录
                document.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.handleLogin(e);
                    }
                });
            }

            async handleLogin(e) {
                e.preventDefault();
                
                const formData = new FormData(this.form);
                const username = formData.get('username').trim();
                const password = formData.get('password');
                const remember = formData.get('remember') === 'on';

                if (!username || !password) {
                    this.showError('请输入用户名和密码');
                    return;
                }

                this.setLoading(true);
                this.hideError();

                try {
                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ username, password })
                    });

                    const result = await response.json();

                    if (result.success) {
                        // 保存令牌
                        if (remember) {
                            localStorage.setItem('admin_token', result.data.token);
                        } else {
                            sessionStorage.setItem('admin_token', result.data.token);
                        }
                        
                        // 跳转到管理界面
                        window.location.href = '/admin';
                    } else {
                        this.showError(result.message || '登录失败，请检查用户名和密码');
                    }
                } catch (error) {
                    console.error('登录错误:', error);
                    this.showError('网络错误，请稍后重试');
                } finally {
                    this.setLoading(false);
                }
            }

            setLoading(loading) {
                const btnText = this.loginBtn.querySelector('.btn-text');
                const btnLoading = this.loginBtn.querySelector('.btn-loading');
                
                if (loading) {
                    btnText.style.display = 'none';
                    btnLoading.style.display = 'flex';
                    this.loginBtn.disabled = true;
                } else {
                    btnText.style.display = 'block';
                    btnLoading.style.display = 'none';
                    this.loginBtn.disabled = false;
                }
            }

            showError(message) {
                const errorText = this.errorMessage.querySelector('.error-text');
                errorText.textContent = message;
                this.errorMessage.style.display = 'flex';
                
                // 3秒后自动隐藏
                setTimeout(() => this.hideError(), 3000);
            }

            hideError() {
                this.errorMessage.style.display = 'none';
            }

            checkExistingAuth() {
                const token = localStorage.getItem('admin_token') || sessionStorage.getItem('admin_token');
                if (token) {
                    // 验证令牌有效性
                    fetch('/api/auth/profile', {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    })
                    .then(response => response.json())
                    .then(result => {
                        if (result.success) {
                            window.location.href = '/admin';
                        }
                    })
                    .catch(() => {
                        // 令牌无效，清除
                        localStorage.removeItem('admin_token');
                        sessionStorage.removeItem('admin_token');
                    });
                }
            }
        }

        // 密码显示/隐藏切换
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const eyeIcon = document.querySelector('.eye-icon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.innerHTML = `
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                    <line x1="1" y1="1" x2="23" y2="23"/>
                `;
            } else {
                passwordInput.type = 'password';
                eyeIcon.innerHTML = `
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                    <circle cx="12" cy="12" r="3"/>
                `;
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            new LoginPage();
        });
    </script>
</body>
</html>