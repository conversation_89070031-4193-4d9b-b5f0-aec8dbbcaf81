<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qweb网络验证系统 - 管理员登录</title>
    <meta name="description" content="Qweb网络验证系统管理员登录页面，安全可靠的身份验证">
    <link rel="stylesheet" href="/static/css/clean.css">
    <link rel="shortcut icon" href="/static/img/favicon.ico">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <style>
        /* 登录页面专用样式 */
        .clean-login-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
            position: relative;
        }

        .login-nav {
            position: absolute;
            top: 2rem;
            left: 2rem;
        }

        .back-home-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            text-decoration: none;
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 0.9rem;
            transition: var(--transition);
            box-shadow: var(--shadow-sm);
        }

        .back-home-link:hover {
            color: var(--primary-color);
            border-color: var(--primary-color);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .login-container {
            width: 100%;
            max-width: 400px;
            padding: 2rem;
        }

        .login-box {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-xl);
            padding: 3rem;
            box-shadow: var(--shadow-xl);
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-logo {
            margin-bottom: 1.5rem;
        }

        .logo-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .logo-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .logo-subtitle {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .welcome-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .welcome-description {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.9rem;
        }

        .input-group {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.1rem;
            z-index: 1;
        }

        .form-input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            font-size: 1rem;
            transition: var(--transition);
            background: var(--bg-primary);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.1rem;
            padding: 0.25rem;
            border-radius: var(--radius-sm);
            transition: var(--transition);
        }

        .password-toggle:hover {
            background: var(--bg-secondary);
        }

        .form-options {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 2rem;
        }

        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
        }

        .checkbox-container input[type="checkbox"] {
            width: 1rem;
            height: 1rem;
            accent-color: var(--primary-color);
        }

        .checkbox-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .forgot-password-link {
            font-size: 0.9rem;
            color: var(--primary-color);
            text-decoration: none;
            transition: var(--transition);
        }

        .forgot-password-link:hover {
            text-decoration: underline;
        }

        .btn-login {
            font-size: 1rem;
            padding: 1rem;
        }

        .error-message {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
            color: var(--error-color);
            padding: 1rem;
            border-radius: var(--radius-lg);
            margin-bottom: 1rem;
            font-size: 0.9rem;
            display: none;
        }

        .spinner {
            width: 1rem;
            height: 1rem;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .login-nav {
                top: 1rem;
                left: 1rem;
            }

            .login-container {
                padding: 1rem;
            }

            .login-box {
                padding: 2rem;
            }
        }
    </style>
</head>
<body class="clean-login-page">
    <!-- 返回首页链接 -->
    <div class="login-nav">
        <a href="/" class="back-home-link">
            ← 返回首页
        </a>
    </div>

    <div class="login-container">
        <div class="login-box">
            <!-- 登录表单头部 -->
            <div class="login-header">
                <div class="login-logo">
                    <div class="logo-icon">🔐</div>
                    <h1 class="logo-title">Qweb验证系统</h1>
                    <p class="logo-subtitle">管理员控制台</p>
                </div>
                <div class="login-welcome">
                    <h2 class="welcome-title">欢迎回来</h2>
                    <p class="welcome-description">请登录您的管理员账户以继续</p>
                </div>
            </div>

            <!-- 错误提示 -->
            <div id="error-message" class="error-message">
                <span class="error-text"></span>
            </div>

            <!-- 登录表单 -->
            <form id="login-form" class="login-form">
                <div class="form-group">
                    <label for="username" class="form-label">
                        👤 用户名
                    </label>
                    <div class="input-group">
                        <span class="input-icon">👤</span>
                        <input type="text" id="username" name="username" class="form-input"
                               placeholder="请输入您的用户名" required autocomplete="username">
                    </div>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">
                        🔒 密码
                    </label>
                    <div class="input-group">
                        <span class="input-icon">🔒</span>
                        <input type="password" id="password" name="password" class="form-input"
                               placeholder="请输入您的密码" required autocomplete="current-password">
                        <button type="button" class="password-toggle" onclick="togglePassword()" aria-label="切换密码显示">
                            <span class="eye-icon">👁️</span>
                        </button>
                    </div>
                </div>

                <div class="form-options">
                    <label class="checkbox-container">
                        <input type="checkbox" id="remember" name="remember">
                        <span class="checkbox-label">记住我的登录状态</span>
                    </label>
                    <a href="#" class="forgot-password-link">忘记密码？</a>
                </div>

                <button type="submit" class="btn btn-primary btn-full btn-login" id="login-btn">
                    <span class="btn-text">🚀 立即登录</span>
                    <div class="btn-loading" style="display: none;">
                        <div class="spinner"></div>
                        <span>登录中...</span>
                    </div>
                </button>
            </form>

            <!-- 登录页面底部 -->
            <div class="login-footer">
                <p style="text-align: center; color: var(--text-tertiary); font-size: 0.8rem; margin-top: 2rem;">
                    &copy; 2024 Qweb网络验证系统. 保留所有权利.
                </p>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 密码显示切换
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const eyeIcon = document.querySelector('.eye-icon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                eyeIcon.textContent = '👁️';
            }
        }

        // 表单提交处理
        document.getElementById('login-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const loginBtn = document.getElementById('login-btn');
            const btnText = loginBtn.querySelector('.btn-text');
            const btnLoading = loginBtn.querySelector('.btn-loading');
            const errorMessage = document.getElementById('error-message');

            // 显示加载状态
            btnText.style.display = 'none';
            btnLoading.style.display = 'flex';
            loginBtn.disabled = true;
            errorMessage.style.display = 'none';

            const formData = new FormData(this);
            const data = {
                username: formData.get('username'),
                password: formData.get('password'),
                remember: formData.get('remember') === 'on'
            };

            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    // 登录成功，跳转到管理后台
                    window.location.href = '/admin';
                } else {
                    // 显示错误信息
                    errorMessage.querySelector('.error-text').textContent = result.message || '登录失败，请检查用户名和密码';
                    errorMessage.style.display = 'block';
                }
            } catch (error) {
                console.error('登录请求失败:', error);
                errorMessage.querySelector('.error-text').textContent = '网络错误，请稍后重试';
                errorMessage.style.display = 'block';
            } finally {
                // 恢复按钮状态
                btnText.style.display = 'flex';
                btnLoading.style.display = 'none';
                loginBtn.disabled = false;
            }
        });

        // 回车键提交表单
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('login-form').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>

            <!-- 现代化错误提示 -->
            <div id="error-message" class="modern-error-message" style="display: none;">
                <div class="error-content">
                    <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="10"/>
                        <line x1="15" y1="9" x2="9" y2="15"/>
                        <line x1="9" y1="9" x2="15" y2="15"/>
                    </svg>
                    <div class="error-text-wrapper">
                        <h4 class="error-title">登录失败</h4>
                        <p class="error-text"></p>
                    </div>
                </div>
                <button class="error-close" onclick="hideError()" aria-label="关闭错误提示">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>

            <!-- 登录选项 -->
            <div class="login-options">
                <div class="divider">
                    <span class="divider-text">或者</span>
                </div>
                <div class="alternative-actions">
                    <a href="#" class="alt-action-link">
                        <svg class="alt-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <span>需要帮助？</span>
                    </a>
                    <a href="#" class="alt-action-link">
                        <svg class="alt-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        <span>查看文档</span>
                    </a>
                </div>
            </div>

            <!-- 现代化登录页面底部 -->
            <div class="modern-login-footer">
                <div class="footer-links">
                    <a href="#" class="footer-link">隐私政策</a>
                    <span class="footer-separator">•</span>
                    <a href="#" class="footer-link">服务条款</a>
                    <span class="footer-separator">•</span>
                    <a href="#" class="footer-link">技术支持</a>
                </div>
                <p class="footer-copyright">
                    &copy; 2024 Qweb网络验证系统. 保留所有权利.
                </p>
            </div>
        </div>

        <!-- 登录页面装饰元素 -->
        <div class="login-decorations">
            <div class="decoration-circle decoration-1"></div>
            <div class="decoration-circle decoration-2"></div>
            <div class="decoration-circle decoration-3"></div>
        </div>
    </div>

    <script>
        // 登录页面JavaScript
        class LoginPage {
            constructor() {
                this.form = document.getElementById('login-form');
                this.loginBtn = document.getElementById('login-btn');
                this.errorMessage = document.getElementById('error-message');
                
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.checkExistingAuth();
            }

            setupEventListeners() {
                this.form.addEventListener('submit', (e) => this.handleLogin(e));
                
                // 回车键登录
                document.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.handleLogin(e);
                    }
                });
            }

            async handleLogin(e) {
                e.preventDefault();
                
                const formData = new FormData(this.form);
                const username = formData.get('username').trim();
                const password = formData.get('password');
                const remember = formData.get('remember') === 'on';

                if (!username || !password) {
                    this.showError('请输入用户名和密码');
                    return;
                }

                this.setLoading(true);
                this.hideError();

                try {
                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ username, password })
                    });

                    const result = await response.json();

                    if (result.success) {
                        // 保存令牌
                        if (remember) {
                            localStorage.setItem('admin_token', result.data.token);
                        } else {
                            sessionStorage.setItem('admin_token', result.data.token);
                        }
                        
                        // 跳转到管理界面
                        window.location.href = '/admin';
                    } else {
                        this.showError(result.message || '登录失败，请检查用户名和密码');
                    }
                } catch (error) {
                    console.error('登录错误:', error);
                    this.showError('网络错误，请稍后重试');
                } finally {
                    this.setLoading(false);
                }
            }

            setLoading(loading) {
                const btnText = this.loginBtn.querySelector('.btn-text');
                const btnLoading = this.loginBtn.querySelector('.btn-loading');
                
                if (loading) {
                    btnText.style.display = 'none';
                    btnLoading.style.display = 'flex';
                    this.loginBtn.disabled = true;
                } else {
                    btnText.style.display = 'block';
                    btnLoading.style.display = 'none';
                    this.loginBtn.disabled = false;
                }
            }

            showError(message) {
                const errorText = this.errorMessage.querySelector('.error-text');
                errorText.textContent = message;
                this.errorMessage.style.display = 'flex';
                
                // 3秒后自动隐藏
                setTimeout(() => this.hideError(), 3000);
            }

            hideError() {
                this.errorMessage.style.display = 'none';
            }

            checkExistingAuth() {
                const token = localStorage.getItem('admin_token') || sessionStorage.getItem('admin_token');
                if (token) {
                    // 验证令牌有效性
                    fetch('/api/auth/profile', {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    })
                    .then(response => response.json())
                    .then(result => {
                        if (result.success) {
                            window.location.href = '/admin';
                        }
                    })
                    .catch(() => {
                        // 令牌无效，清除
                        localStorage.removeItem('admin_token');
                        sessionStorage.removeItem('admin_token');
                    });
                }
            }
        }

        // 密码显示/隐藏切换
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const eyeIcon = document.querySelector('.eye-icon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.innerHTML = `
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                    <line x1="1" y1="1" x2="23" y2="23"/>
                `;
            } else {
                passwordInput.type = 'password';
                eyeIcon.innerHTML = `
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                    <circle cx="12" cy="12" r="3"/>
                `;
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            new LoginPage();
        });
    </script>
</body>
</html>