# Qweb网络验证系统 - 安全配置示例

# Web服务器配置
server:
  port: 8080
  host: "localhost"
  static_path: "web/static"
  template_path: "web/templates"
  jwt_secret: "your-jwt-secret-key-here-change-in-production"
  cors_enabled: true
  cors_origins: 
    - "http://localhost:3000"
    - "http://127.0.0.1:3000"

# 安全配置
security:
  # IP访问控制
  enable_ip_check: false
  allowed_ips: []
    # - "***********/24"     # 允许内网访问
    # - "10.0.0.0/8"         # 允许私有网络
  blocked_ips: []
    # - "*************/32"   # 阻止特定IP
  
  # 频率限制
  enable_rate_limit: true
  rate_limit: 60              # 每分钟最多请求数
  burst_limit: 10             # 突发请求数
  rate_limit_window: "1m"     # 时间窗口
  
  # TLS/HTTPS配置
  enable_tls: false
  tls_cert_file: "certs/server.crt"
  tls_key_file: "certs/server.key"
  tls_min_version: "TLS1.2"   # TLS1.2 或 TLS1.3
  
  # 安全头设置
  enable_security_headers: true
  csp_policy: "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:;"
  hsts_max_age: 31536000      # HSTS最大年龄（秒）
  
  # 防暴力破解
  enable_brute_force_check: true
  login_attempt_limit: 5      # 登录失败次数限制
  login_lockout_duration: "15m"  # 锁定时间