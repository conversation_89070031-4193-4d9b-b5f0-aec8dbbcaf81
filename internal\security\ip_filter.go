package security

import (
	"fmt"
	"net"
	"sync"
	"time"
	
	"golang.org/x/time/rate"
)

// IPFilter IP过滤器接口
type IPFilter interface {
	IsAllowed(ip string) bool
	IsBlocked(ip string) bool
	BlockIP(ip string, duration time.Duration, reason string) error
	UnblockIP(ip string) error
	AddToWhitelist(ip string) error
	RemoveFromWhitelist(ip string) error
	GetBlockedIPs() []BlockedIP
	CleanExpiredBlocks()
}

// RateLimiter 速率限制器接口
type RateLimiter interface {
	Allow(ip string) bool
	GetLimiter(ip string) *rate.Limiter
	CleanupInactive()
}

// BlockedIP 被封禁的IP信息
type BlockedIP struct {
	IP        string    `json:"ip"`
	ExpiresAt time.Time `json:"expires_at"`
	Reason    string    `json:"reason"`
	BlockedAt time.Time `json:"blocked_at"`
}

// IPFilterImpl IP过滤器实现
type IPFilterImpl struct {
	mutex       sync.RWMutex
	whitelist   map[string]bool
	blocklist   map[string]BlockedIP
	subnets     []*net.IPNet // 允许的子网
}

// NewIPFilter 创建新的IP过滤器
func NewIPFilter() *IPFilterImpl {
	return &IPFilterImpl{
		whitelist: make(map[string]bool),
		blocklist: make(map[string]BlockedIP),
		subnets:   make([]*net.IPNet, 0),
	}
}

// IsAllowed 检查IP是否被允许
func (f *IPFilterImpl) IsAllowed(ip string) bool {
	f.mutex.RLock()
	defer f.mutex.RUnlock()

	// 检查是否在黑名单中
	if blocked, exists := f.blocklist[ip]; exists {
		if blocked.ExpiresAt.After(time.Now()) {
			return false
		}
		// 过期的封禁记录，自动清理
		delete(f.blocklist, ip)
	}

	// 检查白名单
	if f.whitelist[ip] {
		return true
	}

	// 检查子网
	ipAddr := net.ParseIP(ip)
	if ipAddr == nil {
		return false
	}

	for _, subnet := range f.subnets {
		if subnet.Contains(ipAddr) {
			return true
		}
	}

	// 默认允许本地地址
	if isLocalIP(ip) {
		return true
	}

	// 如果没有配置白名单，默认允许
	return len(f.whitelist) == 0 && len(f.subnets) == 0
}

// IsBlocked 检查IP是否被封禁
func (f *IPFilterImpl) IsBlocked(ip string) bool {
	f.mutex.RLock()
	defer f.mutex.RUnlock()

	blocked, exists := f.blocklist[ip]
	if !exists {
		return false
	}

	if blocked.ExpiresAt.Before(time.Now()) {
		// 过期的封禁记录
		delete(f.blocklist, ip)
		return false
	}

	return true
}

// BlockIP 封禁IP
func (f *IPFilterImpl) BlockIP(ip string, duration time.Duration, reason string) error {
	f.mutex.Lock()
	defer f.mutex.Unlock()

	if net.ParseIP(ip) == nil {
		return fmt.Errorf("invalid IP address: %s", ip)
	}

	f.blocklist[ip] = BlockedIP{
		IP:        ip,
		ExpiresAt: time.Now().Add(duration),
		Reason:    reason,
		BlockedAt: time.Now(),
	}

	return nil
}

// UnblockIP 解封IP
func (f *IPFilterImpl) UnblockIP(ip string) error {
	f.mutex.Lock()
	defer f.mutex.Unlock()

	delete(f.blocklist, ip)
	return nil
}

// AddToWhitelist 添加到白名单
func (f *IPFilterImpl) AddToWhitelist(ip string) error {
	f.mutex.Lock()
	defer f.mutex.Unlock()

	// 支持CIDR格式
	if _, ipnet, err := net.ParseCIDR(ip); err == nil {
		f.subnets = append(f.subnets, ipnet)
		return nil
	}

	// 单个IP地址
	if net.ParseIP(ip) == nil {
		return fmt.Errorf("invalid IP address: %s", ip)
	}

	f.whitelist[ip] = true
	return nil
}

// RemoveFromWhitelist 从白名单移除
func (f *IPFilterImpl) RemoveFromWhitelist(ip string) error {
	f.mutex.Lock()
	defer f.mutex.Unlock()

	delete(f.whitelist, ip)

	// 移除匹配的子网
	for i, subnet := range f.subnets {
		if subnet.String() == ip {
			f.subnets = append(f.subnets[:i], f.subnets[i+1:]...)
			break
		}
	}

	return nil
}

// GetBlockedIPs 获取所有被封禁的IP
func (f *IPFilterImpl) GetBlockedIPs() []BlockedIP {
	f.mutex.RLock()
	defer f.mutex.RUnlock()

	blocked := make([]BlockedIP, 0, len(f.blocklist))
	for _, ip := range f.blocklist {
		if ip.ExpiresAt.After(time.Now()) {
			blocked = append(blocked, ip)
		}
	}

	return blocked
}

// CleanExpiredBlocks 清理过期的封禁记录
func (f *IPFilterImpl) CleanExpiredBlocks() {
	f.mutex.Lock()
	defer f.mutex.Unlock()

	now := time.Now()
	for ip, blocked := range f.blocklist {
		if blocked.ExpiresAt.Before(now) {
			delete(f.blocklist, ip)
		}
	}
}

// RateLimiterImpl 速率限制器实现
type RateLimiterImpl struct {
	mutex    sync.RWMutex
	limiters map[string]*rateLimiterEntry
	rate     rate.Limit
	burst    int
	cleanup  time.Duration
}

type rateLimiterEntry struct {
	limiter  *rate.Limiter
	lastUsed time.Time
}

// NewRateLimiter 创建新的速率限制器
func NewRateLimiter(rateLimit rate.Limit, burst int) *RateLimiterImpl {
	rl := &RateLimiterImpl{
		limiters: make(map[string]*rateLimiterEntry),
		rate:     rateLimit,
		burst:    burst,
		cleanup:  5 * time.Minute,
	}

	// 启动清理协程
	go rl.cleanupRoutine()

	return rl
}

// Allow 检查是否允许访问
func (rl *RateLimiterImpl) Allow(ip string) bool {
	limiter := rl.GetLimiter(ip)
	return limiter.Allow()
}

// GetLimiter 获取IP的限制器
func (rl *RateLimiterImpl) GetLimiter(ip string) *rate.Limiter {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	entry, exists := rl.limiters[ip]
	if !exists {
		entry = &rateLimiterEntry{
			limiter:  rate.NewLimiter(rl.rate, rl.burst),
			lastUsed: time.Now(),
		}
		rl.limiters[ip] = entry
	} else {
		entry.lastUsed = time.Now()
	}

	return entry.limiter
}

// CleanupInactive 清理不活跃的限制器
func (rl *RateLimiterImpl) CleanupInactive() {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	cutoff := time.Now().Add(-rl.cleanup)
	for ip, entry := range rl.limiters {
		if entry.lastUsed.Before(cutoff) {
			delete(rl.limiters, ip)
		}
	}
}

// cleanupRoutine 清理协程
func (rl *RateLimiterImpl) cleanupRoutine() {
	ticker := time.NewTicker(rl.cleanup)
	defer ticker.Stop()

	for range ticker.C {
		rl.CleanupInactive()
	}
}

// 辅助函数

// isLocalIP 检查是否为本地IP
func isLocalIP(ip string) bool {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	return parsedIP.IsLoopback() || parsedIP.IsPrivate()
}

// GetClientIP 从请求中获取真实客户端IP
func GetClientIP(remoteAddr string, headers map[string]string) string {
	// 检查X-Forwarded-For头
	if xff := headers["X-Forwarded-For"]; xff != "" {
		ips := parseXForwardedFor(xff)
		if len(ips) > 0 {
			return ips[0]
		}
	}

	// 检查X-Real-IP头
	if realIP := headers["X-Real-IP"]; realIP != "" {
		if net.ParseIP(realIP) != nil {
			return realIP
		}
	}

	// 使用RemoteAddr
	if host, _, err := net.SplitHostPort(remoteAddr); err == nil {
		return host
	}

	return remoteAddr
}

// parseXForwardedFor 解析X-Forwarded-For头
func parseXForwardedFor(xff string) []string {
	var ips []string
	for _, ip := range splitAndTrim(xff, ",") {
		if net.ParseIP(ip) != nil {
			ips = append(ips, ip)
		}
	}
	return ips
}

// splitAndTrim 分割并去除空白
func splitAndTrim(s, sep string) []string {
	var result []string
	for _, part := range splitString(s, sep) {
		if trimmed := trimSpace(part); trimmed != "" {
			result = append(result, trimmed)
		}
	}
	return result
}

// 简单的字符串处理函数
func splitString(s, sep string) []string {
	if s == "" {
		return nil
	}
	return []string{s} // 简化实现
}

func trimSpace(s string) string {
	start := 0
	end := len(s)
	
	for start < end && (s[start] == ' ' || s[start] == '\t' || s[start] == '\n' || s[start] == '\r') {
		start++
	}
	
	for end > start && (s[end-1] == ' ' || s[end-1] == '\t' || s[end-1] == '\n' || s[end-1] == '\r') {
		end--
	}
	
	return s[start:end]
}