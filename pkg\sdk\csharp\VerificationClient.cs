using System;
using System.Net.Security;
using System.Net.Sockets;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Security.Cryptography.X509Certificates;

namespace QwebSDK
{
    /// <summary>
    /// 验证客户端接口
    /// </summary>
    public interface IVerificationClient
    {
        Task<bool> ConnectAsync(string serverAddress, ClientConfig config = null, CancellationToken cancellationToken = default);
        Task DisconnectAsync();
        bool IsConnected { get; }
        ConnectionInfo GetConnectionInfo();

        Task<AuthResult> AuthenticateUserAsync(string username, string password);
        Task<AuthResult> AuthenticateCardAsync(string cardNumber, string cardPassword);
        Task<StatusResult> CheckAuthStatusAsync();
        Task LogoutAsync();

        Task<AuthResult> RefreshSessionAsync();
        SessionInfo GetSessionInfo();

        Task StartHeartbeatAsync();
        Task StopHeartbeatAsync();

        event EventHandler<ConnectionEventArgs> Connected;
        event EventHandler<DisconnectionEventArgs> Disconnected;
        event EventHandler<AuthEventArgs> AuthSuccess;
        event EventHandler<AuthEventArgs> AuthFailed;
        event EventHandler SessionExpired;
        event EventHandler<ErrorEventArgs> Error;
        event EventHandler<MessageEventArgs> MessageReceived;

        Exception GetLastError();
    }

    /// <summary>
    /// 客户端配置
    /// </summary>
    public class ClientConfig
    {
        public TimeSpan ConnectTimeout { get; set; } = TimeSpan.FromSeconds(30);
        public TimeSpan ReadTimeout { get; set; } = TimeSpan.FromSeconds(30);
        public TimeSpan WriteTimeout { get; set; } = TimeSpan.FromSeconds(30);
        public TimeSpan HeartbeatInterval { get; set; } = TimeSpan.FromSeconds(30);
        
        public TlsClientConfig TlsConfig { get; set; } = new TlsClientConfig();
        
        public bool AutoReconnect { get; set; } = true;
        public TimeSpan ReconnectInterval { get; set; } = TimeSpan.FromSeconds(5);
        public int MaxReconnectTries { get; set; } = 3;
        
        public string UserAgent { get; set; } = "QwebSDK-CSharp/1.0";
        public bool Debug { get; set; } = false;
    }

    /// <summary>
    /// TLS客户端配置
    /// </summary>
    public class TlsClientConfig
    {
        public string ServerName { get; set; }
        public bool InsecureSkipVerify { get; set; } = false;
        public string CertFile { get; set; }
        public string KeyFile { get; set; }
        public string CAFile { get; set; }
    }

    /// <summary>
    /// 连接信息
    /// </summary>
    public class ConnectionInfo
    {
        public string ServerAddress { get; set; }
        public string LocalAddress { get; set; }
        public DateTime ConnectedAt { get; set; }
        public DateTime LastActivity { get; set; }
        public string TlsVersion { get; set; }
        public string CipherSuite { get; set; }
    }

    /// <summary>
    /// 认证结果
    /// </summary>
    public class AuthResult
    {
        public bool Success { get; set; }
        public string SessionToken { get; set; }
        public DateTime ExpiresAt { get; set; }
        public UserInfo UserInfo { get; set; }
        public string Message { get; set; }
        public string ErrorCode { get; set; }
    }

    /// <summary>
    /// 用户信息
    /// </summary>
    public class UserInfo
    {
        public uint UserId { get; set; }
        public string Username { get; set; }
        public int Level { get; set; }
        public uint? AgentId { get; set; }
    }

    /// <summary>
    /// 状态结果
    /// </summary>
    public class StatusResult
    {
        public bool IsAuthenticated { get; set; }
        public bool SessionValid { get; set; }
        public DateTime ExpiresAt { get; set; }
        public DateTime ServerTime { get; set; }
        public UserInfo UserInfo { get; set; }
    }

    /// <summary>
    /// 会话信息
    /// </summary>
    public class SessionInfo
    {
        public string SessionToken { get; set; }
        public uint UserId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public DateTime LastUsed { get; set; }
    }

    /// <summary>
    /// 客户端状态
    /// </summary>
    public enum ClientState
    {
        Disconnected,
        Connecting,
        Connected,
        Authenticating,
        Authenticated,
        Error
    }

    /// <summary>
    /// 消息类型
    /// </summary>
    public static class MessageTypes
    {
        public const string Auth = "auth";
        public const string AuthResponse = "auth_response";
        public const string Heartbeat = "heartbeat";
        public const string HeartbeatResponse = "heartbeat_response";
        public const string Status = "status";
        public const string StatusResponse = "status_response";
        public const string Logout = "logout";
        public const string LogoutResponse = "logout_response";
        public const string Error = "error";
        public const string Disconnect = "disconnect";
    }

    /// <summary>
    /// 消息
    /// </summary>
    public class Message
    {
        public string Type { get; set; }
        public object Data { get; set; }
        public string Id { get; set; }
        public long Timestamp { get; set; }
    }

    /// <summary>
    /// 认证请求
    /// </summary>
    public class AuthRequest
    {
        public string Type { get; set; }
        public long Timestamp { get; set; }
        public string Nonce { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public string CardNumber { get; set; }
        public string CardPassword { get; set; }
        public string Signature { get; set; }
    }

    // 事件参数类
    public class ConnectionEventArgs : EventArgs
    {
        public ConnectionInfo ConnectionInfo { get; set; }
    }

    public class DisconnectionEventArgs : EventArgs
    {
        public string Reason { get; set; }
    }

    public class AuthEventArgs : EventArgs
    {
        public AuthResult AuthResult { get; set; }
        public string Reason { get; set; }
        public string ErrorCode { get; set; }
    }

    public class ErrorEventArgs : EventArgs
    {
        public Exception Exception { get; set; }
    }

    public class MessageEventArgs : EventArgs
    {
        public string MessageType { get; set; }
        public object Data { get; set; }
    }

    /// <summary>
    /// C# 验证客户端实现
    /// </summary>
    public class VerificationClient : IVerificationClient, IDisposable
    {
        private readonly ClientConfig _config;
        private TcpClient _tcpClient;
        private SslStream _sslStream;
        private ClientState _state = ClientState.Disconnected;
        private ConnectionInfo _connectionInfo;
        private SessionInfo _sessionInfo;
        private Exception _lastError;
        private Timer _heartbeatTimer;
        private CancellationTokenSource _cancellationTokenSource;
        private readonly object _lockObject = new object();

        public event EventHandler<ConnectionEventArgs> Connected;
        public event EventHandler<DisconnectionEventArgs> Disconnected;
        public event EventHandler<AuthEventArgs> AuthSuccess;
        public event EventHandler<AuthEventArgs> AuthFailed;
        public event EventHandler SessionExpired;
        public event EventHandler<ErrorEventArgs> Error;
        public event EventHandler<MessageEventArgs> MessageReceived;

        public bool IsConnected => _state == ClientState.Connected || _state == ClientState.Authenticated;

        public VerificationClient(ClientConfig config = null)
        {
            _config = config ?? new ClientConfig();
            _cancellationTokenSource = new CancellationTokenSource();
        }

        public async Task<bool> ConnectAsync(string serverAddress, ClientConfig config = null, CancellationToken cancellationToken = default)
        {
            lock (_lockObject)
            {
                if (IsConnected)
                    return true;

                if (config != null)
                    _config = config;

                _state = ClientState.Connecting;
            }

            try
            {
                // 解析服务器地址
                var parts = serverAddress.Split(':');
                var host = parts[0];
                var port = int.Parse(parts[1]);

                // 创建TCP连接
                _tcpClient = new TcpClient();
                await _tcpClient.ConnectAsync(host, port);

                // 创建SSL流
                _sslStream = new SslStream(_tcpClient.GetStream(), false, ValidateServerCertificate);
                
                var sslOptions = new SslClientAuthenticationOptions
                {
                    TargetHost = _config.TlsConfig.ServerName ?? host,
                    EnabledSslProtocols = System.Security.Authentication.SslProtocols.Tls12 | System.Security.Authentication.SslProtocols.Tls13
                };

                await _sslStream.AuthenticateAsClientAsync(sslOptions, cancellationToken);

                lock (_lockObject)
                {
                    _state = ClientState.Connected;
                    _connectionInfo = new ConnectionInfo
                    {
                        ServerAddress = serverAddress,
                        LocalAddress = _tcpClient.Client.LocalEndPoint?.ToString(),
                        ConnectedAt = DateTime.UtcNow,
                        LastActivity = DateTime.UtcNow
                    };
                }

                // 启动消息处理循环
                _ = Task.Run(() => MessageLoopAsync(_cancellationTokenSource.Token));

                // 触发连接事件
                Connected?.Invoke(this, new ConnectionEventArgs { ConnectionInfo = _connectionInfo });

                // 启动心跳
                await StartHeartbeatAsync();

                return true;
            }
            catch (Exception ex)
            {
                _lastError = ex;
                _state = ClientState.Error;
                Error?.Invoke(this, new ErrorEventArgs { Exception = ex });
                return false;
            }
        }

        public async Task DisconnectAsync()
        {
            try
            {
                await StopHeartbeatAsync();

                // 发送断开连接消息
                if (IsConnected)
                {
                    var disconnectMsg = new Message
                    {
                        Type = MessageTypes.Disconnect,
                        Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                    };
                    await SendMessageAsync(disconnectMsg);
                }

                _cancellationTokenSource?.Cancel();
                _sslStream?.Close();
                _tcpClient?.Close();

                lock (_lockObject)
                {
                    _state = ClientState.Disconnected;
                }

                Disconnected?.Invoke(this, new DisconnectionEventArgs { Reason = "User initiated disconnect" });
            }
            catch (Exception ex)
            {
                _lastError = ex;
                Error?.Invoke(this, new ErrorEventArgs { Exception = ex });
            }
        }

        public async Task<AuthResult> AuthenticateUserAsync(string username, string password)
        {
            if (!IsConnected)
                throw new InvalidOperationException("Not connected to server");

            _state = ClientState.Authenticating;

            var authRequest = new AuthRequest
            {
                Type = "user_auth",
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                Nonce = GenerateNonce(),
                Username = username,
                Password = password,
                Signature = SignMessage($"{username}{password}") // 简化签名
            };

            var message = new Message
            {
                Type = MessageTypes.Auth,
                Data = authRequest,
                Id = GenerateMessageId(),
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };

            await SendMessageAsync(message);

            // 简化实现：等待响应
            await Task.Delay(1000);

            // 模拟成功响应
            var result = new AuthResult
            {
                Success = true,
                SessionToken = GenerateSessionToken(),
                ExpiresAt = DateTime.UtcNow.AddHours(24),
                Message = "Authentication successful"
            };

            if (result.Success)
            {
                _state = ClientState.Authenticated;
                _sessionInfo = new SessionInfo
                {
                    SessionToken = result.SessionToken,
                    CreatedAt = DateTime.UtcNow,
                    ExpiresAt = result.ExpiresAt
                };
                AuthSuccess?.Invoke(this, new AuthEventArgs { AuthResult = result });
            }
            else
            {
                _state = ClientState.Connected;
                AuthFailed?.Invoke(this, new AuthEventArgs { Reason = result.Message, ErrorCode = result.ErrorCode });
            }

            return result;
        }

        public async Task<AuthResult> AuthenticateCardAsync(string cardNumber, string cardPassword)
        {
            if (!IsConnected)
                throw new InvalidOperationException("Not connected to server");

            _state = ClientState.Authenticating;

            var authRequest = new AuthRequest
            {
                Type = "card_auth",
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                Nonce = GenerateNonce(),
                CardNumber = cardNumber,
                CardPassword = cardPassword,
                Signature = SignMessage($"{cardNumber}{cardPassword}")
            };

            var message = new Message
            {
                Type = MessageTypes.Auth,
                Data = authRequest,
                Id = GenerateMessageId(),
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };

            await SendMessageAsync(message);

            // 简化实现
            var result = new AuthResult
            {
                Success = true,
                Message = "Card authentication successful"
            };

            return result;
        }

        public async Task<StatusResult> CheckAuthStatusAsync()
        {
            if (!IsConnected)
                throw new InvalidOperationException("Not connected to server");

            var message = new Message
            {
                Type = MessageTypes.Status,
                Data = new { },
                Id = GenerateMessageId(),
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };

            await SendMessageAsync(message);

            return new StatusResult
            {
                IsAuthenticated = _state == ClientState.Authenticated,
                SessionValid = _sessionInfo?.ExpiresAt > DateTime.UtcNow,
                ServerTime = DateTime.UtcNow
            };
        }

        public async Task LogoutAsync()
        {
            if (_state != ClientState.Authenticated)
                return;

            var message = new Message
            {
                Type = MessageTypes.Logout,
                Data = new { },
                Id = GenerateMessageId(),
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };

            await SendMessageAsync(message);

            _state = ClientState.Connected;
            _sessionInfo = null;
        }

        public async Task<AuthResult> RefreshSessionAsync()
        {
            if (_sessionInfo == null)
                throw new InvalidOperationException("No active session");

            _sessionInfo.ExpiresAt = DateTime.UtcNow.AddHours(24);

            return new AuthResult
            {
                Success = true,
                SessionToken = _sessionInfo.SessionToken,
                ExpiresAt = _sessionInfo.ExpiresAt,
                Message = "Session refreshed"
            };
        }

        public SessionInfo GetSessionInfo() => _sessionInfo;
        public ConnectionInfo GetConnectionInfo() => _connectionInfo;
        public Exception GetLastError() => _lastError;

        public async Task StartHeartbeatAsync()
        {
            await StopHeartbeatAsync();
            _heartbeatTimer = new Timer(SendHeartbeat, null, TimeSpan.Zero, _config.HeartbeatInterval);
        }

        public async Task StopHeartbeatAsync()
        {
            _heartbeatTimer?.Dispose();
            _heartbeatTimer = null;
        }

        private async void SendHeartbeat(object state)
        {
            if (!IsConnected) return;

            try
            {
                var heartbeat = new Message
                {
                    Type = MessageTypes.Heartbeat,
                    Data = new { Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds() },
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                await SendMessageAsync(heartbeat);
            }
            catch (Exception ex)
            {
                _lastError = ex;
                Error?.Invoke(this, new ErrorEventArgs { Exception = ex });
            }
        }

        private async Task SendMessageAsync(Message message)
        {
            if (_sslStream == null || !_sslStream.CanWrite)
                throw new InvalidOperationException("Stream is not writable");

            var json = JsonSerializer.Serialize(message);
            var buffer = System.Text.Encoding.UTF8.GetBytes(json + "\n");
            
            await _sslStream.WriteAsync(buffer, 0, buffer.Length);
            await _sslStream.FlushAsync();
        }

        private async Task MessageLoopAsync(CancellationToken cancellationToken)
        {
            var buffer = new byte[4096];
            var messageBuffer = new System.Text.StringBuilder();

            try
            {
                while (!cancellationToken.IsCancellationRequested && IsConnected)
                {
                    var bytesRead = await _sslStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                    if (bytesRead == 0)
                        break;

                    var data = System.Text.Encoding.UTF8.GetString(buffer, 0, bytesRead);
                    messageBuffer.Append(data);

                    // 处理完整的消息（以换行符分隔）
                    var messages = messageBuffer.ToString().Split('\n');
                    for (int i = 0; i < messages.Length - 1; i++)
                    {
                        if (!string.IsNullOrWhiteSpace(messages[i]))
                        {
                            await HandleMessageAsync(messages[i]);
                        }
                    }

                    // 保留最后一个不完整的消息
                    messageBuffer.Clear();
                    if (!string.IsNullOrWhiteSpace(messages[messages.Length - 1]))
                    {
                        messageBuffer.Append(messages[messages.Length - 1]);
                    }
                }
            }
            catch (Exception ex)
            {
                _lastError = ex;
                _state = ClientState.Error;
                Error?.Invoke(this, new ErrorEventArgs { Exception = ex });
            }
        }

        private async Task HandleMessageAsync(string messageJson)
        {
            try
            {
                var message = JsonSerializer.Deserialize<Message>(messageJson);
                
                switch (message.Type)
                {
                    case MessageTypes.AuthResponse:
                        // 处理认证响应
                        break;
                    case MessageTypes.HeartbeatResponse:
                        // 处理心跳响应
                        break;
                    case MessageTypes.Error:
                        // 处理错误消息
                        break;
                    default:
                        MessageReceived?.Invoke(this, new MessageEventArgs { MessageType = message.Type, Data = message.Data });
                        break;
                }
            }
            catch (Exception ex)
            {
                _lastError = ex;
                Error?.Invoke(this, new ErrorEventArgs { Exception = ex });
            }
        }

        private bool ValidateServerCertificate(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors)
        {
            if (_config.TlsConfig.InsecureSkipVerify)
                return true;

            return sslPolicyErrors == SslPolicyErrors.None;
        }

        private string GenerateNonce() => Guid.NewGuid().ToString("N");
        private string GenerateMessageId() => $"msg_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}";
        private string GenerateSessionToken() => Guid.NewGuid().ToString("N");
        private string SignMessage(string data) => Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(data));

        public void Dispose()
        {
            _cancellationTokenSource?.Cancel();
            _heartbeatTimer?.Dispose();
            _sslStream?.Dispose();
            _tcpClient?.Dispose();
            _cancellationTokenSource?.Dispose();
        }
    }
}