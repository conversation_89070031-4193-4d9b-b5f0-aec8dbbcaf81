package test

import (
	"net/http/httptest"
	"net/url"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"

	"qweb-verification/internal/database"
	"qweb-verification/internal/monitor"
	"qweb-verification/internal/security"
	"qweb-verification/internal/web"
)

func TestWebSocketBasicConnection(t *testing.T) {
	// 创建测试数据库
	dbConfig := &database.Config{
		Path:         ":memory:",
		MaxIdleConns: 10,
		MaxOpenConns: 100,
	}
	
	db, err := database.NewDatabase(dbConfig)
	if err != nil {
		t.Fatalf("Failed to create database: %v", err)
	}
	defer db.Close()

	// 执行数据库迁移
	err = db.Migrate()
	if err != nil {
		t.Fatalf("Failed to migrate database: %v", err)
	}

	// 创建安全管理器
	secConfig := &security.SecurityConfig{
		EncryptionKey: "test-************************************",
	}
	secMgr, err := security.NewSecurityManager(secConfig)
	if err != nil {
		t.Fatalf("Failed to create security manager: %v", err)
	}

	// 创建指标收集器
	repo := database.NewRepository(db.GetDB())
	metricsCollector := monitor.NewMetricsCollector(repo)

	// 创建Web服务器配置
	webConfig := &web.Config{
		Port:         8080,
		Host:         "localhost",
		StaticPath:   "test/static",
		TemplatePath: "test/templates",
		JWTSecret:    "test-secret",
		CORSEnabled:  true,
		CORSOrigins:  []string{"*"},
	}

	// 创建依赖
	deps := &web.Dependencies{
		Database:         db,
		SecurityManager:  secMgr,
		MetricsCollector: metricsCollector,
	}

	// 创建Web服务器
	webServer, err := web.NewWebServer(webConfig, deps)
	if err != nil {
		t.Fatalf("Failed to create web server: %v", err)
	}

	// 创建测试HTTP服务器
	gin.SetMode(gin.TestMode)
	server := httptest.NewServer(webServer.GetRouter())
	defer server.Close()

	// 将HTTP URL转换为WebSocket URL
	u, err := url.Parse(server.URL)
	if err != nil {
		t.Fatalf("Failed to parse server URL: %v", err)
	}
	u.Scheme = "ws"
	u.Path = "/ws"

	// 连接WebSocket
	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		t.Fatalf("Failed to connect to WebSocket: %v", err)
	}
	defer conn.Close()

	// 读取连接消息
	var connectMsg web.WSMessage
	err = conn.ReadJSON(&connectMsg)
	if err != nil {
		t.Fatalf("Failed to read connect message: %v", err)
	}

	if connectMsg.Type != "connected" {
		t.Errorf("Expected 'connected' message, got: %s", connectMsg.Type)
	}

	// 测试心跳消息
	heartbeatMsg := web.WSMessage{
		Type:      "heartbeat",
		Data:      map[string]interface{}{},
		Timestamp: time.Now().Unix(),
	}

	err = conn.WriteJSON(heartbeatMsg)
	if err != nil {
		t.Fatalf("Failed to send heartbeat: %v", err)
	}

	// 读取心跳响应
	var heartbeatResp web.WSMessage
	err = conn.ReadJSON(&heartbeatResp)
	if err != nil {
		t.Fatalf("Failed to read heartbeat response: %v", err)
	}

	if heartbeatResp.Type != "heartbeat_response" {
		t.Errorf("Expected 'heartbeat_response', got: %s", heartbeatResp.Type)
	}

	t.Log("WebSocket基本功能测试通过")
}