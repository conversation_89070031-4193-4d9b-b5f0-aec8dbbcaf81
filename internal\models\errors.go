package models

import "errors"

// 通用错误定义
var (
	ErrRecordNotFound     = errors.New("record not found")
	ErrDuplicateRecord    = errors.New("duplicate record")
	ErrInvalidCredentials = errors.New("invalid credentials")
	ErrUserDisabled       = errors.New("user is disabled")
	ErrAgentDisabled      = errors.New("agent is disabled")
	ErrCardNotFound       = errors.New("card not found")
	ErrCardAlreadyUsed    = errors.New("card already used")
	ErrCardExpired        = errors.New("card expired")
	ErrSessionExpired     = errors.New("session expired")
	ErrInvalidSession     = errors.New("invalid session")
	ErrIPBanned           = errors.New("ip address is banned")
	ErrTooManyAttempts    = errors.New("too many failed attempts")
	ErrInvalidSignature   = errors.New("invalid message signature")
	ErrInvalidTimestamp   = errors.New("invalid timestamp")
	ErrReplayAttack       = errors.New("replay attack detected")
	ErrInsufficientPermission = errors.New("insufficient permission")
)

// UserStatus 用户状态常量
const (
	UserStatusDisabled = 0 // 禁用
	UserStatusEnabled  = 1 // 启用
)

// AgentStatus 代理商状态常量
const (
	AgentStatusDisabled = 0 // 禁用
	AgentStatusEnabled  = 1 // 启用
)

// AgentLevel 代理商级别常量
const (
	AgentLevelPrimary   = 1 // 一级代理商
	AgentLevelSecondary = 2 // 二级代理商
	AgentLevelTertiary  = 3 // 三级代理商
)

// CommissionType 佣金类型常量
const (
	CommissionTypeDirect   = "direct"   // 直接佣金
	CommissionTypeIndirect = "indirect" // 间接佣金
)

// CommissionStatus 佣金状态常量
const (
	CommissionStatusPending = 0 // 待结算
	CommissionStatusPaid    = 1 // 已结算
)

// OperationType 操作类型常量
const (
	OpTypeLogin        = "login"
	OpTypeLogout       = "logout"
	OpTypeRegister     = "register"
	OpTypeCardUse      = "card_use"
	OpTypeCardGenerate = "card_generate"
	OpTypeUserCreate   = "user_create"
	OpTypeUserUpdate   = "user_update"
	OpTypeUserDelete   = "user_delete"
	OpTypeAgentCreate  = "agent_create"
	OpTypeAgentUpdate  = "agent_update"
	OpTypeAgentDelete  = "agent_delete"
	OpTypeConfigUpdate = "config_update"
)

// ConfigType 配置类型常量
const (
	ConfigTypeString = "string"
	ConfigTypeInt    = "int"
	ConfigTypeBool   = "bool"
	ConfigTypeJSON   = "json"
)

// ConfigCategory 配置分类常量
const (
	ConfigCategorySystem   = "system"
	ConfigCategorySecurity = "security"
	ConfigCategoryAgent    = "agent"
	ConfigCategoryNotify   = "notify"
)