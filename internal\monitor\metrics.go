package monitor

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"time"

	"qweb-verification/internal/database"
	"qweb-verification/internal/logger"

	"go.uber.org/zap"
)

// MetricsCollector 指标收集器接口
type MetricsCollector interface {
	Start(ctx context.Context) error
	Stop() error
	GetMetrics() *SystemMetrics
	RegisterAlert(alert Alert) error
	CheckAlerts() []AlertEvent
}

// SystemMetrics 系统指标
type SystemMetrics struct {
	Timestamp     time.Time          `json:"timestamp"`
	CPU           *CPUMetrics        `json:"cpu"`
	Memory        *MemoryMetrics     `json:"memory"`
	Database      *DatabaseMetrics   `json:"database"`
	Network       *NetworkMetrics    `json:"network"`
	Application   *AppMetrics        `json:"application"`
	CustomMetrics map[string]float64 `json:"custom_metrics"`
}

// CPUMetrics CPU指标
type CPUMetrics struct {
	UsagePercent float64 `json:"usage_percent"`
	LoadAverage  float64 `json:"load_average"`
	Processes    int     `json:"processes"`
	Goroutines   int     `json:"goroutines"`
}

// MemoryMetrics 内存指标
type MemoryMetrics struct {
	TotalMB      uint64  `json:"total_mb"`
	UsedMB       uint64  `json:"used_mb"`
	FreeMB       uint64  `json:"free_mb"`
	UsagePercent float64 `json:"usage_percent"`
	HeapMB       uint64  `json:"heap_mb"`
	StackMB      uint64  `json:"stack_mb"`
	GCCount      uint32  `json:"gc_count"`
}

// DatabaseMetrics 数据库指标
type DatabaseMetrics struct {
	ConnectionsActive int   `json:"connections_active"`
	ConnectionsIdle   int   `json:"connections_idle"`
	QueryCount        int64 `json:"query_count"`
	SlowQueryCount    int64 `json:"slow_query_count"`
	ErrorCount        int64 `json:"error_count"`
}

// NetworkMetrics 网络指标
type NetworkMetrics struct {
	ActiveConnections int   `json:"active_connections"`
	TotalConnections  int64 `json:"total_connections"`
	BytesReceived     int64 `json:"bytes_received"`
	BytesSent         int64 `json:"bytes_sent"`
	PacketsReceived   int64 `json:"packets_received"`
	PacketsSent       int64 `json:"packets_sent"`
	ErrorCount        int64 `json:"error_count"`
}

// AppMetrics 应用指标
type AppMetrics struct {
	Uptime       time.Duration `json:"uptime"`
	ActiveUsers  int64         `json:"active_users"`
	TotalUsers   int64         `json:"total_users"`
	TotalAgents  int64         `json:"total_agents"`
	TotalCards   int64         `json:"total_cards"`
	OnlineUsers  int64         `json:"online_users"`
	RequestCount int64         `json:"request_count"`
	ResponseTime float64       `json:"response_time_ms"`
	ErrorRate    float64       `json:"error_rate"`
}

// Alert 告警配置
type Alert struct {
	ID          string        `json:"id"`
	Name        string        `json:"name"`
	Description string        `json:"description"`
	Metric      string        `json:"metric"`    // 监控的指标名称
	Operator    string        `json:"operator"`  // gt, lt, eq, gte, lte
	Threshold   float64       `json:"threshold"` // 阈值
	Duration    time.Duration `json:"duration"`  // 持续时间
	Severity    string        `json:"severity"`  // low, medium, high, critical
	Enabled     bool          `json:"enabled"`
	Actions     []AlertAction `json:"actions"` // 告警动作
}

// AlertAction 告警动作
type AlertAction struct {
	Type   string                 `json:"type"`   // email, webhook, log
	Config map[string]interface{} `json:"config"` // 动作配置
}

// AlertEvent 告警事件
type AlertEvent struct {
	AlertID    string     `json:"alert_id"`
	AlertName  string     `json:"alert_name"`
	Metric     string     `json:"metric"`
	Value      float64    `json:"value"`
	Threshold  float64    `json:"threshold"`
	Severity   string     `json:"severity"`
	Message    string     `json:"message"`
	Timestamp  time.Time  `json:"timestamp"`
	Resolved   bool       `json:"resolved"`
	ResolvedAt *time.Time `json:"resolved_at,omitempty"`
}

// DefaultMetricsCollector 默认指标收集器实现
type DefaultMetricsCollector struct {
	repo            database.Repository
	metrics         *SystemMetrics
	alerts          map[string]Alert
	alertHistory    map[string]AlertEvent
	alertStates     map[string]time.Time // 告警状态持续时间
	ticker          *time.Ticker
	stopCh          chan struct{}
	startTime       time.Time
	mutex           sync.RWMutex
	customMetrics   map[string]float64
	networkCounters *NetworkCounters
}

// NetworkCounters 网络计数器
type NetworkCounters struct {
	TotalConnections int64
	BytesReceived    int64
	BytesSent        int64
	PacketsReceived  int64
	PacketsSent      int64
	ErrorCount       int64
	mutex            sync.RWMutex
}

// NewMetricsCollector 创建指标收集器
func NewMetricsCollector(repo database.Repository) MetricsCollector {
	return &DefaultMetricsCollector{
		repo:            repo,
		alerts:          make(map[string]Alert),
		alertHistory:    make(map[string]AlertEvent),
		alertStates:     make(map[string]time.Time),
		stopCh:          make(chan struct{}),
		startTime:       time.Now(),
		customMetrics:   make(map[string]float64),
		networkCounters: &NetworkCounters{},
	}
}

// Start 启动指标收集
func (mc *DefaultMetricsCollector) Start(ctx context.Context) error {
	logger.Info("Starting metrics collector")

	mc.ticker = time.NewTicker(30 * time.Second) // 每30秒收集一次指标

	// 注册默认告警
	mc.registerDefaultAlerts()

	go mc.collectLoop(ctx)
	go mc.alertLoop(ctx)

	return nil
}

// Stop 停止指标收集
func (mc *DefaultMetricsCollector) Stop() error {
	logger.Info("Stopping metrics collector")

	if mc.ticker != nil {
		mc.ticker.Stop()
	}

	close(mc.stopCh)
	return nil
}

// collectLoop 收集循环
func (mc *DefaultMetricsCollector) collectLoop(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-mc.stopCh:
			return
		case <-mc.ticker.C:
			mc.collectMetrics()
		}
	}
}

// alertLoop 告警检查循环
func (mc *DefaultMetricsCollector) alertLoop(ctx context.Context) {
	alertTicker := time.NewTicker(10 * time.Second) // 每10秒检查一次告警
	defer alertTicker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-mc.stopCh:
			return
		case <-alertTicker.C:
			mc.checkAndTriggerAlerts()
		}
	}
}

// collectMetrics 收集指标
func (mc *DefaultMetricsCollector) collectMetrics() {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	metrics := &SystemMetrics{
		Timestamp:     time.Now(),
		CPU:           mc.collectCPUMetrics(),
		Memory:        mc.collectMemoryMetrics(),
		Database:      mc.collectDatabaseMetrics(),
		Network:       mc.collectNetworkMetrics(),
		Application:   mc.collectAppMetrics(),
		CustomMetrics: mc.copyCustomMetrics(),
	}

	mc.metrics = metrics

	logger.Debug("Metrics collected",
		zap.Float64("cpu_usage", metrics.CPU.UsagePercent),
		zap.Float64("memory_usage", metrics.Memory.UsagePercent),
		zap.Int("active_connections", metrics.Network.ActiveConnections),
		zap.Int64("online_users", metrics.Application.OnlineUsers),
	)
}

// collectCPUMetrics 收集CPU指标
func (mc *DefaultMetricsCollector) collectCPUMetrics() *CPUMetrics {
	// 简化实现，实际项目中应该使用系统调用获取真实的CPU指标
	return &CPUMetrics{
		UsagePercent: 0.0, // 需要实现CPU使用率计算
		LoadAverage:  0.0, // 需要实现负载平均值计算
		Processes:    0,   // 需要实现进程数计算
		Goroutines:   runtime.NumGoroutine(),
	}
}

// collectMemoryMetrics 收集内存指标
func (mc *DefaultMetricsCollector) collectMemoryMetrics() *MemoryMetrics {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return &MemoryMetrics{
		TotalMB:      0, // 需要实现系统总内存获取
		UsedMB:       0, // 需要实现已用内存计算
		FreeMB:       0, // 需要实现空闲内存计算
		UsagePercent: 0, // 需要实现内存使用率计算
		HeapMB:       m.HeapAlloc / 1024 / 1024,
		StackMB:      m.StackInuse / 1024 / 1024,
		GCCount:      m.NumGC,
	}
}

// collectDatabaseMetrics 收集数据库指标
func (mc *DefaultMetricsCollector) collectDatabaseMetrics() *DatabaseMetrics {
	// 使用Repository接口的方法获取统计信息
	userCount, _ := mc.repo.GetUserCount()
	agentCount, _ := mc.repo.GetAgentCount()
	cardCount, _ := mc.repo.GetCardCount()

	return &DatabaseMetrics{
		ConnectionsActive: 10,                                        // 默认值，需要实际实现
		ConnectionsIdle:   2,                                         // 默认值
		QueryCount:        int64(userCount + agentCount + cardCount), // 使用查询统计作为代理
		SlowQueryCount:    0,                                         // 需要实际实现
		ErrorCount:        0,                                         // 需要实际实现
	}
}

// collectNetworkMetrics 收集网络指标
func (mc *DefaultMetricsCollector) collectNetworkMetrics() *NetworkMetrics {
	mc.networkCounters.mutex.RLock()
	defer mc.networkCounters.mutex.RUnlock()

	return &NetworkMetrics{
		ActiveConnections: 0, // 需要从TCP服务器获取
		TotalConnections:  mc.networkCounters.TotalConnections,
		BytesReceived:     mc.networkCounters.BytesReceived,
		BytesSent:         mc.networkCounters.BytesSent,
		PacketsReceived:   mc.networkCounters.PacketsReceived,
		PacketsSent:       mc.networkCounters.PacketsSent,
		ErrorCount:        mc.networkCounters.ErrorCount,
	}
}

// collectAppMetrics 收集应用指标
func (mc *DefaultMetricsCollector) collectAppMetrics() *AppMetrics {
	// 从数据库获取统计信息
	userCount, _ := mc.repo.GetUserCount()
	agentCount, _ := mc.repo.GetAgentCount()
	cardCount, _ := mc.repo.GetCardCount()
	onlineUserCount, _ := mc.repo.GetOnlineUserCount()

	return &AppMetrics{
		Uptime:       time.Since(mc.startTime),
		ActiveUsers:  userCount,
		TotalUsers:   userCount,
		TotalAgents:  agentCount,
		TotalCards:   cardCount,
		OnlineUsers:  onlineUserCount,
		RequestCount: 0,   // 需要实现请求计数
		ResponseTime: 0.0, // 需要实现响应时间计算
		ErrorRate:    0.0, // 需要实现错误率计算
	}
}

// copyCustomMetrics 复制自定义指标
func (mc *DefaultMetricsCollector) copyCustomMetrics() map[string]float64 {
	metrics := make(map[string]float64)
	for k, v := range mc.customMetrics {
		metrics[k] = v
	}
	return metrics
}

// GetMetrics 获取当前指标
func (mc *DefaultMetricsCollector) GetMetrics() *SystemMetrics {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()

	if mc.metrics == nil {
		mc.collectMetrics()
	}

	return mc.metrics
}

// RegisterAlert 注册告警
func (mc *DefaultMetricsCollector) RegisterAlert(alert Alert) error {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	mc.alerts[alert.ID] = alert
	logger.Info("Alert registered", zap.String("alert_id", alert.ID), zap.String("alert_name", alert.Name))

	return nil
}

// CheckAlerts 检查告警
func (mc *DefaultMetricsCollector) CheckAlerts() []AlertEvent {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()

	var events []AlertEvent

	for _, alert := range mc.alerts {
		if !alert.Enabled {
			continue
		}

		value := mc.getMetricValue(alert.Metric)
		if mc.checkAlertCondition(alert, value) {
			event := AlertEvent{
				AlertID:   alert.ID,
				AlertName: alert.Name,
				Metric:    alert.Metric,
				Value:     value,
				Threshold: alert.Threshold,
				Severity:  alert.Severity,
				Message:   fmt.Sprintf("Metric %s %s %.2f (threshold: %.2f)", alert.Metric, alert.Operator, value, alert.Threshold),
				Timestamp: time.Now(),
				Resolved:  false,
			}

			events = append(events, event)
		}
	}

	return events
}

// checkAndTriggerAlerts 检查并触发告警
func (mc *DefaultMetricsCollector) checkAndTriggerAlerts() {
	events := mc.CheckAlerts()

	for _, event := range events {
		// 检查告警状态持续时间
		stateKey := event.AlertID
		if startTime, exists := mc.alertStates[stateKey]; exists {
			if time.Since(startTime) >= mc.alerts[event.AlertID].Duration {
				// 触发告警
				mc.triggerAlert(event)
				delete(mc.alertStates, stateKey) // 重置状态
			}
		} else {
			// 记录告警开始时间
			mc.alertStates[stateKey] = time.Now()
		}
	}
}

// triggerAlert 触发告警
func (mc *DefaultMetricsCollector) triggerAlert(event AlertEvent) {
	logger.Warn("Alert triggered",
		zap.String("alert_id", event.AlertID),
		zap.String("alert_name", event.AlertName),
		zap.String("metric", event.Metric),
		zap.Float64("value", event.Value),
		zap.Float64("threshold", event.Threshold),
		zap.String("severity", event.Severity),
	)

	// 执行告警动作
	alert := mc.alerts[event.AlertID]
	for _, action := range alert.Actions {
		mc.executeAlertAction(action, event)
	}

	// 记录告警历史
	mc.alertHistory[event.AlertID] = event
}

// executeAlertAction 执行告警动作
func (mc *DefaultMetricsCollector) executeAlertAction(action AlertAction, event AlertEvent) {
	switch action.Type {
	case "log":
		logger.Error("ALERT: "+event.Message,
			zap.String("alert_id", event.AlertID),
			zap.String("severity", event.Severity),
		)
	case "email":
		// 实现邮件告警
		logger.Info("Email alert action executed", zap.String("alert_id", event.AlertID))
	case "webhook":
		// 实现Webhook告警
		logger.Info("Webhook alert action executed", zap.String("alert_id", event.AlertID))
	default:
		logger.Warn("Unknown alert action type", zap.String("type", action.Type))
	}
}

// getMetricValue 获取指标值
func (mc *DefaultMetricsCollector) getMetricValue(metric string) float64 {
	if mc.metrics == nil {
		return 0
	}

	switch metric {
	case "cpu.usage_percent":
		return mc.metrics.CPU.UsagePercent
	case "memory.usage_percent":
		return mc.metrics.Memory.UsagePercent
	case "network.active_connections":
		return float64(mc.metrics.Network.ActiveConnections)
	case "app.online_users":
		return float64(mc.metrics.Application.OnlineUsers)
	case "app.error_rate":
		return mc.metrics.Application.ErrorRate
	default:
		if value, exists := mc.metrics.CustomMetrics[metric]; exists {
			return value
		}
		return 0
	}
}

// checkAlertCondition 检查告警条件
func (mc *DefaultMetricsCollector) checkAlertCondition(alert Alert, value float64) bool {
	switch alert.Operator {
	case "gt":
		return value > alert.Threshold
	case "gte":
		return value >= alert.Threshold
	case "lt":
		return value < alert.Threshold
	case "lte":
		return value <= alert.Threshold
	case "eq":
		return value == alert.Threshold
	default:
		return false
	}
}

// registerDefaultAlerts 注册默认告警
func (mc *DefaultMetricsCollector) registerDefaultAlerts() {
	defaultAlerts := []Alert{
		{
			ID:          "high_cpu_usage",
			Name:        "High CPU Usage",
			Description: "CPU usage is too high",
			Metric:      "cpu.usage_percent",
			Operator:    "gt",
			Threshold:   80.0,
			Duration:    2 * time.Minute,
			Severity:    "high",
			Enabled:     true,
			Actions: []AlertAction{
				{Type: "log", Config: map[string]interface{}{}},
			},
		},
		{
			ID:          "high_memory_usage",
			Name:        "High Memory Usage",
			Description: "Memory usage is too high",
			Metric:      "memory.usage_percent",
			Operator:    "gt",
			Threshold:   85.0,
			Duration:    2 * time.Minute,
			Severity:    "high",
			Enabled:     true,
			Actions: []AlertAction{
				{Type: "log", Config: map[string]interface{}{}},
			},
		},
		{
			ID:          "high_connection_count",
			Name:        "High Connection Count",
			Description: "Too many active connections",
			Metric:      "network.active_connections",
			Operator:    "gt",
			Threshold:   1000.0,
			Duration:    1 * time.Minute,
			Severity:    "medium",
			Enabled:     true,
			Actions: []AlertAction{
				{Type: "log", Config: map[string]interface{}{}},
			},
		},
	}

	for _, alert := range defaultAlerts {
		mc.RegisterAlert(alert)
	}
}

// UpdateCustomMetric 更新自定义指标
func (mc *DefaultMetricsCollector) UpdateCustomMetric(name string, value float64) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	mc.customMetrics[name] = value
}

// IncrementNetworkCounter 增加网络计数器
func (mc *DefaultMetricsCollector) IncrementNetworkCounter(counter string, value int64) {
	mc.networkCounters.mutex.Lock()
	defer mc.networkCounters.mutex.Unlock()

	switch counter {
	case "total_connections":
		mc.networkCounters.TotalConnections += value
	case "bytes_received":
		mc.networkCounters.BytesReceived += value
	case "bytes_sent":
		mc.networkCounters.BytesSent += value
	case "packets_received":
		mc.networkCounters.PacketsReceived += value
	case "packets_sent":
		mc.networkCounters.PacketsSent += value
	case "error_count":
		mc.networkCounters.ErrorCount += value
	}
}
