package web

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"qweb-verification/internal/database"
	"qweb-verification/internal/logger"
	"qweb-verification/internal/models"
)

// APIV1Handler API V1处理器
type APIV1Handler struct {
	deps      *Dependencies
	adminAuth *AdminAuthService
	repo      database.Repository
	wsManager *WebSocketManager
}

// NewAPIV1Handler 创建API V1处理器
func NewAPIV1Handler(deps *Dependencies, adminAuth *AdminAuthService, wsManager *WebSocketManager) *APIV1Handler {
	return &APIV1Handler{
		deps:      deps,
		adminAuth: adminAuth,
		repo:      database.NewRepository(deps.Database.GetDB()),
		wsManager: wsManager,
	}
}

// 管理员认证相关API

// AdminLogin 管理员登录
func (h *APIV1Handler) AdminLogin(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid request format",
		})
		return
	}

	// 记录登录尝试
	logger.Info("Admin login attempt",
		zap.String("username", req.Username),
		zap.String("ip", c.ClientIP()),
	)

	resp, err := h.adminAuth.Login(req.Username, req.Password, c.ClientIP())
	if err != nil {
		logger.Warn("Admin login failed",
			zap.String("username", req.Username),
			zap.String("ip", c.ClientIP()),
			zap.Error(err),
		)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   true,
			"message": "Invalid credentials",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    resp,
	})
}

// AdminLogout 管理员登出
func (h *APIV1Handler) AdminLogout(c *gin.Context) {
	adminID := c.GetUint("admin_id")
	username := c.GetString("admin_username")

	h.adminAuth.Logout(adminID, username)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Logged out successfully",
	})
}

// GetAdminProfile 获取管理员信息
func (h *APIV1Handler) GetAdminProfile(c *gin.Context) {
	adminID := c.GetUint("admin_id")

	info, err := h.adminAuth.GetAdminInfo(adminID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": "Failed to get admin info",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    info,
	})
}

// 用户管理API

// ListUsers 获取用户列表
func (h *APIV1Handler) ListUsers(c *gin.Context) {
	page, size, offset := GetPaginationParams(c)
	filters := GetFilterParams(c)

	users, total, err := h.repo.ListUsers(offset, size, filters)
	if err != nil {
		logger.Error("Failed to list users", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": "Failed to get users",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    users,
		"pagination": gin.H{
			"total": total,
			"page":  page,
			"size":  size,
			"pages": (total + int64(size) - 1) / int64(size),
		},
	})
}

// CreateUser 创建用户
func (h *APIV1Handler) CreateUser(c *gin.Context) {
	var req struct {
		Username string `json:"username" binding:"required"`
		Password string `json:"password" binding:"required"`
		AgentID  *uint  `json:"agent_id"`
		Status   int    `json:"status"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid request format",
		})
		return
	}

	// 生成密码哈希
	encryptor := h.deps.SecurityManager.GetEncryptor()
	salt := encryptor.GenerateSalt()
	passwordHash := encryptor.HashPassword(req.Password, salt)

	user := &models.User{
		Username:     req.Username,
		PasswordHash: passwordHash,
		Salt:        salt,
		Status:      req.Status,
		AgentID:     req.AgentID,
	}

	if err := h.repo.CreateUser(user); err != nil {
		logger.Error("Failed to create user", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": "Failed to create user",
		})
		return
	}

	// 记录操作日志
	h.logOperation(c, models.OpTypeUserCreate, "Created user: "+req.Username, true, "")

	// 发送WebSocket事件通知
	if h.wsManager != nil {
		h.wsManager.PushSystemEvent("user_created", map[string]interface{}{
			"user_id":  user.ID,
			"username": user.Username,
			"agent_id": user.AgentID,
			"status":   user.Status,
			"created_by": c.GetString("admin_username"),
		})
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    user,
	})
}

// GetUser 获取用户详情
func (h *APIV1Handler) GetUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid user ID",
		})
		return
	}

	user, err := h.repo.GetUserByID(uint(id))
	if err != nil {
		logger.Error("Failed to get user", zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{
			"error":   true,
			"message": "User not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    user,
	})
}

// UpdateUser 更新用户
func (h *APIV1Handler) UpdateUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid user ID",
		})
		return
	}

	user, err := h.repo.GetUserByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   true,
			"message": "User not found",
		})
		return
	}

	var req struct {
		Username string `json:"username"`
		Password string `json:"password"`
		AgentID  *uint  `json:"agent_id"`
		Status   *int   `json:"status"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid request format",
		})
		return
	}

	// 更新字段
	if req.Username != "" {
		user.Username = req.Username
	}
	
	if req.Password != "" {
		encryptor := h.deps.SecurityManager.GetEncryptor()
		salt := encryptor.GenerateSalt()
		user.PasswordHash = encryptor.HashPassword(req.Password, salt)
		user.Salt = salt
	}
	
	if req.AgentID != nil {
		user.AgentID = req.AgentID
	}
	
	if req.Status != nil {
		user.Status = *req.Status
	}

	if err := h.repo.UpdateUser(user); err != nil {
		logger.Error("Failed to update user", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": "Failed to update user",
		})
		return
	}

	h.logOperation(c, models.OpTypeUserUpdate, "Updated user: "+user.Username, true, "")

	// 发送WebSocket事件通知
	if h.wsManager != nil {
		h.wsManager.PushSystemEvent("user_updated", map[string]interface{}{
			"user_id":    user.ID,
			"username":   user.Username,
			"agent_id":   user.AgentID,
			"status":     user.Status,
			"updated_by": c.GetString("admin_username"),
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    user,
	})
}

// DeleteUser 删除用户
func (h *APIV1Handler) DeleteUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid user ID",
		})
		return
	}

	user, err := h.repo.GetUserByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   true,
			"message": "User not found",
		})
		return
	}

	if err := h.repo.DeleteUser(uint(id)); err != nil {
		logger.Error("Failed to delete user", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": "Failed to delete user",
		})
		return
	}

	h.logOperation(c, models.OpTypeUserDelete, "Deleted user: "+user.Username, true, "")

	// 发送WebSocket事件通知
	if h.wsManager != nil {
		h.wsManager.PushSystemEvent("user_deleted", map[string]interface{}{
			"user_id":    user.ID,
			"username":   user.Username,
			"deleted_by": c.GetString("admin_username"),
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "User deleted successfully",
	})
}

// 代理商管理API

// ListAgents 获取代理商列表
func (h *APIV1Handler) ListAgents(c *gin.Context) {
	page, size, offset := GetPaginationParams(c)
	filters := GetFilterParams(c)

	agents, total, err := h.repo.ListAgents(offset, size, filters)
	if err != nil {
		logger.Error("Failed to list agents", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": "Failed to get agents",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    agents,
		"pagination": gin.H{
			"total": total,
			"page":  page,
			"size":  size,
			"pages": (total + int64(size) - 1) / int64(size),
		},
	})
}

// CreateAgent 创建代理商
func (h *APIV1Handler) CreateAgent(c *gin.Context) {
	var req struct {
		Username       string   `json:"username" binding:"required"`
		Password       string   `json:"password" binding:"required"`
		ParentID       *uint    `json:"parent_id"`
		Level          int      `json:"level" binding:"required"`
		CommissionRate float64  `json:"commission_rate"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid request format",
		})
		return
	}

	agent, err := h.deps.AgentService.CreateAgent(req.ParentID, req.Username, req.Password, req.Level, req.CommissionRate)
	if err != nil {
		logger.Error("Failed to create agent", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": err.Error(),
		})
		return
	}

	h.logOperation(c, models.OpTypeAgentCreate, "Created agent: "+req.Username, true, "")

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    agent,
	})
}

// GetAgent 获取代理商详情
func (h *APIV1Handler) GetAgent(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid agent ID",
		})
		return
	}

	agent, err := h.deps.AgentService.GetAgent(uint(id))
	if err != nil {
		logger.Error("Failed to get agent", zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{
			"error":   true,
			"message": "Agent not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    agent,
	})
}

// UpdateAgent 更新代理商
func (h *APIV1Handler) UpdateAgent(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid agent ID",
		})
		return
	}

	agent, err := h.deps.AgentService.GetAgent(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   true,
			"message": "Agent not found",
		})
		return
	}

	var req struct {
		Username       string   `json:"username"`
		CommissionRate *float64 `json:"commission_rate"`
		Status         *int     `json:"status"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid request format",
		})
		return
	}

	// 更新字段
	if req.Username != "" {
		agent.Username = req.Username
	}
	if req.CommissionRate != nil {
		agent.CommissionRate = *req.CommissionRate
	}
	if req.Status != nil {
		agent.Status = *req.Status
	}

	if err := h.deps.AgentService.UpdateAgent(agent); err != nil {
		logger.Error("Failed to update agent", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": "Failed to update agent",
		})
		return
	}

	h.logOperation(c, models.OpTypeAgentUpdate, "Updated agent: "+agent.Username, true, "")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    agent,
	})
}

// DeleteAgent 删除代理商
func (h *APIV1Handler) DeleteAgent(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid agent ID",
		})
		return
	}

	agent, err := h.deps.AgentService.GetAgent(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   true,
			"message": "Agent not found",
		})
		return
	}

	if err := h.deps.AgentService.DeleteAgent(uint(id)); err != nil {
		logger.Error("Failed to delete agent", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": err.Error(),
		})
		return
	}

	h.logOperation(c, models.OpTypeAgentDelete, "Deleted agent: "+agent.Username, true, "")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Agent deleted successfully",
	})
}

// GetAgentStats 获取代理商统计
func (h *APIV1Handler) GetAgentStats(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid agent ID",
		})
		return
	}

	// 获取时间范围参数
	startTime := time.Now().AddDate(0, -1, 0) // 默认一个月
	endTime := time.Now()

	if startStr := c.Query("start_time"); startStr != "" {
		if t, err := time.Parse("2006-01-02", startStr); err == nil {
			startTime = t
		}
	}

	if endStr := c.Query("end_time"); endStr != "" {
		if t, err := time.Parse("2006-01-02", endStr); err == nil {
			endTime = t
		}
	}

	stats, err := h.deps.AgentService.GetAgentStats(uint(id), startTime, endTime)
	if err != nil {
		logger.Error("Failed to get agent stats", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": "Failed to get agent stats",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// GetAgentCommissions 获取代理商佣金记录
func (h *APIV1Handler) GetAgentCommissions(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid agent ID",
		})
		return
	}

	page, size, offset := GetPaginationParams(c)

	commissions, total, err := h.deps.AgentService.GetAgentCommissions(uint(id), offset, size)
	if err != nil {
		logger.Error("Failed to get agent commissions", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": "Failed to get commissions",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    commissions,
		"pagination": gin.H{
			"total": total,
			"page":  page,
			"size":  size,
			"pages": (total + int64(size) - 1) / int64(size),
		},
	})
}

// 工具方法

// logOperation 记录操作日志
func (h *APIV1Handler) logOperation(c *gin.Context, operation, details string, success bool, errorMsg string) {
	adminID := c.GetUint("admin_id")
	
	log := &models.OperationLog{
		UserID:    nil, // 管理员操作不关联用户
		AgentID:   nil,
		Operation: operation,
		Details:   details,
		IPAddress: c.ClientIP(),
		UserAgent: c.GetHeader("User-Agent"),
		Success:   success,
		ErrorMsg:  errorMsg,
	}

	// 如果是管理员操作，可以在详情中记录管理员ID
	if adminID > 0 {
		log.Details = fmt.Sprintf("Admin %d: %s", adminID, details)
	}

	h.repo.CreateOperationLog(log)
}

