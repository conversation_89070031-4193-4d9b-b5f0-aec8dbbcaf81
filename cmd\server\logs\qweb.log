{"level":"warn","timestamp":"2025-09-01T18:27:41.502+0800","caller":"logger/logger.go:194","msg":"Using default encryption key, please change it in production"}
{"level":"warn","timestamp":"2025-09-01T18:27:41.524+0800","caller":"logger/logger.go:194","msg":"Using default secret key, please change it in production"}
{"level":"warn","timestamp":"2025-09-01T18:27:41.525+0800","caller":"logger/logger.go:194","msg":"Using default JWT secret, please change it in production"}
{"level":"info","timestamp":"2025-09-01T18:27:41.527+0800","caller":"logger/logger.go:190","msg":"Starting application","name":"Qweb网络验证系统","version":"1.0.0","config":"configs/config.yaml"}
{"level":"info","timestamp":"2025-09-01T18:27:41.527+0800","caller":"logger/logger.go:190","msg":"Starting application components..."}
{"level":"info","timestamp":"2025-09-01T18:27:41.579+0800","caller":"logger/logger.go:190","msg":"Database initialized","path":"data\\verification.db"}
{"level":"info","timestamp":"2025-09-01T18:27:41.580+0800","caller":"logger/logger.go:190","msg":"Security manager initialized"}
{"level":"info","timestamp":"2025-09-01T18:27:41.580+0800","caller":"logger/logger.go:190","msg":"Authentication service initialized"}
{"level":"info","timestamp":"2025-09-01T18:27:41.580+0800","caller":"logger/logger.go:190","msg":"Agent service initialized"}
{"level":"info","timestamp":"2025-09-01T18:27:41.581+0800","caller":"logger/logger.go:190","msg":"Starting metrics collector"}
{"level":"info","timestamp":"2025-09-01T18:27:41.582+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_cpu_usage","alert_name":"High CPU Usage"}
{"level":"info","timestamp":"2025-09-01T18:27:41.582+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_memory_usage","alert_name":"High Memory Usage"}
{"level":"info","timestamp":"2025-09-01T18:27:41.582+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_connection_count","alert_name":"High Connection Count"}
{"level":"info","timestamp":"2025-09-01T18:27:41.582+0800","caller":"logger/logger.go:190","msg":"Metrics collector started"}
{"level":"warn","timestamp":"2025-09-01T18:32:19.445+0800","caller":"logger/logger.go:194","msg":"Using default encryption key, please change it in production"}
{"level":"warn","timestamp":"2025-09-01T18:32:19.468+0800","caller":"logger/logger.go:194","msg":"Using default secret key, please change it in production"}
{"level":"warn","timestamp":"2025-09-01T18:32:19.468+0800","caller":"logger/logger.go:194","msg":"Using default JWT secret, please change it in production"}
{"level":"info","timestamp":"2025-09-01T18:32:19.469+0800","caller":"logger/logger.go:190","msg":"Starting application","name":"Qweb网络验证系统","version":"1.0.0","config":"configs/config.yaml"}
{"level":"info","timestamp":"2025-09-01T18:32:19.469+0800","caller":"logger/logger.go:190","msg":"Starting application components..."}
{"level":"info","timestamp":"2025-09-01T18:32:19.521+0800","caller":"logger/logger.go:190","msg":"Database initialized","path":"data\\verification.db"}
{"level":"info","timestamp":"2025-09-01T18:32:19.522+0800","caller":"logger/logger.go:190","msg":"Security manager initialized"}
{"level":"info","timestamp":"2025-09-01T18:32:19.522+0800","caller":"logger/logger.go:190","msg":"Authentication service initialized"}
{"level":"info","timestamp":"2025-09-01T18:32:19.522+0800","caller":"logger/logger.go:190","msg":"Agent service initialized"}
{"level":"info","timestamp":"2025-09-01T18:32:19.523+0800","caller":"logger/logger.go:190","msg":"Starting metrics collector"}
{"level":"info","timestamp":"2025-09-01T18:32:19.523+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_cpu_usage","alert_name":"High CPU Usage"}
{"level":"info","timestamp":"2025-09-01T18:32:19.523+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_memory_usage","alert_name":"High Memory Usage"}
{"level":"info","timestamp":"2025-09-01T18:32:19.523+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_connection_count","alert_name":"High Connection Count"}
{"level":"info","timestamp":"2025-09-01T18:32:19.523+0800","caller":"logger/logger.go:190","msg":"Metrics collector started"}
{"level":"info","timestamp":"2025-09-01T18:32:19.579+0800","caller":"logger/logger.go:190","msg":"TCP server started","port":9999}
{"level":"info","timestamp":"2025-09-01T18:32:19.581+0800","caller":"logger/logger.go:190","msg":"Web server started","port":8080}
{"level":"info","timestamp":"2025-09-01T18:32:19.581+0800","caller":"logger/logger.go:190","msg":"Application started successfully","tcp_port":9999,"web_port":8080}
{"level":"warn","timestamp":"2025-09-01T19:04:27.539+0800","caller":"logger/logger.go:194","msg":"Using default encryption key, please change it in production"}
{"level":"warn","timestamp":"2025-09-01T19:04:27.561+0800","caller":"logger/logger.go:194","msg":"Using default secret key, please change it in production"}
{"level":"warn","timestamp":"2025-09-01T19:04:27.562+0800","caller":"logger/logger.go:194","msg":"Using default JWT secret, please change it in production"}
{"level":"info","timestamp":"2025-09-01T19:04:27.562+0800","caller":"logger/logger.go:190","msg":"Starting application","name":"Qweb网络验证系统","version":"1.0.0","config":"configs/config.yaml"}
{"level":"info","timestamp":"2025-09-01T19:04:27.562+0800","caller":"logger/logger.go:190","msg":"Starting application components..."}
{"level":"info","timestamp":"2025-09-01T19:04:27.611+0800","caller":"logger/logger.go:190","msg":"Database initialized","path":"data\\verification.db"}
{"level":"info","timestamp":"2025-09-01T19:04:27.612+0800","caller":"logger/logger.go:190","msg":"Security manager initialized"}
{"level":"info","timestamp":"2025-09-01T19:04:27.612+0800","caller":"logger/logger.go:190","msg":"Authentication service initialized"}
{"level":"info","timestamp":"2025-09-01T19:04:27.612+0800","caller":"logger/logger.go:190","msg":"Agent service initialized"}
{"level":"info","timestamp":"2025-09-01T19:04:27.612+0800","caller":"logger/logger.go:190","msg":"Starting metrics collector"}
{"level":"info","timestamp":"2025-09-01T19:04:27.612+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_cpu_usage","alert_name":"High CPU Usage"}
{"level":"info","timestamp":"2025-09-01T19:04:27.612+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_memory_usage","alert_name":"High Memory Usage"}
{"level":"info","timestamp":"2025-09-01T19:04:27.612+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_connection_count","alert_name":"High Connection Count"}
{"level":"info","timestamp":"2025-09-01T19:04:27.612+0800","caller":"logger/logger.go:190","msg":"Metrics collector started"}
{"level":"warn","timestamp":"2025-09-01T19:08:29.395+0800","caller":"logger/logger.go:194","msg":"Using default encryption key, please change it in production"}
{"level":"warn","timestamp":"2025-09-01T19:08:29.418+0800","caller":"logger/logger.go:194","msg":"Using default secret key, please change it in production"}
{"level":"warn","timestamp":"2025-09-01T19:08:29.418+0800","caller":"logger/logger.go:194","msg":"Using default JWT secret, please change it in production"}
{"level":"info","timestamp":"2025-09-01T19:08:29.419+0800","caller":"logger/logger.go:190","msg":"Starting application","name":"Qweb网络验证系统","version":"1.0.0","config":"configs/config.yaml"}
{"level":"info","timestamp":"2025-09-01T19:08:29.419+0800","caller":"logger/logger.go:190","msg":"Starting application components..."}
{"level":"info","timestamp":"2025-09-01T19:08:29.460+0800","caller":"logger/logger.go:190","msg":"Database initialized","path":"data\\verification.db"}
{"level":"info","timestamp":"2025-09-01T19:08:29.461+0800","caller":"logger/logger.go:190","msg":"Security manager initialized"}
{"level":"info","timestamp":"2025-09-01T19:08:29.461+0800","caller":"logger/logger.go:190","msg":"Authentication service initialized"}
{"level":"info","timestamp":"2025-09-01T19:08:29.461+0800","caller":"logger/logger.go:190","msg":"Agent service initialized"}
{"level":"info","timestamp":"2025-09-01T19:08:29.461+0800","caller":"logger/logger.go:190","msg":"Starting metrics collector"}
{"level":"info","timestamp":"2025-09-01T19:08:29.461+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_cpu_usage","alert_name":"High CPU Usage"}
{"level":"info","timestamp":"2025-09-01T19:08:29.461+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_memory_usage","alert_name":"High Memory Usage"}
{"level":"info","timestamp":"2025-09-01T19:08:29.462+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_connection_count","alert_name":"High Connection Count"}
{"level":"info","timestamp":"2025-09-01T19:08:29.462+0800","caller":"logger/logger.go:190","msg":"Metrics collector started"}
{"level":"info","timestamp":"2025-09-01T19:08:29.627+0800","caller":"logger/logger.go:190","msg":"TCP server started","port":9999}
{"level":"info","timestamp":"2025-09-01T19:08:29.627+0800","caller":"logger/logger.go:190","msg":"Web server started","port":8080}
{"level":"info","timestamp":"2025-09-01T19:08:29.627+0800","caller":"logger/logger.go:190","msg":"Application started successfully","tcp_port":9999,"web_port":8080}
{"level":"warn","timestamp":"2025-09-01T19:51:05.663+0800","caller":"logger/logger.go:194","msg":"Using default encryption key, please change it in production"}
{"level":"warn","timestamp":"2025-09-01T19:51:05.682+0800","caller":"logger/logger.go:194","msg":"Using default secret key, please change it in production"}
{"level":"warn","timestamp":"2025-09-01T19:51:05.682+0800","caller":"logger/logger.go:194","msg":"Using default JWT secret, please change it in production"}
{"level":"info","timestamp":"2025-09-01T19:51:05.683+0800","caller":"logger/logger.go:190","msg":"Starting application","name":"Qweb网络验证系统","version":"1.0.0","config":"configs/config.yaml"}
{"level":"info","timestamp":"2025-09-01T19:51:05.683+0800","caller":"logger/logger.go:190","msg":"Starting application components..."}
{"level":"info","timestamp":"2025-09-01T19:51:05.719+0800","caller":"logger/logger.go:190","msg":"Database initialized","path":"data\\verification.db"}
{"level":"info","timestamp":"2025-09-01T19:51:05.720+0800","caller":"logger/logger.go:190","msg":"Security manager initialized"}
{"level":"info","timestamp":"2025-09-01T19:51:05.720+0800","caller":"logger/logger.go:190","msg":"Authentication service initialized"}
{"level":"info","timestamp":"2025-09-01T19:51:05.720+0800","caller":"logger/logger.go:190","msg":"Agent service initialized"}
{"level":"info","timestamp":"2025-09-01T19:51:05.720+0800","caller":"logger/logger.go:190","msg":"Starting metrics collector"}
{"level":"info","timestamp":"2025-09-01T19:51:05.721+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_cpu_usage","alert_name":"High CPU Usage"}
{"level":"info","timestamp":"2025-09-01T19:51:05.721+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_memory_usage","alert_name":"High Memory Usage"}
{"level":"info","timestamp":"2025-09-01T19:51:05.721+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_connection_count","alert_name":"High Connection Count"}
{"level":"info","timestamp":"2025-09-01T19:51:05.721+0800","caller":"logger/logger.go:190","msg":"Metrics collector started"}
{"level":"warn","timestamp":"2025-09-01T19:57:35.206+0800","caller":"logger/logger.go:194","msg":"Using default encryption key, please change it in production"}
{"level":"warn","timestamp":"2025-09-01T19:57:35.227+0800","caller":"logger/logger.go:194","msg":"Using default secret key, please change it in production"}
{"level":"warn","timestamp":"2025-09-01T19:57:35.227+0800","caller":"logger/logger.go:194","msg":"Using default JWT secret, please change it in production"}
{"level":"info","timestamp":"2025-09-01T19:57:35.227+0800","caller":"logger/logger.go:190","msg":"Starting application","name":"Qweb网络验证系统","version":"1.0.0","config":"configs/config.yaml"}
{"level":"info","timestamp":"2025-09-01T19:57:35.228+0800","caller":"logger/logger.go:190","msg":"Starting application components..."}
{"level":"info","timestamp":"2025-09-01T19:57:35.279+0800","caller":"logger/logger.go:190","msg":"Database initialized","path":"data\\verification.db"}
{"level":"info","timestamp":"2025-09-01T19:57:35.280+0800","caller":"logger/logger.go:190","msg":"Security manager initialized"}
{"level":"info","timestamp":"2025-09-01T19:57:35.281+0800","caller":"logger/logger.go:190","msg":"Authentication service initialized"}
{"level":"info","timestamp":"2025-09-01T19:57:35.281+0800","caller":"logger/logger.go:190","msg":"Agent service initialized"}
{"level":"info","timestamp":"2025-09-01T19:57:35.281+0800","caller":"logger/logger.go:190","msg":"Starting metrics collector"}
{"level":"info","timestamp":"2025-09-01T19:57:35.281+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_cpu_usage","alert_name":"High CPU Usage"}
{"level":"info","timestamp":"2025-09-01T19:57:35.281+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_memory_usage","alert_name":"High Memory Usage"}
{"level":"info","timestamp":"2025-09-01T19:57:35.281+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_connection_count","alert_name":"High Connection Count"}
{"level":"info","timestamp":"2025-09-01T19:57:35.282+0800","caller":"logger/logger.go:190","msg":"Metrics collector started"}
{"level":"info","timestamp":"2025-09-01T19:57:35.457+0800","caller":"logger/logger.go:190","msg":"TCP server started","port":9999}
{"level":"info","timestamp":"2025-09-01T19:57:35.457+0800","caller":"logger/logger.go:190","msg":"Web server started","port":8080}
{"level":"info","timestamp":"2025-09-01T19:57:35.457+0800","caller":"logger/logger.go:190","msg":"Application started successfully","tcp_port":9999,"web_port":8080}
{"level":"warn","timestamp":"2025-09-02T18:45:03.885+0800","caller":"logger/logger.go:194","msg":"Using default encryption key, please change it in production"}
{"level":"warn","timestamp":"2025-09-02T18:45:03.903+0800","caller":"logger/logger.go:194","msg":"Using default secret key, please change it in production"}
{"level":"warn","timestamp":"2025-09-02T18:45:03.903+0800","caller":"logger/logger.go:194","msg":"Using default JWT secret, please change it in production"}
{"level":"info","timestamp":"2025-09-02T18:45:03.903+0800","caller":"logger/logger.go:190","msg":"Starting application","name":"Qweb网络验证系统","version":"1.0.0","config":"configs/config.yaml"}
{"level":"info","timestamp":"2025-09-02T18:45:03.905+0800","caller":"logger/logger.go:190","msg":"Starting application components..."}
{"level":"info","timestamp":"2025-09-02T18:45:03.950+0800","caller":"logger/logger.go:190","msg":"Database initialized","path":"data\\verification.db"}
{"level":"info","timestamp":"2025-09-02T18:45:03.951+0800","caller":"logger/logger.go:190","msg":"Security manager initialized"}
{"level":"info","timestamp":"2025-09-02T18:45:03.952+0800","caller":"logger/logger.go:190","msg":"Authentication service initialized"}
{"level":"info","timestamp":"2025-09-02T18:45:03.952+0800","caller":"logger/logger.go:190","msg":"Agent service initialized"}
{"level":"info","timestamp":"2025-09-02T18:45:03.952+0800","caller":"logger/logger.go:190","msg":"Starting metrics collector"}
{"level":"info","timestamp":"2025-09-02T18:45:03.952+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_cpu_usage","alert_name":"High CPU Usage"}
{"level":"info","timestamp":"2025-09-02T18:45:03.952+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_memory_usage","alert_name":"High Memory Usage"}
{"level":"info","timestamp":"2025-09-02T18:45:03.952+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_connection_count","alert_name":"High Connection Count"}
{"level":"info","timestamp":"2025-09-02T18:45:03.952+0800","caller":"logger/logger.go:190","msg":"Metrics collector started"}
{"level":"info","timestamp":"2025-09-02T18:49:30.252+0800","caller":"logger/logger.go:190","msg":"Received shutdown signal","signal":"terminated"}
{"level":"info","timestamp":"2025-09-02T18:49:30.253+0800","caller":"logger/logger.go:190","msg":"Stopping application..."}
{"level":"info","timestamp":"2025-09-02T18:49:30.253+0800","caller":"logger/logger.go:190","msg":"Stopping metrics collector"}
{"level":"info","timestamp":"2025-09-02T18:49:30.261+0800","caller":"logger/logger.go:190","msg":"Application stopped"}
{"level":"warn","timestamp":"2025-09-02T18:51:19.920+0800","caller":"logger/logger.go:194","msg":"Using default encryption key, please change it in production"}
{"level":"warn","timestamp":"2025-09-02T18:51:19.941+0800","caller":"logger/logger.go:194","msg":"Using default secret key, please change it in production"}
{"level":"warn","timestamp":"2025-09-02T18:51:19.942+0800","caller":"logger/logger.go:194","msg":"Using default JWT secret, please change it in production"}
{"level":"info","timestamp":"2025-09-02T18:51:19.942+0800","caller":"logger/logger.go:190","msg":"Starting application","name":"Qweb网络验证系统","version":"1.0.0","config":"configs/config.yaml"}
{"level":"info","timestamp":"2025-09-02T18:51:19.943+0800","caller":"logger/logger.go:190","msg":"Starting application components..."}
{"level":"info","timestamp":"2025-09-02T18:51:19.990+0800","caller":"logger/logger.go:190","msg":"Database initialized","path":"data\\verification.db"}
{"level":"info","timestamp":"2025-09-02T18:51:19.991+0800","caller":"logger/logger.go:190","msg":"Security manager initialized"}
{"level":"info","timestamp":"2025-09-02T18:51:19.991+0800","caller":"logger/logger.go:190","msg":"Authentication service initialized"}
{"level":"info","timestamp":"2025-09-02T18:51:19.991+0800","caller":"logger/logger.go:190","msg":"Agent service initialized"}
{"level":"info","timestamp":"2025-09-02T18:51:19.992+0800","caller":"logger/logger.go:190","msg":"Starting metrics collector"}
{"level":"info","timestamp":"2025-09-02T18:51:19.992+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_cpu_usage","alert_name":"High CPU Usage"}
{"level":"info","timestamp":"2025-09-02T18:51:19.992+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_memory_usage","alert_name":"High Memory Usage"}
{"level":"info","timestamp":"2025-09-02T18:51:19.992+0800","caller":"logger/logger.go:190","msg":"Alert registered","alert_id":"high_connection_count","alert_name":"High Connection Count"}
{"level":"info","timestamp":"2025-09-02T18:51:19.992+0800","caller":"logger/logger.go:190","msg":"Metrics collector started"}
{"level":"info","timestamp":"2025-09-02T18:51:20.192+0800","caller":"logger/logger.go:190","msg":"TCP server started","port":9999}
{"level":"info","timestamp":"2025-09-02T18:51:20.278+0800","caller":"logger/logger.go:190","msg":"Default admin account initialized","username":"admin"}
