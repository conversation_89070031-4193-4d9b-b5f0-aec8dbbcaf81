package models

import (
	"time"
	"gorm.io/gorm"
)

// Card 卡号表模型
type Card struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	CardNumber   string         `json:"card_number" gorm:"uniqueIndex;not null;size:50"`
	CardPassword string         `json:"card_password" gorm:"not null;size:128"`
	DurationDays int            `json:"duration_days" gorm:"not null"`
	Price        float64        `json:"price" gorm:"type:decimal(10,2);not null"`
	AgentID      uint           `json:"agent_id" gorm:"not null;index"`
	Agent        Agent          `json:"agent,omitempty" gorm:"foreignKey:AgentID"`
	UserID       *uint          `json:"user_id" gorm:"index"`
	User         *User          `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Status       int            `json:"status" gorm:"default:0"` // 0:未使用 1:已使用 2:已过期
	UsedAt       *time.Time     `json:"used_at"`
	ExpiresAt    *time.Time     `json:"expires_at"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Commissions  []Commission   `json:"commissions,omitempty" gorm:"foreignKey:CardID"`
}

// TableName 指定表名
func (Card) TableName() string {
	return "cards"
}

// CardStatus 卡号状态常量
const (
	CardStatusUnused  = 0 // 未使用
	CardStatusUsed    = 1 // 已使用
	CardStatusExpired = 2 // 已过期
)

// IsUnused 检查卡号是否未使用
func (c *Card) IsUnused() bool {
	return c.Status == CardStatusUnused
}

// IsUsed 检查卡号是否已使用
func (c *Card) IsUsed() bool {
	return c.Status == CardStatusUsed
}

// IsExpired 检查卡号是否已过期
func (c *Card) IsExpired() bool {
	return c.Status == CardStatusExpired || 
		   (c.ExpiresAt != nil && c.ExpiresAt.Before(time.Now()))
}

// IsValid 检查卡号是否有效（已使用且未过期）
func (c *Card) IsValid() bool {
	return c.IsUsed() && !c.IsExpired()
}

// Use 使用卡号
func (c *Card) Use(userID uint) error {
	if !c.IsUnused() {
		return ErrCardAlreadyUsed
	}
	
	now := time.Now()
	c.UserID = &userID
	c.Status = CardStatusUsed
	c.UsedAt = &now
	expiresAt := now.AddDate(0, 0, c.DurationDays)
	c.ExpiresAt = &expiresAt
	c.UpdatedAt = now
	
	return nil
}

// BeforeCreate GORM钩子：创建前
func (c *Card) BeforeCreate(tx *gorm.DB) error {
	c.CreatedAt = time.Now()
	c.UpdatedAt = time.Now()
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (c *Card) BeforeUpdate(tx *gorm.DB) error {
	c.UpdatedAt = time.Now()
	return nil
}