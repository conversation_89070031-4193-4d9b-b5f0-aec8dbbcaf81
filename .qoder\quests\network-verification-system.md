# 网络验证系统设计文档

## 1. 概述

### 1.1 系统简介
基于Go语言开发的安全网络验证系统，采用TCP Socket通信，支持多级代理商分销、用户管理、卡号管理等功能。系统注重安全性，实现了TLS加密、防暴力破解、数据加密存储等多重安全机制。

### 1.2 核心特性
- 安全的TCP Socket通信（TLS 1.2+加密）
- 多线程并发处理
- SQLite3数据库存储
- Web管理界面
- 多级代理商分销系统
- 完善的日志系统和监控告警
- 多重安全防护机制

### 1.3 技术栈
- **后端语言**: Go 1.19+
- **数据库**: SQLite3
- **Web框架**: Gin
- **ORM**: GORM
- **加密**: TLS 1.2+, AES-256
- **前端**: HTML/CSS/JavaScript (或Vue.js)

## 2. 系统架构

### 2.1 整体架构图

```mermaid
graph TB
    Client[客户端应用] --> LB[负载均衡器]
    LB --> Server1[验证服务器1]
    LB --> Server2[验证服务器2]
    LB --> ServerN[验证服务器N]
    
    Server1 --> Auth[认证模块]
    Server1 --> Agent[代理商模块]
    Server1 --> Card[卡号管理]
    Server1 --> Log[日志系统]
    
    Auth --> DB[(SQLite3数据库)]
    Agent --> DB
    Card --> DB
    Log --> DB
    
    Admin[管理员] --> Web[Web管理界面]
    Web --> API[REST API]
    API --> DB
    
    Monitor[监控告警] --> Log
    Monitor --> Metrics[性能指标]
```

### 2.2 模块架构

```mermaid
graph LR
    A[TCP服务器] --> B[连接管理器]
    B --> C[认证处理器]
    C --> D[业务逻辑层]
    D --> E[数据访问层]
    E --> F[SQLite3数据库]
    
    G[Web服务器] --> H[API路由]
    H --> I[中间件]
    I --> D
    
    J[安全模块] --> K[TLS加密]
    J --> L[IP限制]
    J --> M[频率控制]
    J --> N[防暴力破解]
```

## 3. 数据库设计

### 3.1 数据库表结构

#### 用户表 (users)
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PRIMARY KEY | 用户ID |
| username | TEXT | UNIQUE, NOT NULL | 用户名 |
| password_hash | TEXT | NOT NULL | 密码哈希 |
| salt | TEXT | NOT NULL | 密码盐值 |
| status | INTEGER | DEFAULT 1 | 用户状态(1:启用 0:禁用) |
| agent_id | INTEGER | FOREIGN KEY | 所属代理商ID |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 更新时间 |
| last_login | DATETIME | NULL | 最后登录时间 |

#### 代理商表 (agents)
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PRIMARY KEY | 代理商ID |
| username | TEXT | UNIQUE, NOT NULL | 代理商用户名 |
| password_hash | TEXT | NOT NULL | 密码哈希 |
| salt | TEXT | NOT NULL | 密码盐值 |
| parent_id | INTEGER | FOREIGN KEY | 上级代理商ID |
| level | INTEGER | NOT NULL | 代理商级别 |
| commission_rate | DECIMAL(5,2) | DEFAULT 0.00 | 佣金比例 |
| status | INTEGER | DEFAULT 1 | 状态 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

#### 卡号表 (cards)
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PRIMARY KEY | 卡号ID |
| card_number | TEXT | UNIQUE, NOT NULL | 卡号 |
| card_password | TEXT | NOT NULL | 卡密 |
| duration_days | INTEGER | NOT NULL | 有效期天数 |
| price | DECIMAL(10,2) | NOT NULL | 价格 |
| agent_id | INTEGER | FOREIGN KEY | 所属代理商 |
| user_id | INTEGER | FOREIGN KEY | 绑定用户ID |
| status | INTEGER | DEFAULT 0 | 状态(0:未使用 1:已使用 2:已过期) |
| used_at | DATETIME | NULL | 使用时间 |
| expires_at | DATETIME | NULL | 过期时间 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

#### 会话表 (sessions)
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PRIMARY KEY | 会话ID |
| user_id | INTEGER | FOREIGN KEY | 用户ID |
| session_token | TEXT | UNIQUE, NOT NULL | 会话令牌 |
| ip_address | TEXT | NOT NULL | IP地址 |
| expires_at | DATETIME | NOT NULL | 过期时间 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

#### 操作日志表 (operation_logs)
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PRIMARY KEY | 日志ID |
| user_id | INTEGER | FOREIGN KEY | 用户ID |
| operation | TEXT | NOT NULL | 操作类型 |
| details | TEXT | NULL | 操作详情 |
| ip_address | TEXT | NOT NULL | IP地址 |
| user_agent | TEXT | NULL | 用户代理 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

#### IP黑名单表 (ip_blacklist)
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PRIMARY KEY | 记录ID |
| ip_address | TEXT | UNIQUE, NOT NULL | IP地址 |
| failed_attempts | INTEGER | DEFAULT 1 | 失败次数 |
| banned_until | DATETIME | NULL | 封禁到期时间 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

### 3.2 数据库关系图

```mermaid
erDiagram
    USERS {
        int id PK
        string username UK
        string password_hash
        string salt
        int status
        int agent_id FK
        datetime created_at
        datetime updated_at
        datetime last_login
    }
    
    AGENTS {
        int id PK
        string username UK
        string password_hash
        string salt
        int parent_id FK
        int level
        decimal commission_rate
        int status
        datetime created_at
    }
    
    CARDS {
        int id PK
        string card_number UK
        string card_password
        int duration_days
        decimal price
        int agent_id FK
        int user_id FK
        int status
        datetime used_at
        datetime expires_at
        datetime created_at
    }
    
    SESSIONS {
        int id PK
        int user_id FK
        string session_token UK
        string ip_address
        datetime expires_at
        datetime created_at
    }
    
    OPERATION_LOGS {
        int id PK
        int user_id FK
        string operation
        string details
        string ip_address
        string user_agent
        datetime created_at
    }
    
    IP_BLACKLIST {
        int id PK
        string ip_address UK
        int failed_attempts
        datetime banned_until
        datetime created_at
    }
    
    USERS ||--o{ CARDS : uses
    AGENTS ||--o{ USERS : manages
    AGENTS ||--o{ CARDS : owns
    AGENTS ||--o{ AGENTS : parent-child
    USERS ||--o{ SESSIONS : has
    USERS ||--o{ OPERATION_LOGS : generates
```

## 4. 核心模块设计

### 4.1 TCP服务器模块

#### 4.1.1 连接管理器
```mermaid
graph TD
    A[TCP监听器] --> B[接收连接]
    B --> C[TLS握手]
    C --> D[IP白名单检查]
    D --> E[连接频率限制]
    E --> F[创建连接处理器]
    F --> G[添加到连接池]
    
    H[心跳检测] --> I[检查连接状态]
    I --> J[清理无效连接]
```

#### 4.1.2 认证流程
```mermaid
sequenceDiagram
    participant C as 客户端
    participant S as 服务器
    participant DB as 数据库
    
    C->>S: 连接请求 (TLS)
    S->>S: 验证IP白名单
    S->>C: 连接建立
    
    C->>S: 认证请求 (用户名/卡号+密码)
    S->>S: 验证消息签名
    S->>S: 检查时间戳和随机数
    S->>DB: 查询用户/卡号信息
    DB->>S: 返回用户信息
    S->>S: 验证密码
    S->>DB: 创建会话记录
    S->>C: 认证响应 (成功/失败)
    
    loop 心跳保持
        C->>S: 心跳包
        S->>C: 心跳响应
    end
```

### 4.2 Web管理界面模块

#### 4.2.1 功能模块划分
```mermaid
graph LR
    A[Web管理界面] --> B[用户管理]
    A --> C[代理商管理]
    A --> D[卡号管理]
    A --> E[系统监控]
    A --> F[日志查看]
    A --> G[权限管理]
    
    B --> B1[用户列表]
    B --> B2[用户添加/编辑]
    B --> B3[用户状态管理]
    
    C --> C1[代理商列表]
    C --> C2[代理商层级管理]
    C --> C3[佣金统计]
    
    D --> D1[卡号生成]
    D --> D2[卡号查询]
    D --> D3[批量导入]
    
    E --> E1[实时连接数]
    E --> E2[性能指标]
    E --> E3[告警管理]
```

#### 4.2.2 API接口设计

| 接口路径 | 方法 | 功能 | 认证要求 |
|----------|------|------|----------|
| /api/auth/login | POST | 管理员登录 | 无 |
| /api/auth/logout | POST | 管理员登出 | Token |
| /api/users | GET | 获取用户列表 | Token |
| /api/users | POST | 创建用户 | Token |
| /api/users/:id | PUT | 更新用户 | Token |
| /api/users/:id | DELETE | 删除用户 | Token |
| /api/agents | GET | 获取代理商列表 | Token |
| /api/agents | POST | 创建代理商 | Token |
| /api/agents/:id/stats | GET | 代理商统计 | Token |
| /api/cards | GET | 获取卡号列表 | Token |
| /api/cards/generate | POST | 生成卡号 | Token |
| /api/cards/batch | POST | 批量导入卡号 | Token |
| /api/logs | GET | 获取操作日志 | Token |
| /api/monitor/stats | GET | 获取系统统计 | Token |

### 4.3 代理商分销系统

#### 4.3.1 多级分销架构
```mermaid
graph TD
    A[总代理商 Level 1] --> B[二级代理商 Level 2]
    A --> C[二级代理商 Level 2]
    B --> D[三级代理商 Level 3]
    B --> E[三级代理商 Level 3]
    C --> F[三级代理商 Level 3]
    
    D --> G[最终用户]
    E --> H[最终用户]
    F --> I[最终用户]
```

#### 4.3.2 佣金计算流程
```mermaid
graph TD
    A[用户购买卡号] --> B[计算总金额]
    B --> C[查找代理商层级链]
    C --> D[计算各级佣金]
    D --> E[更新代理商收益]
    E --> F[记录佣金日志]
    
    G[佣金计算规则] --> H[Level 1: 50%]
    G --> I[Level 2: 30%]
    G --> J[Level 3: 20%]
```

### 4.4 安全模块

#### 4.4.1 连接安全机制
```mermaid
graph TD
    A[客户端连接] --> B[TLS 1.2+ 验证]
    B --> C[IP白名单检查]
    C --> D[连接频率限制]
    D --> E[DDoS防护]
    E --> F[建立安全连接]
    
    G[失败处理] --> H[记录失败次数]
    H --> I[达到阈值?]
    I -->|是| J[临时封禁IP]
    I -->|否| K[允许重试]
```

#### 4.4.2 数据安全机制
```mermaid
graph LR
    A[敏感数据] --> B[AES-256加密]
    B --> C[加密存储]
    
    D[数据库操作] --> E[参数化查询]
    E --> F[SQL注入防护]
    
    G[数据库备份] --> H[定期自动备份]
    H --> I[加密备份文件]
```

#### 4.4.3 认证安全机制
```mermaid
sequenceDiagram
    participant C as 客户端
    participant S as 服务器
    
    C->>C: 生成时间戳和随机数
    C->>C: 构造消息体
    C->>C: 计算消息签名
    C->>S: 发送认证请求
    
    S->>S: 验证时间戳有效性
    S->>S: 检查随机数是否重复
    S->>S: 验证消息签名
    S->>S: 执行认证逻辑
    S->>C: 返回认证结果
```

## 5. 客户端SDK设计

### 5.1 多语言SDK架构

#### 5.1.1 SDK统一接口设计
为支持Go、C#、Python等主流语言，设计统一的客户端SDK接口：

```mermaid
graph TD
    A[统一接口规范] --> B[Go SDK]
    A --> C[C# SDK]
    A --> D[Python SDK]
    A --> E[Java SDK]
    A --> F[其他语言SDK]
    
    B --> G[TCP连接管理]
    C --> G
    D --> G
    E --> G
    F --> G
    
    G --> H[TLS加密通信]
    H --> I[消息序列化]
    I --> J[服务器]
```

#### 5.1.2 SDK核心接口定义

**认证客户端接口**
```go
type VerificationClient interface {
    // 连接到验证服务器
    Connect(serverAddr string, tlsConfig *tls.Config) error
    
    // 用户名密码认证
    AuthenticateUser(username, password string) (*AuthResult, error)
    
    // 卡号密码认证
    AuthenticateCard(cardNumber, cardPassword string) (*AuthResult, error)
    
    // 检查认证状态
    CheckAuthStatus() (*StatusResult, error)
    
    // 心跳保持
    SendHeartbeat() error
    
    // 断开连接
    Disconnect() error
    
    // 设置事件回调
    SetEventHandler(handler EventHandler)
}
```

**事件处理接口**
```go
type EventHandler interface {
    OnConnected()
    OnDisconnected(reason string)
    OnAuthSuccess(result *AuthResult)
    OnAuthFailed(reason string)
    OnSessionExpired()
    OnError(err error)
}
```

**数据结构定义**
```go
type AuthResult struct {
    Success      bool      `json:"success"`
    SessionToken string    `json:"session_token"`
    ExpiresAt    time.Time `json:"expires_at"`
    UserInfo     *UserInfo `json:"user_info"`
    Message      string    `json:"message"`
}

type UserInfo struct {
    UserID   int64  `json:"user_id"`
    Username string `json:"username"`
    Level    int    `json:"level"`
    AgentID  int64  `json:"agent_id"`
}

type StatusResult struct {
    IsAuthenticated bool      `json:"is_authenticated"`
    SessionValid    bool      `json:"session_valid"`
    ExpiresAt       time.Time `json:"expires_at"`
    ServerTime      time.Time `json:"server_time"`
}
```

### 5.2 各语言SDK实现规范

#### 5.2.1 Go SDK示例
```go
package verification

import (
    "crypto/tls"
    "encoding/json"
    "net"
    "time"
)

type Client struct {
    conn        net.Conn
    serverAddr  string
    tlsConfig   *tls.Config
    eventHandler EventHandler
    sessionToken string
    isConnected  bool
}

func NewClient() *Client {
    return &Client{
        isConnected: false,
    }
}

func (c *Client) Connect(serverAddr string, tlsConfig *tls.Config) error {
    // 实现TLS连接逻辑
    conn, err := tls.Dial("tcp", serverAddr, tlsConfig)
    if err != nil {
        return err
    }
    
    c.conn = conn
    c.serverAddr = serverAddr
    c.isConnected = true
    
    if c.eventHandler != nil {
        c.eventHandler.OnConnected()
    }
    
    return nil
}

func (c *Client) AuthenticateUser(username, password string) (*AuthResult, error) {
    // 构造认证消息
    authReq := &AuthRequest{
        Type:      "user_auth",
        Timestamp: time.Now().Unix(),
        Nonce:     generateNonce(),
        Username:  username,
        Password:  encryptPassword(password),
    }
    
    // 添加消息签名
    authReq.Signature = signMessage(authReq)
    
    // 发送消息并接收响应
    response, err := c.sendMessage(authReq)
    if err != nil {
        return nil, err
    }
    
    // 解析响应
    var result AuthResult
    if err := json.Unmarshal(response, &result); err != nil {
        return nil, err
    }
    
    if result.Success {
        c.sessionToken = result.SessionToken
        if c.eventHandler != nil {
            c.eventHandler.OnAuthSuccess(&result)
        }
    } else {
        if c.eventHandler != nil {
            c.eventHandler.OnAuthFailed(result.Message)
        }
    }
    
    return &result, nil
}
```

#### 5.2.2 C# SDK示例
```csharp
namespace VerificationSDK
{
    public interface IVerificationClient
    {
        Task<bool> ConnectAsync(string serverAddress, SslClientAuthenticationOptions sslOptions);
        Task<AuthResult> AuthenticateUserAsync(string username, string password);
        Task<AuthResult> AuthenticateCardAsync(string cardNumber, string cardPassword);
        Task<StatusResult> CheckAuthStatusAsync();
        Task<bool> SendHeartbeatAsync();
        Task DisconnectAsync();
        void SetEventHandler(IEventHandler handler);
    }
    
    public class VerificationClient : IVerificationClient
    {
        private SslStream _sslStream;
        private TcpClient _tcpClient;
        private IEventHandler _eventHandler;
        private string _sessionToken;
        private bool _isConnected;
        
        public async Task<bool> ConnectAsync(string serverAddress, SslClientAuthenticationOptions sslOptions)
        {
            try
            {
                var uri = new Uri($"tcp://{serverAddress}");
                _tcpClient = new TcpClient();
                await _tcpClient.ConnectAsync(uri.Host, uri.Port);
                
                _sslStream = new SslStream(_tcpClient.GetStream());
                await _sslStream.AuthenticateAsClientAsync(sslOptions);
                
                _isConnected = true;
                _eventHandler?.OnConnected();
                
                return true;
            }
            catch (Exception ex)
            {
                _eventHandler?.OnError(ex);
                return false;
            }
        }
        
        public async Task<AuthResult> AuthenticateUserAsync(string username, string password)
        {
            var authRequest = new AuthRequest
            {
                Type = "user_auth",
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                Nonce = GenerateNonce(),
                Username = username,
                Password = EncryptPassword(password)
            };
            
            authRequest.Signature = SignMessage(authRequest);
            
            var response = await SendMessageAsync(authRequest);
            var result = JsonSerializer.Deserialize<AuthResult>(response);
            
            if (result.Success)
            {
                _sessionToken = result.SessionToken;
                _eventHandler?.OnAuthSuccess(result);
            }
            else
            {
                _eventHandler?.OnAuthFailed(result.Message);
            }
            
            return result;
        }
    }
}
```

#### 5.2.3 Python SDK示例
```python
import ssl
import socket
import json
import time
import hashlib
import secrets
from typing import Optional, Dict, Any
from abc import ABC, abstractmethod

class EventHandler(ABC):
    @abstractmethod
    def on_connected(self):
        pass
    
    @abstractmethod
    def on_disconnected(self, reason: str):
        pass
    
    @abstractmethod
    def on_auth_success(self, result: dict):
        pass
    
    @abstractmethod
    def on_auth_failed(self, reason: str):
        pass
    
    @abstractmethod
    def on_error(self, error: Exception):
        pass

class VerificationClient:
    def __init__(self):
        self._socket: Optional[ssl.SSLSocket] = None
        self._event_handler: Optional[EventHandler] = None
        self._session_token: Optional[str] = None
        self._is_connected: bool = False
    
    def connect(self, server_address: str, ssl_context: ssl.SSLContext) -> bool:
        try:
            host, port = server_address.split(':')
            
            # 创建TCP连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            
            # 包装SSL
            self._socket = ssl_context.wrap_socket(sock)
            self._socket.connect((host, int(port)))
            
            self._is_connected = True
            
            if self._event_handler:
                self._event_handler.on_connected()
            
            return True
            
        except Exception as e:
            if self._event_handler:
                self._event_handler.on_error(e)
            return False
    
    def authenticate_user(self, username: str, password: str) -> Dict[str, Any]:
        auth_request = {
            'type': 'user_auth',
            'timestamp': int(time.time()),
            'nonce': secrets.token_hex(16),
            'username': username,
            'password': self._encrypt_password(password)
        }
        
        auth_request['signature'] = self._sign_message(auth_request)
        
        response = self._send_message(auth_request)
        result = json.loads(response)
        
        if result['success']:
            self._session_token = result['session_token']
            if self._event_handler:
                self._event_handler.on_auth_success(result)
        else:
            if self._event_handler:
                self._event_handler.on_auth_failed(result['message'])
        
        return result
    
    def set_event_handler(self, handler: EventHandler):
        self._event_handler = handler
    
    def _encrypt_password(self, password: str) -> str:
        # 实现密码加密逻辑
        return hashlib.sha256(password.encode()).hexdigest()
    
    def _sign_message(self, message: dict) -> str:
        # 实现消息签名逻辑
        message_str = json.dumps(message, sort_keys=True)
        return hashlib.sha256(message_str.encode()).hexdigest()
```

### 5.3 SDK配置文件规范

#### 5.3.1 通用配置格式
```yaml
# SDK配置文件 (sdk_config.yaml)
server:
  address: "server.example.com:8080"
  tls:
    enabled: true
    cert_path: "/path/to/server.crt"
    verify_server: true
    min_version: "TLS12"

client:
  connect_timeout: 30s
  read_timeout: 60s
  write_timeout: 30s
  heartbeat_interval: 30s
  max_retries: 3
  retry_interval: 5s

logging:
  level: "info"
  file: "verification_client.log"
  max_size: 50MB
  max_backups: 5

security:
  enable_signature: true
  signature_algorithm: "SHA256"
  encryption_algorithm: "AES256"
```

#### 5.3.2 各语言配置加载示例

**Go配置加载**
```go
type SDKConfig struct {
    Server struct {
        Address string `yaml:"address"`
        TLS     struct {
            Enabled      bool   `yaml:"enabled"`
            CertPath     string `yaml:"cert_path"`
            VerifyServer bool   `yaml:"verify_server"`
            MinVersion   string `yaml:"min_version"`
        } `yaml:"tls"`
    } `yaml:"server"`
    // ... 其他配置
}

func LoadConfig(configPath string) (*SDKConfig, error) {
    data, err := ioutil.ReadFile(configPath)
    if err != nil {
        return nil, err
    }
    
    var config SDKConfig
    if err := yaml.Unmarshal(data, &config); err != nil {
        return nil, err
    }
    
    return &config, nil
}
```

**C#配置加载**
```csharp
public class SDKConfig
{
    public ServerConfig Server { get; set; }
    public ClientConfig Client { get; set; }
    public LoggingConfig Logging { get; set; }
    public SecurityConfig Security { get; set; }
    
    public static SDKConfig LoadFromFile(string configPath)
    {
        var yaml = File.ReadAllText(configPath);
        var deserializer = new DeserializerBuilder().Build();
        return deserializer.Deserialize<SDKConfig>(yaml);
    }
}
```

**Python配置加载**
```python
import yaml
from dataclasses import dataclass
from typing import Optional

@dataclass
class SDKConfig:
    server_address: str
    tls_enabled: bool
    cert_path: Optional[str]
    connect_timeout: int
    heartbeat_interval: int
    
    @classmethod
    def load_from_file(cls, config_path: str) -> 'SDKConfig':
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        return cls(
            server_address=config_data['server']['address'],
            tls_enabled=config_data['server']['tls']['enabled'],
            cert_path=config_data['server']['tls'].get('cert_path'),
            connect_timeout=config_data['client']['connect_timeout'],
            heartbeat_interval=config_data['client']['heartbeat_interval']
        )
```

### 5.4 SDK使用示例

#### 5.4.1 Go使用示例
```go
package main

import (
    "crypto/tls"
    "log"
    verification "github.com/yourorg/verification-sdk-go"
)

type MyEventHandler struct{}

func (h *MyEventHandler) OnConnected() {
    log.Println("连接成功")
}

func (h *MyEventHandler) OnAuthSuccess(result *verification.AuthResult) {
    log.Printf("认证成功: %s", result.Message)
}

func (h *MyEventHandler) OnAuthFailed(reason string) {
    log.Printf("认证失败: %s", reason)
}

func (h *MyEventHandler) OnError(err error) {
    log.Printf("错误: %v", err)
}

func main() {
    // 创建客户端
    client := verification.NewClient()
    
    // 设置事件处理器
    client.SetEventHandler(&MyEventHandler{})
    
    // 配置TLS
    tlsConfig := &tls.Config{
        ServerName: "verification.example.com",
        MinVersion: tls.VersionTLS12,
    }
    
    // 连接服务器
    if err := client.Connect("verification.example.com:8080", tlsConfig); err != nil {
        log.Fatal(err)
    }
    defer client.Disconnect()
    
    // 用户认证
    result, err := client.AuthenticateUser("testuser", "password123")
    if err != nil {
        log.Fatal(err)
    }
    
    if result.Success {
        log.Println("认证成功，开始心跳")
        // 定期发送心跳
        for {
            time.Sleep(30 * time.Second)
            if err := client.SendHeartbeat(); err != nil {
                log.Printf("心跳失败: %v", err)
                break
            }
        }
    }
}
```

#### 5.4.2 C#使用示例
```csharp
using System;
using System.Threading.Tasks;
using VerificationSDK;

class Program
{
    static async Task Main(string[] args)
    {
        var client = new VerificationClient();
        client.SetEventHandler(new MyEventHandler());
        
        var sslOptions = new SslClientAuthenticationOptions
        {
            TargetHost = "verification.example.com",
            EnabledSslProtocols = SslProtocols.Tls12 | SslProtocols.Tls13
        };
        
        if (await client.ConnectAsync("verification.example.com:8080", sslOptions))
        {
            var result = await client.AuthenticateUserAsync("testuser", "password123");
            
            if (result.Success)
            {
                Console.WriteLine("认证成功");
                
                // 心跳循环
                while (true)
                {
                    await Task.Delay(30000);
                    await client.SendHeartbeatAsync();
                }
            }
        }
    }
}

public class MyEventHandler : IEventHandler
{
    public void OnConnected() => Console.WriteLine("连接成功");
    public void OnAuthSuccess(AuthResult result) => Console.WriteLine($"认证成功: {result.Message}");
    public void OnAuthFailed(string reason) => Console.WriteLine($"认证失败: {reason}");
    public void OnError(Exception error) => Console.WriteLine($"错误: {error.Message}");
}
```

#### 5.4.3 Python使用示例
```python
import ssl
import time
from verification_sdk import VerificationClient, EventHandler

class MyEventHandler(EventHandler):
    def on_connected(self):
        print("连接成功")
    
    def on_auth_success(self, result):
        print(f"认证成功: {result['message']}")
    
    def on_auth_failed(self, reason):
        print(f"认证失败: {reason}")
    
    def on_error(self, error):
        print(f"错误: {error}")

def main():
    # 创建客户端
    client = VerificationClient()
    client.set_event_handler(MyEventHandler())
    
    # 配置SSL
    ssl_context = ssl.create_default_context()
    ssl_context.minimum_version = ssl.TLSVersion.TLSv1_2
    
    # 连接服务器
    if client.connect("verification.example.com:8080", ssl_context):
        # 用户认证
        result = client.authenticate_user("testuser", "password123")
        
        if result['success']:
            print("认证成功，开始心跳")
            
            # 心跳循环
            while True:
                time.sleep(30)
                client.send_heartbeat()

if __name__ == "__main__":
    main()
```

## 6. 消息协议设计

### 5.1 通信协议格式

#### 5.1.1 消息包结构
```
+--------+--------+----------+----------+----------+
| 魔数   | 版本   | 消息类型  | 数据长度  | 数据体    |
| 4字节  | 2字节  | 2字节    | 4字节    | N字节    |
+--------+--------+----------+----------+----------+
```

#### 5.1.2 消息类型定义
| 消息类型 | 值 | 说明 |
|----------|----|----- |
| 认证请求 | 0x0001 | 用户认证请求 |
| 认证响应 | 0x0002 | 认证结果响应 |
| 心跳请求 | 0x0003 | 客户端心跳 |
| 心跳响应 | 0x0004 | 服务端心跳响应 |
| 卡号验证 | 0x0005 | 卡号验证请求 |
| 卡号响应 | 0x0006 | 卡号验证响应 |
| 错误消息 | 0x00FF | 错误信息 |

### 5.2 认证消息格式

#### 5.2.1 认证请求消息
```json
{
    "type": "auth_request",
    "timestamp": 1640995200,
    "nonce": "random_string_32",
    "data": {
        "username": "user123",
        "password": "encrypted_password",
        "client_version": "1.0.0"
    },
    "signature": "message_signature"
}
```

#### 5.2.2 认证响应消息
```json
{
    "type": "auth_response",
    "timestamp": 1640995201,
    "data": {
        "status": "success|failed",
        "message": "认证成功|认证失败原因",
        "session_token": "session_token_if_success",
        "expires_at": 1640995200
    },
    "signature": "message_signature"
}
```

## 7. 日志系统与监控

### 6.1 日志系统架构
```mermaid
graph TD
    A[应用日志] --> B[日志收集器]
    C[访问日志] --> B
    D[错误日志] --> B
    E[审计日志] --> B
    
    B --> F[日志处理器]
    F --> G[日志格式化]
    G --> H[日志存储]
    H --> I[文件存储]
    H --> J[数据库存储]
    
    K[日志查询] --> L[Web界面]
    K --> M[API接口]
```

### 6.2 监控指标

#### 6.2.1 系统指标
- CPU使用率
- 内存使用率
- 磁盘使用率
- 网络IO
- 并发连接数
- 请求QPS

#### 6.2.2 业务指标
- 认证成功率
- 认证失败次数
- 活跃用户数
- 卡号使用情况
- 代理商销售统计

### 6.3 告警机制
```mermaid
graph TD
    A[监控指标采集] --> B[规则引擎]
    B --> C[阈值检查]
    C --> D[告警触发]
    D --> E[邮件通知]
    D --> F[短信通知]
    D --> G[钉钉/微信通知]
    
    H[告警收敛] --> I[相同告警合并]
    I --> J[告警升级]
```

## 8. 部署与运维

### 7.1 部署架构
```mermaid
graph TB
    A[负载均衡器] --> B[验证服务器1]
    A --> C[验证服务器2]
    A --> D[验证服务器N]
    
    E[Web服务器] --> F[Web应用]
    F --> G[SQLite数据库]
    
    B --> G
    C --> G
    D --> G
    
    H[监控服务] --> I[Prometheus]
    I --> J[Grafana]
```

### 7.2 配置管理

#### 7.2.1 配置文件结构
```yaml
server:
  tcp_port: 8080
  web_port: 8081
  tls_cert: "/path/to/cert.pem"
  tls_key: "/path/to/key.pem"

database:
  path: "/data/verification.db"
  backup_interval: "24h"
  backup_path: "/backup/"

security:
  max_failed_attempts: 5
  ban_duration: "1h"
  session_timeout: "30m"
  rate_limit: 100

logging:
  level: "info"
  file: "/logs/verification.log"
  max_size: 100
  max_backups: 7
```

### 7.3 性能优化策略

#### 7.3.1 数据库优化
- 添加适当索引
- 定期清理过期数据
- 数据库连接池管理
- 查询语句优化

#### 7.3.2 并发优化
- 使用协程池管理连接
- 读写分离
- 缓存热点数据
- 异步日志写入