package sdk

import (
	"time"
	"context"
)

// VerificationClient 验证客户端接口
type VerificationClient interface {
	// 连接管理
	Connect(ctx context.Context, serverAddr string, config *ClientConfig) error
	Disconnect() error
	IsConnected() bool
	GetConnectionInfo() *ConnectionInfo

	// 认证功能
	AuthenticateUser(username, password string) (*AuthResult, error)
	AuthenticateCard(cardNumber, cardPassword string) (*AuthResult, error)
	CheckAuthStatus() (*StatusResult, error)
	Logout() error

	// 会话管理
	RefreshSession() (*AuthResult, error)
	GetSessionInfo() *SessionInfo

	// 心跳保持
	StartHeartbeat() error
	StopHeartbeat() error

	// 事件处理
	SetEventHandler(handler EventHandler)
	RemoveEventHandler()

	// 错误处理
	GetLastError() error
}

// ClientConfig 客户端配置
type ClientConfig struct {
	// 连接配置
	ConnectTimeout    time.Duration `json:"connect_timeout"`
	ReadTimeout       time.Duration `json:"read_timeout"`
	WriteTimeout      time.Duration `json:"write_timeout"`
	HeartbeatInterval time.Duration `json:"heartbeat_interval"`
	
	// TLS配置
	TLSConfig *TLSClientConfig `json:"tls_config"`
	
	// 重连配置
	AutoReconnect     bool          `json:"auto_reconnect"`
	ReconnectInterval time.Duration `json:"reconnect_interval"`
	MaxReconnectTries int           `json:"max_reconnect_tries"`
	
	// 其他配置
	UserAgent         string `json:"user_agent"`
	Debug             bool   `json:"debug"`
}

// TLSClientConfig TLS客户端配置
type TLSClientConfig struct {
	ServerName         string `json:"server_name"`
	InsecureSkipVerify bool   `json:"insecure_skip_verify"`
	CertFile           string `json:"cert_file"`
	KeyFile            string `json:"key_file"`
	CAFile             string `json:"ca_file"`
}

// DefaultClientConfig 默认客户端配置
func DefaultClientConfig() *ClientConfig {
	return &ClientConfig{
		ConnectTimeout:    30 * time.Second,
		ReadTimeout:       30 * time.Second,
		WriteTimeout:      30 * time.Second,
		HeartbeatInterval: 30 * time.Second,
		AutoReconnect:     true,
		ReconnectInterval: 5 * time.Second,
		MaxReconnectTries: 3,
		UserAgent:         "QwebSDK/1.0",
		Debug:             false,
	}
}

// ConnectionInfo 连接信息
type ConnectionInfo struct {
	ServerAddr    string    `json:"server_addr"`
	LocalAddr     string    `json:"local_addr"`
	ConnectedAt   time.Time `json:"connected_at"`
	LastActivity  time.Time `json:"last_activity"`
	TLSVersion    string    `json:"tls_version"`
	CipherSuite   string    `json:"cipher_suite"`
}

// AuthResult 认证结果
type AuthResult struct {
	Success      bool      `json:"success"`
	SessionToken string    `json:"session_token"`
	ExpiresAt    time.Time `json:"expires_at"`
	UserInfo     *UserInfo `json:"user_info"`
	Message      string    `json:"message"`
	ErrorCode    string    `json:"error_code"`
}

// UserInfo 用户信息
type UserInfo struct {
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	Level    int    `json:"level"`
	AgentID  *uint  `json:"agent_id"`
}

// StatusResult 状态结果
type StatusResult struct {
	IsAuthenticated bool      `json:"is_authenticated"`
	SessionValid    bool      `json:"session_valid"`
	ExpiresAt       time.Time `json:"expires_at"`
	ServerTime      time.Time `json:"server_time"`
	UserInfo        *UserInfo `json:"user_info"`
}

// SessionInfo 会话信息
type SessionInfo struct {
	SessionToken string    `json:"session_token"`
	UserID       uint      `json:"user_id"`
	CreatedAt    time.Time `json:"created_at"`
	ExpiresAt    time.Time `json:"expires_at"`
	LastUsed     time.Time `json:"last_used"`
}

// EventHandler 事件处理器接口
type EventHandler interface {
	OnConnected(info *ConnectionInfo)
	OnDisconnected(reason string)
	OnAuthSuccess(result *AuthResult)
	OnAuthFailed(reason string, errorCode string)
	OnSessionExpired()
	OnHeartbeat()
	OnError(err error)
	OnMessage(msgType string, data interface{})
}

// ErrorCode 错误代码常量
const (
	ErrCodeNetworkError     = "NETWORK_ERROR"
	ErrCodeTimeout          = "TIMEOUT"
	ErrCodeAuthFailed       = "AUTH_FAILED"
	ErrCodeSessionExpired   = "SESSION_EXPIRED"
	ErrCodeInvalidRequest   = "INVALID_REQUEST"
	ErrCodeServerError      = "SERVER_ERROR"
	ErrCodePermissionDenied = "PERMISSION_DENIED"
	ErrCodeTLSError         = "TLS_ERROR"
	ErrCodeProtocolError    = "PROTOCOL_ERROR"
)

// ClientError 客户端错误
type ClientError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details"`
}

func (e *ClientError) Error() string {
	return e.Message
}

// NewClientError 创建客户端错误
func NewClientError(code, message, details string) *ClientError {
	return &ClientError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// 便捷错误创建函数
func NewNetworkError(details string) *ClientError {
	return NewClientError(ErrCodeNetworkError, "Network error", details)
}

func NewTimeoutError(details string) *ClientError {
	return NewClientError(ErrCodeTimeout, "Operation timeout", details)
}

func NewAuthError(details string) *ClientError {
	return NewClientError(ErrCodeAuthFailed, "Authentication failed", details)
}

func NewSessionExpiredError() *ClientError {
	return NewClientError(ErrCodeSessionExpired, "Session expired", "")
}

func NewProtocolError(details string) *ClientError {
	return NewClientError(ErrCodeProtocolError, "Protocol error", details)
}

// Message 消息定义
type Message struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	ID        string      `json:"id,omitempty"`
	Timestamp int64       `json:"timestamp"`
}

// MessageType 消息类型常量
const (
	MsgTypeAuth         = "auth"
	MsgTypeAuthResp     = "auth_response"
	MsgTypeHeartbeat    = "heartbeat"
	MsgTypeHeartbeatResp = "heartbeat_response"
	MsgTypeStatus       = "status"
	MsgTypeStatusResp   = "status_response"
	MsgTypeLogout       = "logout"
	MsgTypeLogoutResp   = "logout_response"
	MsgTypeError        = "error"
	MsgTypeDisconnect   = "disconnect"
)

// AuthRequest 认证请求
type AuthRequest struct {
	Type         string `json:"type"`           // "user_auth" 或 "card_auth"
	Timestamp    int64  `json:"timestamp"`      // 时间戳
	Nonce        string `json:"nonce"`          // 随机数
	Username     string `json:"username,omitempty"`     // 用户名
	Password     string `json:"password,omitempty"`     // 密码
	CardNumber   string `json:"card_number,omitempty"`  // 卡号
	CardPassword string `json:"card_password,omitempty"` // 卡密
	Signature    string `json:"signature"`      // 消息签名
}

// HeartbeatRequest 心跳请求
type HeartbeatRequest struct {
	Timestamp int64 `json:"timestamp"`
	ClientID  string `json:"client_id"`
}

// HeartbeatResponse 心跳响应
type HeartbeatResponse struct {
	Status     string `json:"status"`
	ServerTime int64  `json:"server_time"`
}

// ClientInfo 客户端信息
type ClientInfo struct {
	Version    string `json:"version"`
	Platform   string `json:"platform"`
	Language   string `json:"language"`
	UserAgent  string `json:"user_agent"`
}

// GetDefaultClientInfo 获取默认客户端信息
func GetDefaultClientInfo() *ClientInfo {
	return &ClientInfo{
		Version:   "1.0.0",
		Platform:  "unknown",
		Language:  "go",
		UserAgent: "QwebSDK/1.0",
	}
}

// ClientState 客户端状态
type ClientState int

const (
	StateDisconnected ClientState = iota
	StateConnecting
	StateConnected
	StateAuthenticating
	StateAuthenticated
	StateError
)

func (s ClientState) String() string {
	switch s {
	case StateDisconnected:
		return "disconnected"
	case StateConnecting:
		return "connecting"
	case StateConnected:
		return "connected"
	case StateAuthenticating:
		return "authenticating"
	case StateAuthenticated:
		return "authenticated"
	case StateError:
		return "error"
	default:
		return "unknown"
	}
}

// ClientMetrics 客户端指标
type ClientMetrics struct {
	ConnectionCount      int64         `json:"connection_count"`
	ReconnectionCount    int64         `json:"reconnection_count"`
	AuthSuccessCount     int64         `json:"auth_success_count"`
	AuthFailureCount     int64         `json:"auth_failure_count"`
	MessagesSent         int64         `json:"messages_sent"`
	MessagesReceived     int64         `json:"messages_received"`
	BytesSent            int64         `json:"bytes_sent"`
	BytesReceived        int64         `json:"bytes_received"`
	LastConnectedAt      time.Time     `json:"last_connected_at"`
	TotalConnectionTime  time.Duration `json:"total_connection_time"`
	AvgResponseTime      time.Duration `json:"avg_response_time"`
	ErrorCount           int64         `json:"error_count"`
}

// ValidationResult 验证结果
type ValidationResult struct {
	IsValid bool   `json:"is_valid"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

// ClientOption 客户端选项
type ClientOption func(*ClientConfig)

// WithTimeout 设置超时时间
func WithTimeout(timeout time.Duration) ClientOption {
	return func(config *ClientConfig) {
		config.ConnectTimeout = timeout
		config.ReadTimeout = timeout
		config.WriteTimeout = timeout
	}
}

// WithTLS 设置TLS配置
func WithTLS(tlsConfig *TLSClientConfig) ClientOption {
	return func(config *ClientConfig) {
		config.TLSConfig = tlsConfig
	}
}

// WithAutoReconnect 设置自动重连
func WithAutoReconnect(enable bool, interval time.Duration, maxTries int) ClientOption {
	return func(config *ClientConfig) {
		config.AutoReconnect = enable
		config.ReconnectInterval = interval
		config.MaxReconnectTries = maxTries
	}
}

// WithHeartbeat 设置心跳间隔
func WithHeartbeat(interval time.Duration) ClientOption {
	return func(config *ClientConfig) {
		config.HeartbeatInterval = interval
	}
}

// WithUserAgent 设置用户代理
func WithUserAgent(userAgent string) ClientOption {
	return func(config *ClientConfig) {
		config.UserAgent = userAgent
	}
}

// WithDebug 设置调试模式
func WithDebug(debug bool) ClientOption {
	return func(config *ClientConfig) {
		config.Debug = debug
	}
}

// ApplyOptions 应用选项
func ApplyOptions(config *ClientConfig, options ...ClientOption) {
	for _, option := range options {
		option(config)
	}
}