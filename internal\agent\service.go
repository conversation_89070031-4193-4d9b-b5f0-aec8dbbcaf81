package agent

import (
	"fmt"
	"time"

	"qweb-verification/internal/database"
	"qweb-verification/internal/models"
	"qweb-verification/internal/security"
)

// AgentService 代理商服务接口
type AgentService interface {
	// 代理商管理
	CreateAgent(parentID *uint, username, password string, level int, commissionRate float64) (*models.Agent, error)
	GetAgent(id uint) (*models.Agent, error)
	GetAgentByUsername(username string) (*models.Agent, error)
	UpdateAgent(agent *models.Agent) error
	DeleteAgent(id uint) error
	ListAgents(parentID *uint, offset, limit int) ([]*models.Agent, int64, error)
	
	// 层级管理
	GetAgentHierarchy(agentID uint) ([]*models.Agent, error)
	GetAgentChildren(agentID uint) ([]*models.Agent, error)
	GetAgentPath(agentID uint) ([]*models.Agent, error)
	ValidateAgentLevel(parentID *uint, level int) error
	
	// 权限管理
	CheckAgentPermission(agentID uint, permission string) bool
	GetAgentPermissions(agentID uint) []string
	
	// 业绩统计
	GetAgentStats(agentID uint, startTime, endTime time.Time) (*AgentStats, error)
	GetAgentCommissions(agentID uint, offset, limit int) ([]*models.Commission, int64, error)
	
	// 卡号管理
	GenerateCards(agentID uint, count int, durationDays int, price float64) ([]*models.Card, error)
	GetAgentCards(agentID uint, status int, offset, limit int) ([]*models.Card, int64, error)
	
	// 佣金计算
	CalculateCommissions(cardID uint) error
}

// AgentStats 代理商统计信息
type AgentStats struct {
	TotalSales       float64 `json:"total_sales"`        // 总销售额
	TotalCommissions float64 `json:"total_commissions"`  // 总佣金
	DirectSales      float64 `json:"direct_sales"`       // 直接销售额
	IndirectSales    float64 `json:"indirect_sales"`     // 间接销售额
	CardsSold        int64   `json:"cards_sold"`         // 售出卡号数量
	ActiveUsers      int64   `json:"active_users"`       // 活跃用户数
	ChildAgents      int64   `json:"child_agents"`       // 下级代理商数量
}

// AgentServiceImpl 代理商服务实现
type AgentServiceImpl struct {
	repo      database.Repository
	encryptor security.Encryptor
}

// NewAgentService 创建代理商服务
func NewAgentService(repo database.Repository, encryptor security.Encryptor) AgentService {
	return &AgentServiceImpl{
		repo:      repo,
		encryptor: encryptor,
	}
}

// CreateAgent 创建代理商
func (as *AgentServiceImpl) CreateAgent(parentID *uint, username, password string, level int, commissionRate float64) (*models.Agent, error) {
	// 验证用户名是否已存在
	if _, err := as.repo.GetAgentByUsername(username); err == nil {
		return nil, fmt.Errorf("username already exists")
	}

	// 验证代理商层级
	if err := as.ValidateAgentLevel(parentID, level); err != nil {
		return nil, err
	}

	// 生成密码盐值和哈希
	salt := as.encryptor.GenerateSalt()
	passwordHash := as.encryptor.HashPassword(password, salt)

	agent := &models.Agent{
		Username:       username,
		PasswordHash:   passwordHash,
		Salt:          salt,
		ParentID:      parentID,
		Level:         level,
		CommissionRate: commissionRate,
		Status:        models.AgentStatusEnabled,
	}

	if err := as.repo.CreateAgent(agent); err != nil {
		return nil, fmt.Errorf("failed to create agent: %w", err)
	}

	return agent, nil
}

// GetAgent 获取代理商
func (as *AgentServiceImpl) GetAgent(id uint) (*models.Agent, error) {
	return as.repo.GetAgentByID(id)
}

// GetAgentByUsername 根据用户名获取代理商
func (as *AgentServiceImpl) GetAgentByUsername(username string) (*models.Agent, error) {
	return as.repo.GetAgentByUsername(username)
}

// UpdateAgent 更新代理商
func (as *AgentServiceImpl) UpdateAgent(agent *models.Agent) error {
	return as.repo.UpdateAgent(agent)
}

// DeleteAgent 删除代理商
func (as *AgentServiceImpl) DeleteAgent(id uint) error {
	// 检查是否有下级代理商
	children, err := as.repo.GetAgentChildren(id)
	if err != nil {
		return fmt.Errorf("failed to check child agents: %w", err)
	}
	
	if len(children) > 0 {
		return fmt.Errorf("cannot delete agent with child agents")
	}

	// 检查是否有相关的卡号
	filters := map[string]interface{}{"agent_id": id}
	cards, _, err := as.repo.ListCards(0, 1, filters)
	if err != nil {
		return fmt.Errorf("failed to check agent cards: %w", err)
	}
	
	if len(cards) > 0 {
		return fmt.Errorf("cannot delete agent with existing cards")
	}

	return as.repo.DeleteAgent(id)
}

// ListAgents 列出代理商
func (as *AgentServiceImpl) ListAgents(parentID *uint, offset, limit int) ([]*models.Agent, int64, error) {
	filters := make(map[string]interface{})
	if parentID != nil {
		filters["parent_id"] = *parentID
	}
	
	return as.repo.ListAgents(offset, limit, filters)
}

// GetAgentHierarchy 获取代理商层级结构
func (as *AgentServiceImpl) GetAgentHierarchy(agentID uint) ([]*models.Agent, error) {
	var hierarchy []*models.Agent
	
	// 递归获取所有下级代理商
	if err := as.buildHierarchy(agentID, &hierarchy); err != nil {
		return nil, err
	}
	
	return hierarchy, nil
}

// buildHierarchy 递归构建层级结构
func (as *AgentServiceImpl) buildHierarchy(agentID uint, hierarchy *[]*models.Agent) error {
	agent, err := as.repo.GetAgentByID(agentID)
	if err != nil {
		return err
	}
	
	*hierarchy = append(*hierarchy, agent)
	
	children, err := as.repo.GetAgentChildren(agentID)
	if err != nil {
		return err
	}
	
	for _, child := range children {
		if err := as.buildHierarchy(child.ID, hierarchy); err != nil {
			return err
		}
	}
	
	return nil
}

// GetAgentChildren 获取直接下级代理商
func (as *AgentServiceImpl) GetAgentChildren(agentID uint) ([]*models.Agent, error) {
	return as.repo.GetAgentChildren(agentID)
}

// GetAgentPath 获取代理商路径（从根到当前代理商）
func (as *AgentServiceImpl) GetAgentPath(agentID uint) ([]*models.Agent, error) {
	var path []*models.Agent
	
	currentID := agentID
	for currentID != 0 {
		agent, err := as.repo.GetAgentByID(currentID)
		if err != nil {
			return nil, err
		}
		
		path = append([]*models.Agent{agent}, path...) // 添加到开头
		
		if agent.ParentID == nil {
			break
		}
		currentID = *agent.ParentID
	}
	
	return path, nil
}

// ValidateAgentLevel 验证代理商层级
func (as *AgentServiceImpl) ValidateAgentLevel(parentID *uint, level int) error {
	if level < models.AgentLevelPrimary || level > models.AgentLevelTertiary {
		return fmt.Errorf("invalid agent level: %d", level)
	}
	
	if parentID == nil {
		// 顶级代理商必须是一级
		if level != models.AgentLevelPrimary {
			return fmt.Errorf("root agent must be level 1")
		}
		return nil
	}
	
	// 检查上级代理商
	parent, err := as.repo.GetAgentByID(*parentID)
	if err != nil {
		return fmt.Errorf("parent agent not found: %w", err)
	}
	
	if !parent.IsActive() {
		return fmt.Errorf("parent agent is not active")
	}
	
	// 子级别必须大于父级别
	if level <= parent.Level {
		return fmt.Errorf("child agent level must be greater than parent level")
	}
	
	return nil
}

// CheckAgentPermission 检查代理商权限
func (as *AgentServiceImpl) CheckAgentPermission(agentID uint, permission string) bool {
	agent, err := as.repo.GetAgentByID(agentID)
	if err != nil || !agent.IsActive() {
		return false
	}
	
	// 根据代理商级别判断权限
	switch permission {
	case "create_agent":
		return agent.Level < models.AgentLevelTertiary
	case "generate_cards":
		return true
	case "view_stats":
		return true
	case "manage_users":
		return agent.Level <= models.AgentLevelSecondary
	default:
		return false
	}
}

// GetAgentPermissions 获取代理商权限列表
func (as *AgentServiceImpl) GetAgentPermissions(agentID uint) []string {
	agent, err := as.repo.GetAgentByID(agentID)
	if err != nil || !agent.IsActive() {
		return []string{}
	}
	
	var permissions []string
	
	// 基本权限
	permissions = append(permissions, "view_stats", "generate_cards")
	
	// 根据级别添加权限
	if agent.Level < models.AgentLevelTertiary {
		permissions = append(permissions, "create_agent")
	}
	
	if agent.Level <= models.AgentLevelSecondary {
		permissions = append(permissions, "manage_users")
	}
	
	return permissions
}

// GetAgentStats 获取代理商统计信息
func (as *AgentServiceImpl) GetAgentStats(agentID uint, startTime, endTime time.Time) (*AgentStats, error) {
	stats := &AgentStats{}
	
	// 获取所有下级代理商
	hierarchy, err := as.GetAgentHierarchy(agentID)
	if err != nil {
		return nil, err
	}
	
	var agentIDs []uint
	for _, agent := range hierarchy {
		agentIDs = append(agentIDs, agent.ID)
	}
	
	// 计算销售额和佣金（这里简化实现，实际应该查询数据库）
	// 由于模型复杂，这里提供基本框架
	
	// 获取直接下级数量
	children, err := as.GetAgentChildren(agentID)
	if err != nil {
		return nil, err
	}
	stats.ChildAgents = int64(len(children))
	
	return stats, nil
}

// GetAgentCommissions 获取代理商佣金记录
func (as *AgentServiceImpl) GetAgentCommissions(agentID uint, offset, limit int) ([]*models.Commission, int64, error) {
	return as.repo.GetAgentCommissions(agentID, offset, limit)
}

// GenerateCards 生成卡号
func (as *AgentServiceImpl) GenerateCards(agentID uint, count int, durationDays int, price float64) ([]*models.Card, error) {
	// 验证代理商权限
	if !as.CheckAgentPermission(agentID, "generate_cards") {
		return nil, fmt.Errorf("insufficient permission to generate cards")
	}
	
	var cards []*models.Card
	
	for i := 0; i < count; i++ {
		// 生成卡号和卡密
		cardNumber, err := as.generateCardNumber()
		if err != nil {
			return nil, fmt.Errorf("failed to generate card number: %w", err)
		}
		
		cardPassword, err := as.generateCardPassword()
		if err != nil {
			return nil, fmt.Errorf("failed to generate card password: %w", err)
		}
		
		card := &models.Card{
			CardNumber:   cardNumber,
			CardPassword: cardPassword,
			DurationDays: durationDays,
			Price:        price,
			AgentID:      agentID,
			Status:       models.CardStatusUnused,
		}
		
		cards = append(cards, card)
	}
	
	// 批量插入
	if err := as.repo.CreateCardsBatch(cards); err != nil {
		return nil, fmt.Errorf("failed to create cards: %w", err)
	}
	
	return cards, nil
}

// GetAgentCards 获取代理商的卡号
func (as *AgentServiceImpl) GetAgentCards(agentID uint, status int, offset, limit int) ([]*models.Card, int64, error) {
	filters := map[string]interface{}{
		"agent_id": agentID,
	}
	
	if status >= 0 {
		filters["status"] = status
	}
	
	return as.repo.ListCards(offset, limit, filters)
}

// CalculateCommissions 计算佣金
func (as *AgentServiceImpl) CalculateCommissions(cardID uint) error {
	// 获取卡号信息
	card, err := as.repo.GetCardByID(cardID)
	if err != nil {
		return fmt.Errorf("failed to get card: %w", err)
	}
	
	if card.Status != models.CardStatusUsed {
		return fmt.Errorf("card is not used, cannot calculate commissions")
	}
	
	// 获取代理商路径
	agentPath, err := as.GetAgentPath(card.AgentID)
	if err != nil {
		return fmt.Errorf("failed to get agent path: %w", err)
	}
	
	// 计算各级佣金
	for i, agent := range agentPath {
		var commissionType string
		var rate float64
		
		if i == len(agentPath)-1 {
			// 直接代理商
			commissionType = models.CommissionTypeDirect
			rate = agent.CommissionRate
		} else {
			// 间接代理商
			commissionType = models.CommissionTypeIndirect
			// 间接佣金率可以设置为直接佣金率的一定比例
			rate = agent.CommissionRate * 0.5
		}
		
		amount := card.Price * rate
		
		commission := &models.Commission{
			AgentID: agent.ID,
			CardID:  card.ID,
			Amount:  amount,
			Rate:    rate,
			Type:    commissionType,
			Status:  models.CommissionStatusPending,
		}
		
		if err := as.repo.CreateCommission(commission); err != nil {
			return fmt.Errorf("failed to create commission for agent %d: %w", agent.ID, err)
		}
	}
	
	return nil
}

// generateCardNumber 生成卡号
func (as *AgentServiceImpl) generateCardNumber() (string, error) {
	// 生成16位卡号
	prefix := "QW"
	suffix, err := security.GenerateSecureToken(7) // 14位随机字符
	if err != nil {
		return "", err
	}
	
	return prefix + suffix, nil
}

// generateCardPassword 生成卡密
func (as *AgentServiceImpl) generateCardPassword() (string, error) {
	// 生成12位卡密
	return security.GenerateSecureToken(6) // 12位随机字符
}