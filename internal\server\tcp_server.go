package server

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net"
	"sync"
	"time"

	"qweb-verification/internal/auth"
	"qweb-verification/internal/database"
	"qweb-verification/internal/models"
	"qweb-verification/internal/security"
)

// Connection 连接信息
type Connection struct {
	ID           string
	Conn         net.Conn
	RemoteAddr   string
	UserID       *uint
	SessionToken string
	LastActivity time.Time
	IsAuthenticated bool
	UserAgent    string
	mu           sync.RWMutex
}

// Message 消息结构
type Message struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	ID        string      `json:"id,omitempty"`
	Timestamp int64       `json:"timestamp"`
}

// MessageType 消息类型
const (
	MsgTypeAuth      = "auth"
	MsgTypeAuthResp  = "auth_response"
	MsgTypeHeartbeat = "heartbeat"
	MsgTypeHeartbeatResp = "heartbeat_response"
	MsgTypeError     = "error"
	MsgTypeDisconnect = "disconnect"
)

// ConnectionManager 连接管理器
type ConnectionManager struct {
	connections map[string]*Connection
	mu          sync.RWMutex
	maxConns    int
	timeout     time.Duration
}

// NewConnectionManager 创建连接管理器
func NewConnectionManager(maxConns int, timeout time.Duration) *ConnectionManager {
	cm := &ConnectionManager{
		connections: make(map[string]*Connection),
		maxConns:    maxConns,
		timeout:     timeout,
	}

	// 启动清理协程
	go cm.cleanupRoutine()

	return cm
}

// AddConnection 添加连接
func (cm *ConnectionManager) AddConnection(conn *Connection) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	if len(cm.connections) >= cm.maxConns {
		return fmt.Errorf("maximum connection limit reached")
	}

	cm.connections[conn.ID] = conn
	return nil
}

// RemoveConnection 移除连接
func (cm *ConnectionManager) RemoveConnection(id string) {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	if conn, exists := cm.connections[id]; exists {
		conn.Conn.Close()
		delete(cm.connections, id)
	}
}

// GetConnection 获取连接
func (cm *ConnectionManager) GetConnection(id string) (*Connection, bool) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	conn, exists := cm.connections[id]
	return conn, exists
}

// GetConnectionCount 获取连接数
func (cm *ConnectionManager) GetConnectionCount() int {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	return len(cm.connections)
}

// GetUserConnections 获取用户的所有连接
func (cm *ConnectionManager) GetUserConnections(userID uint) []*Connection {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	var userConns []*Connection
	for _, conn := range cm.connections {
		if conn.UserID != nil && *conn.UserID == userID {
			userConns = append(userConns, conn)
		}
	}

	return userConns
}

// cleanupRoutine 清理超时连接
func (cm *ConnectionManager) cleanupRoutine() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		cm.cleanupTimeoutConnections()
	}
}

// cleanupTimeoutConnections 清理超时连接
func (cm *ConnectionManager) cleanupTimeoutConnections() {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	cutoff := time.Now().Add(-cm.timeout)
	for id, conn := range cm.connections {
		if conn.LastActivity.Before(cutoff) {
			conn.Conn.Close()
			delete(cm.connections, id)
		}
	}
}

// TCPServer TCP服务器
type TCPServer struct {
	listener       net.Listener
	connManager    *ConnectionManager
	authService    *auth.AuthenticationService
	securityMgr    *security.SecurityManager
	repo           database.Repository
	config         *ServerConfig
	shutdownCh     chan struct{}
	wg             sync.WaitGroup
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port           int           `yaml:"port"`
	MaxConnections int           `yaml:"max_connections"`
	ConnTimeout    time.Duration `yaml:"connection_timeout"`
	HeartbeatInterval time.Duration `yaml:"heartbeat_interval"`
	TLS            *security.TLSConfig `yaml:"tls"`
}

// NewTCPServer 创建TCP服务器
func NewTCPServer(config *ServerConfig, authService *auth.AuthenticationService, securityMgr *security.SecurityManager, repo database.Repository) *TCPServer {
	if config == nil {
		config = &ServerConfig{
			Port:           9999,
			MaxConnections: 1000,
			ConnTimeout:    5 * time.Minute,
			HeartbeatInterval: 30 * time.Second,
		}
	}

	connManager := NewConnectionManager(config.MaxConnections, config.ConnTimeout)

	return &TCPServer{
		connManager: connManager,
		authService: authService,
		securityMgr: securityMgr,
		repo:        repo,
		config:      config,
		shutdownCh:  make(chan struct{}),
	}
}

// Start 启动服务器
func (s *TCPServer) Start() error {
	// 获取TLS配置
	tlsConfig, err := s.securityMgr.GetTLSManager().GetServerTLSConfig()
	if err != nil {
		return fmt.Errorf("failed to get TLS config: %w", err)
	}

	// 创建监听器
	addr := fmt.Sprintf(":%d", s.config.Port)
	listener, err := tls.Listen("tcp", addr, tlsConfig)
	if err != nil {
		return fmt.Errorf("failed to listen on %s: %w", addr, err)
	}

	s.listener = listener
	fmt.Printf("TCP server listening on %s\n", addr)

	// 接受连接
	go s.acceptLoop()

	return nil
}

// Stop 停止服务器
func (s *TCPServer) Stop() error {
	close(s.shutdownCh)
	
	if s.listener != nil {
		s.listener.Close()
	}

	// 等待所有协程结束
	s.wg.Wait()

	return nil
}

// acceptLoop 接受连接循环
func (s *TCPServer) acceptLoop() {
	for {
		select {
		case <-s.shutdownCh:
			return
		default:
			conn, err := s.listener.Accept()
			if err != nil {
				select {
				case <-s.shutdownCh:
					return
				default:
					fmt.Printf("Accept error: %v\n", err)
					continue
				}
			}

			s.wg.Add(1)
			go s.handleConnection(conn)
		}
	}
}

// handleConnection 处理连接
func (s *TCPServer) handleConnection(conn net.Conn) {
	defer s.wg.Done()
	defer conn.Close()

	// 获取客户端IP
	clientIP := security.GetClientIP(conn.RemoteAddr().String(), nil)

	// 检查IP访问权限
	if err := s.securityMgr.CheckIPAccess(clientIP); err != nil {
		fmt.Printf("IP access denied for %s: %v\n", clientIP, err)
		return
	}

	// 创建连接对象
	connID, _ := security.GenerateSecureToken(16)
	connection := &Connection{
		ID:           connID,
		Conn:         conn,
		RemoteAddr:   clientIP,
		LastActivity: time.Now(),
		IsAuthenticated: false,
	}

	// 添加到连接管理器
	if err := s.connManager.AddConnection(connection); err != nil {
		fmt.Printf("Failed to add connection: %v\n", err)
		return
	}

	defer s.connManager.RemoveConnection(connID)

	// 设置连接超时
	conn.SetDeadline(time.Now().Add(s.config.ConnTimeout))

	// 处理消息循环
	s.messageLoop(connection)
}

// messageLoop 消息处理循环
func (s *TCPServer) messageLoop(conn *Connection) {
	decoder := json.NewDecoder(conn.Conn)
	encoder := json.NewEncoder(conn.Conn)

	// 启动心跳检测
	heartbeatTicker := time.NewTicker(s.config.HeartbeatInterval)
	defer heartbeatTicker.Stop()

	for {
		select {
		case <-s.shutdownCh:
			return
		case <-heartbeatTicker.C:
			// 发送心跳
			if err := s.sendHeartbeat(encoder, conn); err != nil {
				fmt.Printf("Failed to send heartbeat to %s: %v\n", conn.ID, err)
				return
			}
		default:
			// 设置读取超时
			conn.Conn.SetReadDeadline(time.Now().Add(30 * time.Second))

			var msg Message
			if err := decoder.Decode(&msg); err != nil {
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					continue // 读取超时，继续循环
				}
				fmt.Printf("Failed to decode message from %s: %v\n", conn.ID, err)
				return
			}

			// 更新活动时间
			conn.mu.Lock()
			conn.LastActivity = time.Now()
			conn.mu.Unlock()

			// 处理消息
			if err := s.handleMessage(encoder, conn, &msg); err != nil {
				fmt.Printf("Failed to handle message from %s: %v\n", conn.ID, err)
				s.sendError(encoder, err.Error())
			}
		}
	}
}

// handleMessage 处理消息
func (s *TCPServer) handleMessage(encoder *json.Encoder, conn *Connection, msg *Message) error {
	switch msg.Type {
	case MsgTypeAuth:
		return s.handleAuthMessage(encoder, conn, msg)
	case MsgTypeHeartbeat:
		return s.handleHeartbeatMessage(encoder, conn, msg)
	default:
		if !conn.IsAuthenticated {
			return fmt.Errorf("connection not authenticated")
		}
		// 处理其他业务消息
		return s.handleBusinessMessage(encoder, conn, msg)
	}
}

// handleAuthMessage 处理认证消息
func (s *TCPServer) handleAuthMessage(encoder *json.Encoder, conn *Connection, msg *Message) error {
	// 解析认证请求
	authReqData, err := json.Marshal(msg.Data)
	if err != nil {
		return fmt.Errorf("failed to marshal auth request: %w", err)
	}

	var authReq auth.AuthRequest
	if err := json.Unmarshal(authReqData, &authReq); err != nil {
		return fmt.Errorf("failed to unmarshal auth request: %w", err)
	}

	var authResp *auth.AuthResponse

	// 根据认证类型处理
	switch authReq.Type {
	case "user_auth":
		authResp, err = s.authService.AuthenticateUser(&authReq, conn.RemoteAddr, conn.UserAgent)
	case "card_auth":
		authResp, err = s.authService.AuthenticateCard(&authReq, conn.RemoteAddr, conn.UserAgent)
	default:
		authResp = auth.CreateErrorResponse("Invalid authentication type", "INVALID_AUTH_TYPE")
	}

	if err != nil {
		authResp = auth.CreateErrorResponse("Authentication failed", "AUTH_FAILED")
		
		// 记录失败尝试
		s.securityMgr.HandleFailedAuth(conn.RemoteAddr)
	}

	// 发送认证响应
	response := Message{
		Type:      MsgTypeAuthResp,
		Data:      authResp,
		ID:        msg.ID,
		Timestamp: time.Now().Unix(),
	}

	if err := encoder.Encode(response); err != nil {
		return fmt.Errorf("failed to send auth response: %w", err)
	}

	// 如果认证成功，更新连接状态
	if authResp.Success {
		conn.mu.Lock()
		conn.IsAuthenticated = true
		conn.SessionToken = authResp.SessionToken
		if authResp.UserInfo != nil {
			conn.UserID = &authResp.UserInfo.UserID
		}
		conn.mu.Unlock()

		// 创建在线用户记录
		if conn.UserID != nil {
			session, _ := s.authService.ValidateSession(authResp.SessionToken)
			if session != nil {
				onlineUser := &models.OnlineUser{
					UserID:     *conn.UserID,
					SessionID:  session.ID,
					IPAddress:  conn.RemoteAddr,
					LoginTime:  time.Now(),
					LastActive: time.Now(),
				}
				s.repo.CreateOnlineUser(onlineUser)
			}
		}
	}

	return nil
}

// handleHeartbeatMessage 处理心跳消息
func (s *TCPServer) handleHeartbeatMessage(encoder *json.Encoder, conn *Connection, msg *Message) error {
	response := Message{
		Type:      MsgTypeHeartbeatResp,
		Data:      map[string]interface{}{"status": "ok"},
		ID:        msg.ID,
		Timestamp: time.Now().Unix(),
	}

	return encoder.Encode(response)
}

// handleBusinessMessage 处理业务消息
func (s *TCPServer) handleBusinessMessage(encoder *json.Encoder, conn *Connection, msg *Message) error {
	// 验证会话是否有效
	if conn.SessionToken != "" {
		session, err := s.authService.ValidateSession(conn.SessionToken)
		if err != nil {
			conn.mu.Lock()
			conn.IsAuthenticated = false
			conn.SessionToken = ""
			conn.UserID = nil
			conn.mu.Unlock()
			
			return fmt.Errorf("session expired or invalid")
		}

		// 刷新会话
		s.authService.RefreshSession(session.SessionToken)
	}

	// 这里可以添加其他业务消息的处理逻辑
	// 例如：查询用户信息、修改密码等

	return nil
}

// sendHeartbeat 发送心跳
func (s *TCPServer) sendHeartbeat(encoder *json.Encoder, conn *Connection) error {
	heartbeat := Message{
		Type:      MsgTypeHeartbeat,
		Data:      map[string]interface{}{"timestamp": time.Now().Unix()},
		Timestamp: time.Now().Unix(),
	}

	return encoder.Encode(heartbeat)
}

// sendError 发送错误消息
func (s *TCPServer) sendError(encoder *json.Encoder, errMsg string) error {
	errorMsg := Message{
		Type:      MsgTypeError,
		Data:      map[string]interface{}{"error": errMsg},
		Timestamp: time.Now().Unix(),
	}

	return encoder.Encode(errorMsg)
}

// GetStats 获取服务器统计信息
func (s *TCPServer) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"total_connections": s.connManager.GetConnectionCount(),
		"max_connections":   s.config.MaxConnections,
		"port":             s.config.Port,
		"uptime":           time.Since(time.Now()).String(), // 简化实现
	}
}

// DisconnectUser 断开用户连接
func (s *TCPServer) DisconnectUser(userID uint) error {
	connections := s.connManager.GetUserConnections(userID)
	for _, conn := range connections {
		s.connManager.RemoveConnection(conn.ID)
	}
	return nil
}