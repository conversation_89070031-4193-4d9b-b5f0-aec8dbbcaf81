package security

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/tls"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/pem"
	"fmt"
	"io/ioutil"
	"math/big"
	"net"
	"time"

	"golang.org/x/time/rate"
)

// TLSConfig TLS配置
type TLSConfig struct {
	CertFile           string   `yaml:"cert_file"`
	KeyFile            string   `yaml:"key_file"`
	CAFile             string   `yaml:"ca_file"`
	MinVersion         string   `yaml:"min_version"`
	MaxVersion         string   `yaml:"max_version"`
	CipherSuites       []string `yaml:"cipher_suites"`
	PreferServerCipher bool     `yaml:"prefer_server_cipher"`
	ClientAuth         string   `yaml:"client_auth"`
}

// TLSManager TLS管理器
type TLSManager struct {
	config *TLSConfig
}

// NewTLSManager 创建新的TLS管理器
func NewTLSManager(config *TLSConfig) *TLSManager {
	if config == nil {
		config = &TLSConfig{
			MinVersion:         "1.2",
			PreferServerCipher: true,
			ClientAuth:         "none",
		}
	}
	return &TLSManager{config: config}
}

// GetServerTLSConfig 获取服务器TLS配置
func (tm *TLSManager) GetServerTLSConfig() (*tls.Config, error) {
	config := &tls.Config{
		MinVersion:               tm.parseVersion(tm.config.MinVersion),
		MaxVersion:               tm.parseVersion(tm.config.MaxVersion),
		PreferServerCipherSuites: tm.config.PreferServerCipher,
		CipherSuites:             tm.parseCipherSuites(tm.config.CipherSuites),
		ClientAuth:               tm.parseClientAuth(tm.config.ClientAuth),
	}

	// 加载服务器证书
	if tm.config.CertFile != "" && tm.config.KeyFile != "" {
		cert, err := tls.LoadX509KeyPair(tm.config.CertFile, tm.config.KeyFile)
		if err != nil {
			return nil, fmt.Errorf("failed to load server certificate: %w", err)
		}
		config.Certificates = []tls.Certificate{cert}
	} else {
		// 生成自签名证书
		cert, err := tm.generateSelfSignedCert()
		if err != nil {
			return nil, fmt.Errorf("failed to generate self-signed certificate: %w", err)
		}
		config.Certificates = []tls.Certificate{cert}
	}

	// 加载CA证书
	if tm.config.CAFile != "" {
		caCert, err := ioutil.ReadFile(tm.config.CAFile)
		if err != nil {
			return nil, fmt.Errorf("failed to read CA certificate: %w", err)
		}

		caCertPool := x509.NewCertPool()
		if !caCertPool.AppendCertsFromPEM(caCert) {
			return nil, fmt.Errorf("failed to parse CA certificate")
		}
		config.ClientCAs = caCertPool
	}

	return config, nil
}

// GetClientTLSConfig 获取客户端TLS配置
func (tm *TLSManager) GetClientTLSConfig(serverName string, insecure bool) (*tls.Config, error) {
	config := &tls.Config{
		ServerName:         serverName,
		InsecureSkipVerify: insecure,
		MinVersion:         tm.parseVersion(tm.config.MinVersion),
		MaxVersion:         tm.parseVersion(tm.config.MaxVersion),
		CipherSuites:       tm.parseCipherSuites(tm.config.CipherSuites),
	}

	// 加载CA证书
	if tm.config.CAFile != "" && !insecure {
		caCert, err := ioutil.ReadFile(tm.config.CAFile)
		if err != nil {
			return nil, fmt.Errorf("failed to read CA certificate: %w", err)
		}

		caCertPool := x509.NewCertPool()
		if !caCertPool.AppendCertsFromPEM(caCert) {
			return nil, fmt.Errorf("failed to parse CA certificate")
		}
		config.RootCAs = caCertPool
	}

	return config, nil
}

// parseVersion 解析TLS版本
func (tm *TLSManager) parseVersion(version string) uint16 {
	switch version {
	case "1.0":
		return tls.VersionTLS10
	case "1.1":
		return tls.VersionTLS11
	case "1.2":
		return tls.VersionTLS12
	case "1.3":
		return tls.VersionTLS13
	default:
		return tls.VersionTLS12 // 默认使用TLS 1.2
	}
}

// parseCipherSuites 解析加密套件
func (tm *TLSManager) parseCipherSuites(suites []string) []uint16 {
	if len(suites) == 0 {
		// 返回推荐的安全加密套件
		return []uint16{
			tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
			tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,
			tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
			tls.TLS_RSA_WITH_AES_256_GCM_SHA384,
			tls.TLS_RSA_WITH_AES_128_GCM_SHA256,
		}
	}

	var cipherSuites []uint16
	for _, suite := range suites {
		if cs := tm.parseCipherSuite(suite); cs != 0 {
			cipherSuites = append(cipherSuites, cs)
		}
	}

	return cipherSuites
}

// parseCipherSuite 解析单个加密套件
func (tm *TLSManager) parseCipherSuite(suite string) uint16 {
	switch suite {
	case "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384":
		return tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
	case "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305":
		return tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305
	case "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256":
		return tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
	case "TLS_RSA_WITH_AES_256_GCM_SHA384":
		return tls.TLS_RSA_WITH_AES_256_GCM_SHA384
	case "TLS_RSA_WITH_AES_128_GCM_SHA256":
		return tls.TLS_RSA_WITH_AES_128_GCM_SHA256
	default:
		return 0
	}
}

// parseClientAuth 解析客户端认证模式
func (tm *TLSManager) parseClientAuth(auth string) tls.ClientAuthType {
	switch auth {
	case "require":
		return tls.RequireAndVerifyClientCert
	case "request":
		return tls.RequestClientCert
	case "verify":
		return tls.VerifyClientCertIfGiven
	default:
		return tls.NoClientCert
	}
}

// generateSelfSignedCert 生成自签名证书
func (tm *TLSManager) generateSelfSignedCert() (tls.Certificate, error) {
	// 生成私钥
	priv, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return tls.Certificate{}, fmt.Errorf("failed to generate private key: %w", err)
	}

	// 创建证书模板
	template := x509.Certificate{
		SerialNumber: big.NewInt(1),
		Subject: pkix.Name{
			Organization:  []string{"Qweb"},
			Country:       []string{"CN"},
			Province:      []string{"Beijing"},
			Locality:      []string{"Beijing"},
			StreetAddress: []string{"自动生成"},
			PostalCode:    []string{"100000"},
		},
		NotBefore:   time.Now(),
		NotAfter:    time.Now().Add(365 * 24 * time.Hour), // 1年有效期
		KeyUsage:    x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature,
		ExtKeyUsage: []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
		IPAddresses: []net.IP{net.IPv4(127, 0, 0, 1), net.IPv6loopback},
		DNSNames:    []string{"localhost"},
	}

	// 生成证书
	certDER, err := x509.CreateCertificate(rand.Reader, &template, &template, &priv.PublicKey, priv)
	if err != nil {
		return tls.Certificate{}, fmt.Errorf("failed to create certificate: %w", err)
	}

	// 编码证书
	certPEM := pem.EncodeToMemory(&pem.Block{Type: "CERTIFICATE", Bytes: certDER})

	// 编码私钥
	privBytes, err := x509.MarshalPKCS8PrivateKey(priv)
	if err != nil {
		return tls.Certificate{}, fmt.Errorf("failed to marshal private key: %w", err)
	}
	privPEM := pem.EncodeToMemory(&pem.Block{Type: "PRIVATE KEY", Bytes: privBytes})

	// 创建TLS证书
	cert, err := tls.X509KeyPair(certPEM, privPEM)
	if err != nil {
		return tls.Certificate{}, fmt.Errorf("failed to create TLS certificate: %w", err)
	}

	return cert, nil
}

// SecurityManager 安全管理器
type SecurityManager struct {
	tlsManager  *TLSManager
	ipFilter    IPFilter
	rateLimiter RateLimiter
	encryptor   Encryptor
	config      *SecurityConfig
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	TLS                        *TLSConfig `yaml:"tls"`
	MaxFailedAttempts          int        `yaml:"max_failed_attempts"`
	BanDuration                string     `yaml:"ban_duration"`
	RateLimit                  float64    `yaml:"rate_limit"`
	RateBurst                  int        `yaml:"rate_burst"`
	SessionTimeout             string     `yaml:"session_timeout"`
	EncryptionKey              string     `yaml:"encryption_key"`
	EnableBruteForceProtection bool       `yaml:"enable_brute_force_protection"`
}

// NewSecurityManager 创建安全管理器
func NewSecurityManager(config *SecurityConfig) (*SecurityManager, error) {
	if config == nil {
		config = &SecurityConfig{
			MaxFailedAttempts:          5,
			BanDuration:                "30m",
			RateLimit:                  10.0,
			RateBurst:                  20,
			SessionTimeout:             "24h",
			EncryptionKey:              "default-encryption-key",
			EnableBruteForceProtection: true,
		}
	}

	tlsManager := NewTLSManager(config.TLS)
	ipFilter := NewIPFilter()
	rateLimiter := NewRateLimiter(rate.Limit(config.RateLimit), config.RateBurst)
	encryptor := NewAESEncryptor(config.EncryptionKey)

	return &SecurityManager{
		tlsManager:  tlsManager,
		ipFilter:    ipFilter,
		rateLimiter: rateLimiter,
		encryptor:   encryptor,
		config:      config,
	}, nil
}

// GetTLSManager 获取TLS管理器
func (sm *SecurityManager) GetTLSManager() *TLSManager {
	return sm.tlsManager
}

// GetIPFilter 获取IP过滤器
func (sm *SecurityManager) GetIPFilter() IPFilter {
	return sm.ipFilter
}

// GetRateLimiter 获取速率限制器
func (sm *SecurityManager) GetRateLimiter() RateLimiter {
	return sm.rateLimiter
}

// GetEncryptor 获取加密器
func (sm *SecurityManager) GetEncryptor() Encryptor {
	return sm.encryptor
}

// CheckIPAccess 检查IP访问权限
func (sm *SecurityManager) CheckIPAccess(ip string) error {
	// 检查IP是否被封禁
	if sm.ipFilter.IsBlocked(ip) {
		return fmt.Errorf("IP %s is blocked", ip)
	}

	// 检查IP是否被允许
	if !sm.ipFilter.IsAllowed(ip) {
		return fmt.Errorf("IP %s is not allowed", ip)
	}

	// 检查速率限制
	if !sm.rateLimiter.Allow(ip) {
		return fmt.Errorf("rate limit exceeded for IP %s", ip)
	}

	return nil
}

// HandleFailedAuth 处理认证失败
func (sm *SecurityManager) HandleFailedAuth(ip string) error {
	if !sm.config.EnableBruteForceProtection {
		return nil
	}

	// 这里应该记录失败次数并在达到阈值时封禁IP
	// 简化实现，直接封禁
	duration, _ := time.ParseDuration(sm.config.BanDuration)
	return sm.ipFilter.BlockIP(ip, duration, "too many failed authentication attempts")
}

// GetSessionTimeout 获取会话超时时间
func (sm *SecurityManager) GetSessionTimeout() time.Duration {
	duration, err := time.ParseDuration(sm.config.SessionTimeout)
	if err != nil {
		return 24 * time.Hour // 默认24小时
	}
	return duration
}
