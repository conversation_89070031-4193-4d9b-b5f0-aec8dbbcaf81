package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// Logger 日志接口
type Logger interface {
	Debug(msg string, fields ...zap.Field)
	Info(msg string, fields ...zap.Field)
	Warn(msg string, fields ...zap.Field)
	Error(msg string, fields ...zap.Field)
	Fatal(msg string, fields ...zap.Field)
	Sync() error
}

// LoggerConfig 日志配置
type LoggerConfig struct {
	Level      string `yaml:"level"`       // debug, info, warn, error
	Format     string `yaml:"format"`      // json, console
	Output     string `yaml:"output"`      // stdout, file, both
	Filename   string `yaml:"filename"`    // 日志文件路径
	MaxSize    int    `yaml:"max_size"`    // 单个文件最大大小(MB)
	MaxBackups int    `yaml:"max_backups"` // 保留的旧文件数量
	MaxAge     int    `yaml:"max_age"`     // 保留天数
	Compress   bool   `yaml:"compress"`    // 是否压缩旧文件
}

// DefaultLoggerConfig 默认日志配置
func DefaultLoggerConfig() *LoggerConfig {
	return &LoggerConfig{
		Level:      "info",
		Format:     "json",
		Output:     "both",
		Filename:   "logs/qweb.log",
		MaxSize:    100, // 100MB
		MaxBackups: 10,
		MaxAge:     30, // 30天
		Compress:   true,
	}
}

// ZapLogger Zap日志实现
type ZapLogger struct {
	logger *zap.Logger
	config *LoggerConfig
}

// NewZapLogger 创建Zap日志器
func NewZapLogger(config *LoggerConfig) (*ZapLogger, error) {
	if config == nil {
		config = DefaultLoggerConfig()
	}

	// 确保日志目录存在
	if config.Output == "file" || config.Output == "both" {
		dir := filepath.Dir(config.Filename)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return nil, fmt.Errorf("failed to create log directory: %w", err)
		}
	}

	// 配置日志级别
	level, err := zapcore.ParseLevel(config.Level)
	if err != nil {
		level = zapcore.InfoLevel
	}

	// 配置编码器
	var encoder zapcore.Encoder
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.TimeKey = "timestamp"
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.CallerKey = "caller"
	encoderConfig.EncodeCaller = zapcore.ShortCallerEncoder

	if config.Format == "console" {
		encoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	}

	// 配置输出
	var core zapcore.Core
	switch config.Output {
	case "stdout":
		core = zapcore.NewCore(encoder, zapcore.AddSync(os.Stdout), level)
	case "file":
		fileWriter := getFileWriter(config)
		core = zapcore.NewCore(encoder, zapcore.AddSync(fileWriter), level)
	case "both":
		fileWriter := getFileWriter(config)
		stdoutCore := zapcore.NewCore(encoder, zapcore.AddSync(os.Stdout), level)
		fileCore := zapcore.NewCore(encoder, zapcore.AddSync(fileWriter), level)
		core = zapcore.NewTee(stdoutCore, fileCore)
	default:
		core = zapcore.NewCore(encoder, zapcore.AddSync(os.Stdout), level)
	}

	logger := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))

	return &ZapLogger{
		logger: logger,
		config: config,
	}, nil
}

// getFileWriter 获取文件写入器
func getFileWriter(config *LoggerConfig) zapcore.WriteSyncer {
	// 这里简化实现，实际项目中应该使用lumberjack进行日志轮转
	file, err := os.OpenFile(config.Filename, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		// 如果无法打开文件，降级到标准输出
		return zapcore.AddSync(os.Stdout)
	}
	return zapcore.AddSync(file)
}

// Debug 记录调试日志
func (zl *ZapLogger) Debug(msg string, fields ...zap.Field) {
	zl.logger.Debug(msg, fields...)
}

// Info 记录信息日志
func (zl *ZapLogger) Info(msg string, fields ...zap.Field) {
	zl.logger.Info(msg, fields...)
}

// Warn 记录警告日志
func (zl *ZapLogger) Warn(msg string, fields ...zap.Field) {
	zl.logger.Warn(msg, fields...)
}

// Error 记录错误日志
func (zl *ZapLogger) Error(msg string, fields ...zap.Field) {
	zl.logger.Error(msg, fields...)
}

// Fatal 记录致命错误日志
func (zl *ZapLogger) Fatal(msg string, fields ...zap.Field) {
	zl.logger.Fatal(msg, fields...)
}

// Sync 同步日志
func (zl *ZapLogger) Sync() error {
	return zl.logger.Sync()
}

// Close 关闭日志器
func (zl *ZapLogger) Close() error {
	return zl.Sync()
}

// 全局日志器
var globalLogger Logger

// InitGlobalLogger 初始化全局日志器
func InitGlobalLogger(config *LoggerConfig) error {
	logger, err := NewZapLogger(config)
	if err != nil {
		return err
	}
	globalLogger = logger
	return nil
}

// GetLogger 获取全局日志器
func GetLogger() Logger {
	if globalLogger == nil {
		// 如果全局日志器未初始化，使用默认配置
		logger, _ := NewZapLogger(nil)
		globalLogger = logger
	}
	return globalLogger
}

// 便捷函数
func Debug(msg string, fields ...zap.Field) {
	GetLogger().Debug(msg, fields...)
}

func Info(msg string, fields ...zap.Field) {
	GetLogger().Info(msg, fields...)
}

func Warn(msg string, fields ...zap.Field) {
	GetLogger().Warn(msg, fields...)
}

func Error(msg string, fields ...zap.Field) {
	GetLogger().Error(msg, fields...)
}

func Fatal(msg string, fields ...zap.Field) {
	GetLogger().Fatal(msg, fields...)
}

// LogFields 常用日志字段
func LogFields() *FieldBuilder {
	return &FieldBuilder{}
}

// FieldBuilder 字段构建器
type FieldBuilder struct {
	fields []zap.Field
}

func (fb *FieldBuilder) String(key, value string) *FieldBuilder {
	fb.fields = append(fb.fields, zap.String(key, value))
	return fb
}

func (fb *FieldBuilder) Int(key string, value int) *FieldBuilder {
	fb.fields = append(fb.fields, zap.Int(key, value))
	return fb
}

func (fb *FieldBuilder) Uint(key string, value uint) *FieldBuilder {
	fb.fields = append(fb.fields, zap.Uint(key, value))
	return fb
}

func (fb *FieldBuilder) Float64(key string, value float64) *FieldBuilder {
	fb.fields = append(fb.fields, zap.Float64(key, value))
	return fb
}

func (fb *FieldBuilder) Bool(key string, value bool) *FieldBuilder {
	fb.fields = append(fb.fields, zap.Bool(key, value))
	return fb
}

func (fb *FieldBuilder) Time(key string, value time.Time) *FieldBuilder {
	fb.fields = append(fb.fields, zap.Time(key, value))
	return fb
}

func (fb *FieldBuilder) Duration(key string, value time.Duration) *FieldBuilder {
	fb.fields = append(fb.fields, zap.Duration(key, value))
	return fb
}

func (fb *FieldBuilder) Error(err error) *FieldBuilder {
	fb.fields = append(fb.fields, zap.Error(err))
	return fb
}

func (fb *FieldBuilder) Build() []zap.Field {
	return fb.fields
}

// ContextLogger 上下文日志器
type ContextLogger struct {
	logger Logger
	fields []zap.Field
}

// WithFields 添加字段
func WithFields(fields ...zap.Field) *ContextLogger {
	return &ContextLogger{
		logger: GetLogger(),
		fields: fields,
	}
}

func (cl *ContextLogger) Debug(msg string, fields ...zap.Field) {
	allFields := append(cl.fields, fields...)
	cl.logger.Debug(msg, allFields...)
}

func (cl *ContextLogger) Info(msg string, fields ...zap.Field) {
	allFields := append(cl.fields, fields...)
	cl.logger.Info(msg, allFields...)
}

func (cl *ContextLogger) Warn(msg string, fields ...zap.Field) {
	allFields := append(cl.fields, fields...)
	cl.logger.Warn(msg, allFields...)
}

func (cl *ContextLogger) Error(msg string, fields ...zap.Field) {
	allFields := append(cl.fields, fields...)
	cl.logger.Error(msg, allFields...)
}

func (cl *ContextLogger) Fatal(msg string, fields ...zap.Field) {
	allFields := append(cl.fields, fields...)
	cl.logger.Fatal(msg, allFields...)
}

// AuditLogger 审计日志
type AuditLogger struct {
	logger Logger
}

// NewAuditLogger 创建审计日志器
func NewAuditLogger(config *LoggerConfig) (*AuditLogger, error) {
	if config == nil {
		config = DefaultLoggerConfig()
		config.Filename = "logs/audit.log"
	}

	logger, err := NewZapLogger(config)
	if err != nil {
		return nil, err
	}

	return &AuditLogger{logger: logger}, nil
}

// LogUserAction 记录用户操作
func (al *AuditLogger) LogUserAction(userID uint, action, resource, ipAddress string, success bool, details map[string]interface{}) {
	fields := []zap.Field{
		zap.Uint("user_id", userID),
		zap.String("action", action),
		zap.String("resource", resource),
		zap.String("ip_address", ipAddress),
		zap.Bool("success", success),
		zap.Time("timestamp", time.Now()),
	}

	if details != nil {
		for key, value := range details {
			fields = append(fields, zap.Any(key, value))
		}
	}

	if success {
		al.logger.Info("User action", fields...)
	} else {
		al.logger.Warn("User action failed", fields...)
	}
}

// LogAgentAction 记录代理商操作
func (al *AuditLogger) LogAgentAction(agentID uint, action, resource, ipAddress string, success bool, details map[string]interface{}) {
	fields := []zap.Field{
		zap.Uint("agent_id", agentID),
		zap.String("action", action),
		zap.String("resource", resource),
		zap.String("ip_address", ipAddress),
		zap.Bool("success", success),
		zap.Time("timestamp", time.Now()),
	}

	if details != nil {
		for key, value := range details {
			fields = append(fields, zap.Any(key, value))
		}
	}

	if success {
		al.logger.Info("Agent action", fields...)
	} else {
		al.logger.Warn("Agent action failed", fields...)
	}
}

// LogSystemEvent 记录系统事件
func (al *AuditLogger) LogSystemEvent(event, component, message string, level string, details map[string]interface{}) {
	fields := []zap.Field{
		zap.String("event", event),
		zap.String("component", component),
		zap.String("message", message),
		zap.Time("timestamp", time.Now()),
	}

	if details != nil {
		for key, value := range details {
			fields = append(fields, zap.Any(key, value))
		}
	}

	switch level {
	case "debug":
		al.logger.Debug("System event", fields...)
	case "info":
		al.logger.Info("System event", fields...)
	case "warn":
		al.logger.Warn("System event", fields...)
	case "error":
		al.logger.Error("System event", fields...)
	default:
		al.logger.Info("System event", fields...)
	}
}