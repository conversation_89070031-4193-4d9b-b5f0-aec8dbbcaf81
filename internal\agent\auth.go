package agent

import (
	"fmt"
	"time"

	"qweb-verification/internal/auth"
	"qweb-verification/internal/database"
	"qweb-verification/internal/models"
	"qweb-verification/internal/security"
)

// AgentAuthService 代理商认证服务
type AgentAuthService struct {
	repo      database.Repository
	encryptor security.Encryptor
	sessionManager auth.SessionManager
}

// NewAgentAuthService 创建代理商认证服务
func NewAgentAuthService(repo database.Repository, encryptor security.Encryptor, sessionManager auth.SessionManager) *AgentAuthService {
	return &AgentAuthService{
		repo:           repo,
		encryptor:      encryptor,
		sessionManager: sessionManager,
	}
}

// AuthenticateAgent 代理商认证
func (aas *AgentAuthService) AuthenticateAgent(username, password, ipAddress, userAgent string) (*AgentAuthResponse, error) {
	// 查找代理商
	agent, err := aas.repo.GetAgentByUsername(username)
	if err != nil {
		return &AgentAuthResponse{
			Success: false,
			Message: "Invalid credentials",
			ErrorCode: "INVALID_CREDENTIALS",
		}, nil
	}

	// 检查代理商状态
	if !agent.IsActive() {
		return &AgentAuthResponse{
			Success: false,
			Message: "Agent account is disabled",
			ErrorCode: "AGENT_DISABLED",
		}, nil
	}

	// 验证密码
	if !aas.encryptor.VerifyPassword(password, agent.PasswordHash, agent.Salt) {
		return &AgentAuthResponse{
			Success: false,
			Message: "Invalid credentials",
			ErrorCode: "INVALID_CREDENTIALS",
		}, nil
	}

	// 创建会话（这里需要适配现有的session系统或创建新的agent session）
	sessionToken, err := security.GenerateSecureToken(32)
	if err != nil {
		return &AgentAuthResponse{
			Success: false,
			Message: "Failed to create session",
			ErrorCode: "SESSION_ERROR",
		}, nil
	}

	// 记录登录日志
	aas.logAgentOperation(agent.ID, models.OpTypeLogin, "Agent login successful", ipAddress, userAgent, true, "")

	agentInfo := &AgentInfo{
		AgentID:        agent.ID,
		Username:       agent.Username,
		Level:          agent.Level,
		CommissionRate: agent.CommissionRate,
		ParentID:       agent.ParentID,
		Permissions:    aas.getAgentPermissions(agent),
	}

	return &AgentAuthResponse{
		Success:      true,
		SessionToken: sessionToken,
		ExpiresAt:    time.Now().Add(24 * time.Hour),
		AgentInfo:    agentInfo,
		Message:      "Authentication successful",
	}, nil
}

// AgentAuthResponse 代理商认证响应
type AgentAuthResponse struct {
	Success      bool       `json:"success"`
	SessionToken string     `json:"session_token,omitempty"`
	ExpiresAt    time.Time  `json:"expires_at,omitempty"`
	AgentInfo    *AgentInfo `json:"agent_info,omitempty"`
	Message      string     `json:"message"`
	ErrorCode    string     `json:"error_code,omitempty"`
}

// AgentInfo 代理商信息
type AgentInfo struct {
	AgentID        uint     `json:"agent_id"`
	Username       string   `json:"username"`
	Level          int      `json:"level"`
	CommissionRate float64  `json:"commission_rate"`
	ParentID       *uint    `json:"parent_id,omitempty"`
	Permissions    []string `json:"permissions"`
}

// getAgentPermissions 获取代理商权限
func (aas *AgentAuthService) getAgentPermissions(agent *models.Agent) []string {
	var permissions []string

	// 基础权限
	permissions = append(permissions, "view_dashboard", "view_stats", "generate_cards", "view_cards")

	// 根据级别分配权限
	switch agent.Level {
	case models.AgentLevelPrimary:
		permissions = append(permissions, 
			"create_secondary_agent", 
			"manage_secondary_agents", 
			"view_all_hierarchy",
			"system_settings",
		)
	case models.AgentLevelSecondary:
		permissions = append(permissions, 
			"create_tertiary_agent", 
			"manage_tertiary_agents",
			"manage_users",
		)
	case models.AgentLevelTertiary:
		permissions = append(permissions, 
			"manage_own_users",
		)
	}

	return permissions
}

// logAgentOperation 记录代理商操作日志
func (aas *AgentAuthService) logAgentOperation(agentID uint, operation, details, ipAddress, userAgent string, success bool, errorMsg string) {
	log := &models.OperationLog{
		AgentID:   &agentID,
		Operation: operation,
		Details:   details,
		IPAddress: ipAddress,
		UserAgent: userAgent,
		Success:   success,
		ErrorMsg:  errorMsg,
	}

	// 忽略日志记录错误
	aas.repo.CreateOperationLog(log)
}

// PermissionManager 权限管理器
type PermissionManager struct {
	repo database.Repository
}

// NewPermissionManager 创建权限管理器
func NewPermissionManager(repo database.Repository) *PermissionManager {
	return &PermissionManager{repo: repo}
}

// CheckPermission 检查权限
func (pm *PermissionManager) CheckPermission(agentID uint, permission string) error {
	agent, err := pm.repo.GetAgentByID(agentID)
	if err != nil {
		return fmt.Errorf("agent not found: %w", err)
	}

	if !agent.IsActive() {
		return fmt.Errorf("agent is not active")
	}

	// 检查具体权限
	switch permission {
	case "create_secondary_agent":
		if agent.Level != models.AgentLevelPrimary {
			return fmt.Errorf("only primary agents can create secondary agents")
		}
	case "create_tertiary_agent":
		if agent.Level != models.AgentLevelSecondary {
			return fmt.Errorf("only secondary agents can create tertiary agents")
		}
	case "system_settings":
		if agent.Level != models.AgentLevelPrimary {
			return fmt.Errorf("only primary agents can access system settings")
		}
	case "manage_users":
		if agent.Level > models.AgentLevelSecondary {
			return fmt.Errorf("insufficient permission to manage users")
		}
	default:
		// 默认权限检查
		if !agent.IsActive() {
			return fmt.Errorf("insufficient permission")
		}
	}

	return nil
}

// CommissionCalculator 佣金计算器
type CommissionCalculator struct {
	repo database.Repository
}

// NewCommissionCalculator 创建佣金计算器
func NewCommissionCalculator(repo database.Repository) *CommissionCalculator {
	return &CommissionCalculator{repo: repo}
}

// CommissionRule 佣金规则
type CommissionRule struct {
	Level          int     `json:"level"`           // 代理商级别
	DirectRate     float64 `json:"direct_rate"`     // 直接佣金比例
	IndirectRate   float64 `json:"indirect_rate"`   // 间接佣金比例
	MinAmount      float64 `json:"min_amount"`      // 最小佣金金额
	MaxAmount      float64 `json:"max_amount"`      // 最大佣金金额
}

// DefaultCommissionRules 默认佣金规则
var DefaultCommissionRules = []CommissionRule{
	{
		Level:        models.AgentLevelPrimary,
		DirectRate:   0.15, // 15%
		IndirectRate: 0.05, // 5%
		MinAmount:    0.01,
		MaxAmount:    1000,
	},
	{
		Level:        models.AgentLevelSecondary,
		DirectRate:   0.10, // 10%
		IndirectRate: 0.03, // 3%
		MinAmount:    0.01,
		MaxAmount:    500,
	},
	{
		Level:        models.AgentLevelTertiary,
		DirectRate:   0.05, // 5%
		IndirectRate: 0.00, // 无间接佣金
		MinAmount:    0.01,
		MaxAmount:    100,
	},
}

// CalculateCommission 计算佣金
func (cc *CommissionCalculator) CalculateCommission(cardID uint) (*CommissionResult, error) {
	// 获取卡号信息
	card, err := cc.repo.GetCardByID(cardID)
	if err != nil {
		return nil, fmt.Errorf("failed to get card: %w", err)
	}

	if card.Status != models.CardStatusUsed {
		return nil, fmt.Errorf("card is not used")
	}

	result := &CommissionResult{
		CardID:      cardID,
		TotalAmount: card.Price,
		Commissions: make([]AgentCommission, 0),
	}

	// 获取代理商链
	agentChain, err := cc.getAgentChain(card.AgentID)
	if err != nil {
		return nil, fmt.Errorf("failed to get agent chain: %w", err)
	}

	// 为每个代理商计算佣金
	for i, agent := range agentChain {
		rule := cc.getCommissionRule(agent.Level)
		
		var rate float64
		var commissionType string
		
		if i == 0 { // 直接代理商
			rate = rule.DirectRate
			commissionType = models.CommissionTypeDirect
		} else { // 间接代理商
			rate = rule.IndirectRate
			commissionType = models.CommissionTypeIndirect
		}

		if rate <= 0 {
			continue // 跳过无佣金的级别
		}

		amount := card.Price * rate
		
		// 应用最小最大限制
		if amount < rule.MinAmount {
			amount = rule.MinAmount
		}
		if amount > rule.MaxAmount {
			amount = rule.MaxAmount
		}

		commission := AgentCommission{
			AgentID: agent.ID,
			Agent:   agent,
			Type:    commissionType,
			Rate:    rate,
			Amount:  amount,
		}

		result.Commissions = append(result.Commissions, commission)
		result.TotalCommission += amount
	}

	return result, nil
}

// CommissionResult 佣金计算结果
type CommissionResult struct {
	CardID          uint              `json:"card_id"`
	TotalAmount     float64           `json:"total_amount"`
	TotalCommission float64           `json:"total_commission"`
	Commissions     []AgentCommission `json:"commissions"`
}

// AgentCommission 代理商佣金
type AgentCommission struct {
	AgentID uint           `json:"agent_id"`
	Agent   *models.Agent  `json:"agent"`
	Type    string         `json:"type"`
	Rate    float64        `json:"rate"`
	Amount  float64        `json:"amount"`
}

// getAgentChain 获取代理商链（从当前代理商到根代理商）
func (cc *CommissionCalculator) getAgentChain(agentID uint) ([]*models.Agent, error) {
	var chain []*models.Agent
	
	currentID := agentID
	for currentID != 0 {
		agent, err := cc.repo.GetAgentByID(currentID)
		if err != nil {
			return nil, err
		}
		
		chain = append(chain, agent)
		
		if agent.ParentID == nil {
			break
		}
		currentID = *agent.ParentID
	}
	
	return chain, nil
}

// getCommissionRule 获取佣金规则
func (cc *CommissionCalculator) getCommissionRule(level int) CommissionRule {
	for _, rule := range DefaultCommissionRules {
		if rule.Level == level {
			return rule
		}
	}
	
	// 返回默认规则
	return CommissionRule{
		Level:        level,
		DirectRate:   0.05,
		IndirectRate: 0.01,
		MinAmount:    0.01,
		MaxAmount:    100,
	}
}

// SaveCommissions 保存佣金记录
func (cc *CommissionCalculator) SaveCommissions(result *CommissionResult) error {
	for _, commission := range result.Commissions {
		commissionRecord := &models.Commission{
			AgentID: commission.AgentID,
			CardID:  result.CardID,
			Amount:  commission.Amount,
			Rate:    commission.Rate,
			Type:    commission.Type,
			Status:  models.CommissionStatusPending,
		}
		
		if err := cc.repo.CreateCommission(commissionRecord); err != nil {
			return fmt.Errorf("failed to save commission for agent %d: %w", commission.AgentID, err)
		}
	}
	
	return nil
}