package test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"

	"qweb-verification/internal/database"
	"qweb-verification/internal/monitor"
	"qweb-verification/internal/security"
	"qweb-verification/internal/web"
)

func TestSecurityMiddleware(t *testing.T) {
	// 创建测试数据库
	dbConfig := &database.Config{
		Path:         ":memory:",
		MaxIdleConns: 10,
		MaxOpenConns: 100,
	}
	
	db, err := database.NewDatabase(dbConfig)
	if err != nil {
		t.Fatalf("Failed to create database: %v", err)
	}
	defer db.Close()

	// 执行数据库迁移
	err = db.Migrate()
	if err != nil {
		t.Fatalf("Failed to migrate database: %v", err)
	}

	// 创建安全管理器
	secConfig := &security.SecurityConfig{
		EncryptionKey: "test-************************************",
	}
	secMgr, err := security.NewSecurityManager(secConfig)
	if err != nil {
		t.Fatalf("Failed to create security manager: %v", err)
	}

	// 创建指标收集器
	repo := database.NewRepository(db.GetDB())
	metricsCollector := monitor.NewMetricsCollector(repo)

	// 创建安全配置
	webSecurityConfig := &web.SecurityConfig{
		EnableRateLimit:       true,
		RateLimit:            60,
		BurstLimit:           10,
		EnableIPCheck:        true,
		AllowedIPs:          []string{"127.0.0.1/32"},
		EnableSecurityHeaders: true,
		EnableBruteForceCheck: true,
		LoginAttemptLimit:    3,
		LoginLockoutDuration: 5 * time.Minute,
	}

	// 创建Web服务器配置
	webConfig := &web.Config{
		Port:         8080,
		Host:         "localhost",
		StaticPath:   "test/static",
		TemplatePath: "", // 测试模式下不加载模板
		JWTSecret:    "test-secret",
		CORSEnabled:  true,
		CORSOrigins:  []string{"*"},
		Security:     webSecurityConfig,
	}

	// 创建依赖
	deps := &web.Dependencies{
		Database:         db,
		SecurityManager:  secMgr,
		MetricsCollector: metricsCollector,
	}

	// 设置测试模式
	gin.SetMode(gin.TestMode)

	// 创建Web服务器
	webServer, err := web.NewWebServer(webConfig, deps)
	if err != nil {
		t.Fatalf("Failed to create web server: %v", err)
	}
	
	// 不加载HTML模板，直接使用路由器
	router := webServer.GetRouter()

	t.Run("Test Rate Limiting", func(t *testing.T) {
		// 测试频率限制
		for i := 0; i < 15; i++ {
			req, _ := http.NewRequest("GET", "/", nil)
			req.Header.Set("X-Forwarded-For", "127.0.0.1") // 使用允许的IP
			
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
			
			if i < 10 {
				// 前10个请求应该通过
				if w.Code == http.StatusTooManyRequests {
					t.Fatalf("Request %d should not be rate limited, got status %d", i+1, w.Code)
				}
			} else {
				// 后续请求应该被限制
				if w.Code != http.StatusTooManyRequests {
					t.Fatalf("Request %d should be rate limited, got status %d", i+1, w.Code)
				}
			}
		}
	})

	t.Run("Test Security Headers", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/", nil)
		w := httptest.NewRecorder()
		
		router.ServeHTTP(w, req)
		
		// 检查安全头
		if w.Header().Get("X-Frame-Options") != "DENY" {
			t.Error("X-Frame-Options header not set correctly")
		}
		
		if w.Header().Get("X-Content-Type-Options") != "nosniff" {
			t.Error("X-Content-Type-Options header not set correctly")
		}
		
		if w.Header().Get("X-XSS-Protection") != "1; mode=block" {
			t.Error("X-XSS-Protection header not set correctly")
		}
	})

	t.Run("Test Login Brute Force Protection", func(t *testing.T) {
		loginData := map[string]string{
			"username": "admin",
			"password": "wrongpassword",
		}
		
		// 尝试多次错误登录
		for i := 0; i < 5; i++ {
			jsonData, _ := json.Marshal(loginData)
			req, _ := http.NewRequest("POST", "/api/auth/login", bytes.NewBuffer(jsonData))
			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("X-Forwarded-For", "127.0.0.1") // 使用允许的IP
			
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
			
			if i < 3 {
				// 前3次应该返回401 Unauthorized
				if w.Code != http.StatusUnauthorized {
					t.Fatalf("Login attempt %d should return 401, got %d", i+1, w.Code)
				}
			} else {
				// 后续尝试应该被阻止
				if w.Code != http.StatusTooManyRequests {
					t.Fatalf("Login attempt %d should be blocked, got %d", i+1, w.Code)
				}
			}
		}
	})

	t.Run("Test IP Filtering", func(t *testing.T) {
		// 测试被允许的IP
		req, _ := http.NewRequest("GET", "/", nil)
		req.Header.Set("X-Forwarded-For", "127.0.0.1")
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		if w.Code == http.StatusForbidden {
			t.Error("Allowed IP should not be blocked")
		}
		
		// 测试不在允许列表中的IP
		req2, _ := http.NewRequest("GET", "/", nil)
		req2.Header.Set("X-Forwarded-For", "********")
		
		w2 := httptest.NewRecorder()
		router.ServeHTTP(w2, req2)
		
		if w2.Code != http.StatusForbidden {
			t.Error("Non-allowed IP should be blocked")
		}
	})

	t.Log("Security middleware tests passed")
}