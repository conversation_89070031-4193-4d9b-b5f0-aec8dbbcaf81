package go_sdk

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net"
	"sync"
	"time"

	"qweb-verification/pkg/sdk"
)

// GoClient Go语言客户端实现
type GoClient struct {
	config         *sdk.ClientConfig
	conn           net.Conn
	state          sdk.ClientState
	eventHandler   sdk.EventHandler
	lastError      error
	heartbeatTicker *time.Ticker
	stopCh         chan struct{}
	reconnectCh    chan struct{}
	connInfo       *sdk.ConnectionInfo
	sessionInfo    *sdk.SessionInfo
	metrics        *sdk.ClientMetrics
	mu             sync.RWMutex
	reconnectCount int
}

// NewGoClient 创建Go客户端
func NewGoClient(options ...sdk.ClientOption) sdk.VerificationClient {
	config := sdk.DefaultClientConfig()
	sdk.ApplyOptions(config, options...)

	return &GoClient{
		config:      config,
		state:       sdk.StateDisconnected,
		stopCh:      make(chan struct{}),
		reconnectCh: make(chan struct{}),
		metrics:     &sdk.ClientMetrics{},
	}
}

// Connect 连接到服务器
func (c *GoClient) Connect(ctx context.Context, serverAddr string, config *sdk.ClientConfig) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if config != nil {
		c.config = config
	}

	if c.state == sdk.StateConnected || c.state == sdk.StateAuthenticated {
		return nil
	}

	c.setState(sdk.StateConnecting)

	// 创建TLS配置
	tlsConfig := &tls.Config{
		ServerName:         c.config.TLSConfig.ServerName,
		InsecureSkipVerify: c.config.TLSConfig.InsecureSkipVerify,
	}

	// 连接服务器
	dialer := &net.Dialer{
		Timeout: c.config.ConnectTimeout,
	}

	conn, err := tls.DialWithDialer(dialer, "tcp", serverAddr, tlsConfig)
	if err != nil {
		c.setError(sdk.NewNetworkError(fmt.Sprintf("Failed to connect: %v", err)))
		c.setState(sdk.StateError)
		return c.lastError
	}

	c.conn = conn
	c.setState(sdk.StateConnected)

	// 设置连接信息
	c.connInfo = &sdk.ConnectionInfo{
		ServerAddr:   serverAddr,
		LocalAddr:    conn.LocalAddr().String(),
		ConnectedAt:  time.Now(),
		LastActivity: time.Now(),
	}

	// 更新指标
	c.metrics.ConnectionCount++
	c.metrics.LastConnectedAt = time.Now()

	// 启动消息处理协程
	go c.messageLoop()

	// 触发连接事件
	if c.eventHandler != nil {
		c.eventHandler.OnConnected(c.connInfo)
	}

	// 启动自动重连监控
	if c.config.AutoReconnect {
		go c.reconnectMonitor(ctx, serverAddr)
	}

	return nil
}

// Disconnect 断开连接
func (c *GoClient) Disconnect() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.state == sdk.StateDisconnected {
		return nil
	}

	// 停止心跳
	c.stopHeartbeat()

	// 发送断开连接消息
	if c.conn != nil {
		disconnectMsg := sdk.Message{
			Type:      sdk.MsgTypeDisconnect,
			Timestamp: time.Now().Unix(),
		}
		c.sendMessage(disconnectMsg)
		c.conn.Close()
		c.conn = nil
	}

	c.setState(sdk.StateDisconnected)
	close(c.stopCh)

	if c.eventHandler != nil {
		c.eventHandler.OnDisconnected("User initiated disconnect")
	}

	return nil
}

// IsConnected 检查是否已连接
func (c *GoClient) IsConnected() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.state == sdk.StateConnected || c.state == sdk.StateAuthenticated
}

// GetConnectionInfo 获取连接信息
func (c *GoClient) GetConnectionInfo() *sdk.ConnectionInfo {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.connInfo
}

// AuthenticateUser 用户认证
func (c *GoClient) AuthenticateUser(username, password string) (*sdk.AuthResult, error) {
	if !c.IsConnected() {
		return nil, sdk.NewNetworkError("Not connected to server")
	}

	c.setState(sdk.StateAuthenticating)

	authReq := &sdk.AuthRequest{
		Type:      "user_auth",
		Timestamp: time.Now().Unix(),
		Nonce:     c.generateNonce(),
		Username:  username,
		Password:  password,
	}

	// 添加签名（简化实现）
	authReq.Signature = c.signMessage(authReq)

	msg := sdk.Message{
		Type:      sdk.MsgTypeAuth,
		Data:      authReq,
		ID:        c.generateMessageID(),
		Timestamp: time.Now().Unix(),
	}

	// 发送认证请求
	if err := c.sendMessage(msg); err != nil {
		c.setState(sdk.StateConnected)
		return nil, err
	}

	// 等待认证响应（简化实现，实际应该使用channel等待）
	time.Sleep(time.Second) // 简化等待

	// 这里应该从响应中获取结果
	result := &sdk.AuthResult{
		Success: true,
		Message: "Authentication successful",
	}

	if result.Success {
		c.setState(sdk.StateAuthenticated)
		c.sessionInfo = &sdk.SessionInfo{
			SessionToken: result.SessionToken,
			CreatedAt:    time.Now(),
			ExpiresAt:    result.ExpiresAt,
		}
		c.metrics.AuthSuccessCount++
		
		if c.eventHandler != nil {
			c.eventHandler.OnAuthSuccess(result)
		}
	} else {
		c.setState(sdk.StateConnected)
		c.metrics.AuthFailureCount++
		
		if c.eventHandler != nil {
			c.eventHandler.OnAuthFailed(result.Message, result.ErrorCode)
		}
	}

	return result, nil
}

// AuthenticateCard 卡号认证
func (c *GoClient) AuthenticateCard(cardNumber, cardPassword string) (*sdk.AuthResult, error) {
	if !c.IsConnected() {
		return nil, sdk.NewNetworkError("Not connected to server")
	}

	c.setState(sdk.StateAuthenticating)

	authReq := &sdk.AuthRequest{
		Type:         "card_auth",
		Timestamp:    time.Now().Unix(),
		Nonce:        c.generateNonce(),
		CardNumber:   cardNumber,
		CardPassword: cardPassword,
	}

	// 添加签名
	authReq.Signature = c.signMessage(authReq)

	msg := sdk.Message{
		Type:      sdk.MsgTypeAuth,
		Data:      authReq,
		ID:        c.generateMessageID(),
		Timestamp: time.Now().Unix(),
	}

	if err := c.sendMessage(msg); err != nil {
		c.setState(sdk.StateConnected)
		return nil, err
	}

	// 简化实现
	result := &sdk.AuthResult{
		Success: true,
		Message: "Card authentication successful",
	}

	if result.Success {
		c.setState(sdk.StateAuthenticated)
		c.metrics.AuthSuccessCount++
		
		if c.eventHandler != nil {
			c.eventHandler.OnAuthSuccess(result)
		}
	} else {
		c.setState(sdk.StateConnected)
		c.metrics.AuthFailureCount++
		
		if c.eventHandler != nil {
			c.eventHandler.OnAuthFailed(result.Message, result.ErrorCode)
		}
	}

	return result, nil
}

// CheckAuthStatus 检查认证状态
func (c *GoClient) CheckAuthStatus() (*sdk.StatusResult, error) {
	if !c.IsConnected() {
		return nil, sdk.NewNetworkError("Not connected to server")
	}

	msg := sdk.Message{
		Type:      sdk.MsgTypeStatus,
		Data:      map[string]interface{}{},
		ID:        c.generateMessageID(),
		Timestamp: time.Now().Unix(),
	}

	if err := c.sendMessage(msg); err != nil {
		return nil, err
	}

	// 简化实现
	result := &sdk.StatusResult{
		IsAuthenticated: c.state == sdk.StateAuthenticated,
		SessionValid:    c.sessionInfo != nil && c.sessionInfo.ExpiresAt.After(time.Now()),
		ServerTime:      time.Now(),
	}

	return result, nil
}

// Logout 登出
func (c *GoClient) Logout() error {
	if c.state != sdk.StateAuthenticated {
		return nil
	}

	msg := sdk.Message{
		Type:      sdk.MsgTypeLogout,
		Data:      map[string]interface{}{},
		ID:        c.generateMessageID(),
		Timestamp: time.Now().Unix(),
	}

	if err := c.sendMessage(msg); err != nil {
		return err
	}

	c.setState(sdk.StateConnected)
	c.sessionInfo = nil

	return nil
}

// RefreshSession 刷新会话
func (c *GoClient) RefreshSession() (*sdk.AuthResult, error) {
	if c.sessionInfo == nil {
		return nil, sdk.NewSessionExpiredError()
	}

	// 简化实现
	c.sessionInfo.ExpiresAt = time.Now().Add(24 * time.Hour)

	return &sdk.AuthResult{
		Success:      true,
		SessionToken: c.sessionInfo.SessionToken,
		ExpiresAt:    c.sessionInfo.ExpiresAt,
		Message:      "Session refreshed",
	}, nil
}

// GetSessionInfo 获取会话信息
func (c *GoClient) GetSessionInfo() *sdk.SessionInfo {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.sessionInfo
}

// StartHeartbeat 启动心跳
func (c *GoClient) StartHeartbeat() error {
	if c.heartbeatTicker != nil {
		return nil
	}

	c.heartbeatTicker = time.NewTicker(c.config.HeartbeatInterval)
	go c.heartbeatLoop()

	return nil
}

// StopHeartbeat 停止心跳
func (c *GoClient) StopHeartbeat() error {
	return c.stopHeartbeat()
}

// SetEventHandler 设置事件处理器
func (c *GoClient) SetEventHandler(handler sdk.EventHandler) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.eventHandler = handler
}

// RemoveEventHandler 移除事件处理器
func (c *GoClient) RemoveEventHandler() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.eventHandler = nil
}

// GetLastError 获取最后的错误
func (c *GoClient) GetLastError() error {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.lastError
}

// 内部方法

// setState 设置状态
func (c *GoClient) setState(state sdk.ClientState) {
	c.state = state
}

// setError 设置错误
func (c *GoClient) setError(err error) {
	c.lastError = err
}

// sendMessage 发送消息
func (c *GoClient) sendMessage(msg sdk.Message) error {
	if c.conn == nil {
		return sdk.NewNetworkError("Connection is nil")
	}

	c.conn.SetWriteDeadline(time.Now().Add(c.config.WriteTimeout))
	
	encoder := json.NewEncoder(c.conn)
	if err := encoder.Encode(msg); err != nil {
		return sdk.NewNetworkError(fmt.Sprintf("Failed to send message: %v", err))
	}

	c.metrics.MessagesSent++
	return nil
}

// messageLoop 消息处理循环
func (c *GoClient) messageLoop() {
	decoder := json.NewDecoder(c.conn)

	for {
		select {
		case <-c.stopCh:
			return
		default:
			c.conn.SetReadDeadline(time.Now().Add(c.config.ReadTimeout))

			var msg sdk.Message
			if err := decoder.Decode(&msg); err != nil {
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					continue
				}
				c.setError(sdk.NewNetworkError(fmt.Sprintf("Message decode error: %v", err)))
				c.setState(sdk.StateError)
				c.triggerReconnect()
				return
			}

			c.metrics.MessagesReceived++
			c.handleMessage(&msg)
		}
	}
}

// handleMessage 处理消息
func (c *GoClient) handleMessage(msg *sdk.Message) {
	switch msg.Type {
	case sdk.MsgTypeAuthResp:
		// 处理认证响应
	case sdk.MsgTypeHeartbeatResp:
		// 处理心跳响应
		if c.eventHandler != nil {
			c.eventHandler.OnHeartbeat()
		}
	case sdk.MsgTypeError:
		// 处理错误消息
		if c.eventHandler != nil {
			c.eventHandler.OnError(sdk.NewProtocolError("Server error"))
		}
	default:
		if c.eventHandler != nil {
			c.eventHandler.OnMessage(msg.Type, msg.Data)
		}
	}
}

// heartbeatLoop 心跳循环
func (c *GoClient) heartbeatLoop() {
	for {
		select {
		case <-c.stopCh:
			return
		case <-c.heartbeatTicker.C:
			c.sendHeartbeat()
		}
	}
}

// sendHeartbeat 发送心跳
func (c *GoClient) sendHeartbeat() {
	heartbeat := sdk.Message{
		Type: sdk.MsgTypeHeartbeat,
		Data: &sdk.HeartbeatRequest{
			Timestamp: time.Now().Unix(),
			ClientID:  c.generateClientID(),
		},
		Timestamp: time.Now().Unix(),
	}

	if err := c.sendMessage(heartbeat); err != nil {
		c.setError(err)
	}
}

// stopHeartbeat 停止心跳
func (c *GoClient) stopHeartbeat() error {
	if c.heartbeatTicker != nil {
		c.heartbeatTicker.Stop()
		c.heartbeatTicker = nil
	}
	return nil
}

// reconnectMonitor 重连监控
func (c *GoClient) reconnectMonitor(ctx context.Context, serverAddr string) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-c.stopCh:
			return
		case <-c.reconnectCh:
			c.performReconnect(ctx, serverAddr)
		}
	}
}

// triggerReconnect 触发重连
func (c *GoClient) triggerReconnect() {
	if c.config.AutoReconnect && c.reconnectCount < c.config.MaxReconnectTries {
		select {
		case c.reconnectCh <- struct{}{}:
		default:
		}
	}
}

// performReconnect 执行重连
func (c *GoClient) performReconnect(ctx context.Context, serverAddr string) {
	c.reconnectCount++
	time.Sleep(c.config.ReconnectInterval)

	if err := c.Connect(ctx, serverAddr, nil); err != nil {
		if c.reconnectCount < c.config.MaxReconnectTries {
			c.triggerReconnect()
		}
	} else {
		c.reconnectCount = 0
		c.metrics.ReconnectionCount++
	}
}

// 辅助方法

// generateNonce 生成随机数
func (c *GoClient) generateNonce() string {
	return fmt.Sprintf("%d_%d", time.Now().UnixNano(), time.Now().Nanosecond())
}

// generateMessageID 生成消息ID
func (c *GoClient) generateMessageID() string {
	return fmt.Sprintf("msg_%d", time.Now().UnixNano())
}

// generateClientID 生成客户端ID
func (c *GoClient) generateClientID() string {
	return fmt.Sprintf("go_client_%d", time.Now().Unix())
}

// signMessage 签名消息（简化实现）
func (c *GoClient) signMessage(data interface{}) string {
	// 这里应该实现真正的消息签名
	return "dummy_signature"
}