package database

import (
	"fmt"
	"time"

	"qweb-verification/internal/models"

	"gorm.io/gorm"
)

// Repository 数据库仓库接口
type Repository interface {
	// 用户相关
	CreateUser(user *models.User) error
	GetUserByID(id uint) (*models.User, error)
	GetUserByUsername(username string) (*models.User, error)
	UpdateUser(user *models.User) error
	DeleteUser(id uint) error
	ListUsers(offset, limit int, filters map[string]interface{}) ([]*models.User, int64, error)

	// 代理商相关
	CreateAgent(agent *models.Agent) error
	GetAgentByID(id uint) (*models.Agent, error)
	GetAgentByUsername(username string) (*models.Agent, error)
	UpdateAgent(agent *models.Agent) error
	DeleteAgent(id uint) error
	ListAgents(offset, limit int, filters map[string]interface{}) ([]*models.Agent, int64, error)
	GetAgentChildren(agentID uint) ([]*models.Agent, error)

	// 卡号相关
	CreateCard(card *models.Card) error
	CreateCardsBatch(cards []*models.Card) error
	GetCardByID(id uint) (*models.Card, error)
	GetCardByNumber(cardNumber string) (*models.Card, error)
	UpdateCard(card *models.Card) error
	DeleteCard(id uint) error
	ListCards(offset, limit int, filters map[string]interface{}) ([]*models.Card, int64, error)

	// 会话相关
	CreateSession(session *models.Session) error
	GetSessionByToken(token string) (*models.Session, error)
	UpdateSession(session *models.Session) error
	DeleteSession(id uint) error
	DeleteExpiredSessions() error
	GetUserActiveSessions(userID uint) ([]*models.Session, error)

	// 佣金相关
	CreateCommission(commission *models.Commission) error
	ListCommissions(offset, limit int, filters map[string]interface{}) ([]*models.Commission, int64, error)
	GetAgentCommissions(agentID uint, offset, limit int) ([]*models.Commission, int64, error)

	// 日志相关
	CreateOperationLog(log *models.OperationLog) error
	ListOperationLogs(offset, limit int, filters map[string]interface{}) ([]*models.OperationLog, int64, error)

	// IP黑名单相关
	CreateOrUpdateIPBlacklist(ip *models.IPBlacklist) error
	GetIPBlacklist(ipAddress string) (*models.IPBlacklist, error)
	DeleteIPBlacklist(ipAddress string) error
	CleanExpiredBans() error

	// 系统配置相关
	GetConfig(key string) (*models.SystemConfig, error)
	SetConfig(config *models.SystemConfig) error
	ListConfigs(category string) ([]*models.SystemConfig, error)

	// 在线用户相关
	CreateOnlineUser(user *models.OnlineUser) error
	UpdateOnlineUser(user *models.OnlineUser) error
	DeleteOnlineUser(userID, sessionID uint) error
	GetOnlineUsers() ([]*models.OnlineUser, error)
	CleanInactiveUsers(timeout time.Duration) error

	// 统计相关
	GetUserCount() (int64, error)
	GetAgentCount() (int64, error)
	GetCardCount() (int64, error)
	GetOnlineUserCount() (int64, error)
	GetTodayStats() (map[string]int64, error)
}

// repository 数据库仓库实现
type repository struct {
	db *gorm.DB
}

// NewRepository 创建新的数据库仓库
func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

// 用户相关方法实现

func (r *repository) CreateUser(user *models.User) error {
	return r.db.Create(user).Error
}

func (r *repository) GetUserByID(id uint) (*models.User, error) {
	var user models.User
	err := r.db.Preload("Agent").First(&user, id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *repository) GetUserByUsername(username string) (*models.User, error) {
	var user models.User
	err := r.db.Preload("Agent").Where("username = ?", username).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *repository) UpdateUser(user *models.User) error {
	return r.db.Save(user).Error
}

func (r *repository) DeleteUser(id uint) error {
	return r.db.Delete(&models.User{}, id).Error
}

func (r *repository) ListUsers(offset, limit int, filters map[string]interface{}) ([]*models.User, int64, error) {
	var users []*models.User
	var total int64

	query := r.db.Model(&models.User{}).Preload("Agent")

	// 应用过滤器
	for key, value := range filters {
		switch key {
		case "status":
			query = query.Where("status = ?", value)
		case "agent_id":
			query = query.Where("agent_id = ?", value)
		case "username":
			query = query.Where("username LIKE ?", fmt.Sprintf("%%%v%%", value))
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取列表
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&users).Error
	return users, total, err
}

// 代理商相关方法实现

func (r *repository) CreateAgent(agent *models.Agent) error {
	return r.db.Create(agent).Error
}

func (r *repository) GetAgentByID(id uint) (*models.Agent, error) {
	var agent models.Agent
	err := r.db.Preload("Parent").First(&agent, id).Error
	if err != nil {
		return nil, err
	}
	return &agent, nil
}

func (r *repository) GetAgentByUsername(username string) (*models.Agent, error) {
	var agent models.Agent
	err := r.db.Preload("Parent").Where("username = ?", username).First(&agent).Error
	if err != nil {
		return nil, err
	}
	return &agent, nil
}

func (r *repository) UpdateAgent(agent *models.Agent) error {
	return r.db.Save(agent).Error
}

func (r *repository) DeleteAgent(id uint) error {
	return r.db.Delete(&models.Agent{}, id).Error
}

func (r *repository) ListAgents(offset, limit int, filters map[string]interface{}) ([]*models.Agent, int64, error) {
	var agents []*models.Agent
	var total int64

	query := r.db.Model(&models.Agent{}).Preload("Parent")

	// 应用过滤器
	for key, value := range filters {
		switch key {
		case "status":
			query = query.Where("status = ?", value)
		case "level":
			query = query.Where("level = ?", value)
		case "parent_id":
			query = query.Where("parent_id = ?", value)
		case "username":
			query = query.Where("username LIKE ?", fmt.Sprintf("%%%v%%", value))
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取列表
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&agents).Error
	return agents, total, err
}

func (r *repository) GetAgentChildren(agentID uint) ([]*models.Agent, error) {
	var children []*models.Agent
	err := r.db.Where("parent_id = ?", agentID).Find(&children).Error
	return children, err
}

// 卡号相关方法实现

func (r *repository) CreateCard(card *models.Card) error {
	return r.db.Create(card).Error
}

func (r *repository) CreateCardsBatch(cards []*models.Card) error {
	return r.db.CreateInBatches(cards, 100).Error
}

func (r *repository) GetCardByID(id uint) (*models.Card, error) {
	var card models.Card
	err := r.db.Preload("Agent").Preload("User").First(&card, id).Error
	if err != nil {
		return nil, err
	}
	return &card, nil
}

func (r *repository) GetCardByNumber(cardNumber string) (*models.Card, error) {
	var card models.Card
	err := r.db.Preload("Agent").Preload("User").Where("card_number = ?", cardNumber).First(&card).Error
	if err != nil {
		return nil, err
	}
	return &card, nil
}

func (r *repository) UpdateCard(card *models.Card) error {
	return r.db.Save(card).Error
}

func (r *repository) DeleteCard(id uint) error {
	return r.db.Delete(&models.Card{}, id).Error
}

func (r *repository) ListCards(offset, limit int, filters map[string]interface{}) ([]*models.Card, int64, error) {
	var cards []*models.Card
	var total int64

	query := r.db.Model(&models.Card{}).Preload("Agent").Preload("User")

	// 应用过滤器
	for key, value := range filters {
		switch key {
		case "status":
			query = query.Where("status = ?", value)
		case "agent_id":
			query = query.Where("agent_id = ?", value)
		case "user_id":
			query = query.Where("user_id = ?", value)
		case "card_number":
			query = query.Where("card_number LIKE ?", fmt.Sprintf("%%%v%%", value))
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取列表
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&cards).Error
	return cards, total, err
}

// 会话相关方法实现

func (r *repository) CreateSession(session *models.Session) error {
	return r.db.Create(session).Error
}

func (r *repository) GetSessionByToken(token string) (*models.Session, error) {
	var session models.Session
	err := r.db.Preload("User").Where("session_token = ?", token).First(&session).Error
	if err != nil {
		return nil, err
	}
	return &session, nil
}

func (r *repository) UpdateSession(session *models.Session) error {
	return r.db.Save(session).Error
}

func (r *repository) DeleteSession(id uint) error {
	return r.db.Delete(&models.Session{}, id).Error
}

func (r *repository) DeleteExpiredSessions() error {
	return r.db.Where("expires_at < ?", time.Now()).Delete(&models.Session{}).Error
}

func (r *repository) GetUserActiveSessions(userID uint) ([]*models.Session, error) {
	var sessions []*models.Session
	err := r.db.Where("user_id = ? AND expires_at > ?", userID, time.Now()).Find(&sessions).Error
	return sessions, err
}

// 其他方法实现...
// （由于内容较多，这里省略了一些方法的实现，但结构是相同的）

// 统计相关方法实现

func (r *repository) GetUserCount() (int64, error) {
	var count int64
	err := r.db.Model(&models.User{}).Count(&count).Error
	return count, err
}

func (r *repository) GetAgentCount() (int64, error) {
	var count int64
	err := r.db.Model(&models.Agent{}).Count(&count).Error
	return count, err
}

func (r *repository) GetCardCount() (int64, error) {
	var count int64
	err := r.db.Model(&models.Card{}).Count(&count).Error
	return count, err
}

func (r *repository) GetOnlineUserCount() (int64, error) {
	var count int64
	err := r.db.Model(&models.OnlineUser{}).Count(&count).Error
	return count, err
}

func (r *repository) GetTodayStats() (map[string]int64, error) {
	stats := make(map[string]int64)
	today := time.Now().Truncate(24 * time.Hour)

	// 今日新增用户
	var newUsers int64
	if err := r.db.Model(&models.User{}).Where("created_at >= ?", today).Count(&newUsers).Error; err != nil {
		return nil, err
	}
	stats["new_users"] = newUsers

	// 今日使用卡号
	var usedCards int64
	if err := r.db.Model(&models.Card{}).Where("used_at >= ?", today).Count(&usedCards).Error; err != nil {
		return nil, err
	}
	stats["used_cards"] = usedCards

	// 今日登录次数
	var logins int64
	if err := r.db.Model(&models.OperationLog{}).Where("operation = ? AND created_at >= ?", models.OpTypeLogin, today).Count(&logins).Error; err != nil {
		return nil, err
	}
	stats["logins"] = logins

	return stats, nil
}

// 简化实现其他必要方法
func (r *repository) CreateCommission(commission *models.Commission) error {
	return r.db.Create(commission).Error
}

func (r *repository) ListCommissions(offset, limit int, filters map[string]interface{}) ([]*models.Commission, int64, error) {
	var commissions []*models.Commission
	var total int64
	query := r.db.Model(&models.Commission{}).Preload("Agent").Preload("Card")

	for key, value := range filters {
		if key == "agent_id" {
			query = query.Where("agent_id = ?", value)
		}
	}

	query.Count(&total)
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&commissions).Error
	return commissions, total, err
}

func (r *repository) GetAgentCommissions(agentID uint, offset, limit int) ([]*models.Commission, int64, error) {
	return r.ListCommissions(offset, limit, map[string]interface{}{"agent_id": agentID})
}

func (r *repository) CreateOperationLog(log *models.OperationLog) error {
	return r.db.Create(log).Error
}

func (r *repository) ListOperationLogs(offset, limit int, filters map[string]interface{}) ([]*models.OperationLog, int64, error) {
	var logs []*models.OperationLog
	var total int64
	query := r.db.Model(&models.OperationLog{}).Preload("User").Preload("Agent")

	query.Count(&total)
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&logs).Error
	return logs, total, err
}

func (r *repository) CreateOrUpdateIPBlacklist(ip *models.IPBlacklist) error {
	var existing models.IPBlacklist
	err := r.db.Where("ip_address = ?", ip.IPAddress).First(&existing).Error
	if err == gorm.ErrRecordNotFound {
		return r.db.Create(ip).Error
	}
	existing.FailedAttempts = ip.FailedAttempts
	existing.BannedUntil = ip.BannedUntil
	existing.Reason = ip.Reason
	existing.UpdatedAt = time.Now()
	return r.db.Save(&existing).Error
}

func (r *repository) GetIPBlacklist(ipAddress string) (*models.IPBlacklist, error) {
	var ip models.IPBlacklist
	err := r.db.Where("ip_address = ?", ipAddress).First(&ip).Error
	if err != nil {
		return nil, err
	}
	return &ip, nil
}

func (r *repository) DeleteIPBlacklist(ipAddress string) error {
	return r.db.Where("ip_address = ?", ipAddress).Delete(&models.IPBlacklist{}).Error
}

func (r *repository) CleanExpiredBans() error {
	return r.db.Where("banned_until IS NOT NULL AND banned_until < ?", time.Now()).Delete(&models.IPBlacklist{}).Error
}

func (r *repository) GetConfig(key string) (*models.SystemConfig, error) {
	var config models.SystemConfig
	err := r.db.Where("key = ?", key).First(&config).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

func (r *repository) SetConfig(config *models.SystemConfig) error {
	var existing models.SystemConfig
	err := r.db.Where("key = ?", config.Key).First(&existing).Error
	if err == gorm.ErrRecordNotFound {
		return r.db.Create(config).Error
	}
	existing.Value = config.Value
	existing.UpdatedAt = time.Now()
	return r.db.Save(&existing).Error
}

func (r *repository) ListConfigs(category string) ([]*models.SystemConfig, error) {
	var configs []*models.SystemConfig
	query := r.db.Model(&models.SystemConfig{})
	if category != "" {
		query = query.Where("category = ?", category)
	}
	err := query.Order("key").Find(&configs).Error
	return configs, err
}

func (r *repository) CreateOnlineUser(user *models.OnlineUser) error {
	return r.db.Create(user).Error
}

func (r *repository) UpdateOnlineUser(user *models.OnlineUser) error {
	return r.db.Save(user).Error
}

func (r *repository) DeleteOnlineUser(userID, sessionID uint) error {
	return r.db.Where("user_id = ? AND session_id = ?", userID, sessionID).Delete(&models.OnlineUser{}).Error
}

func (r *repository) GetOnlineUsers() ([]*models.OnlineUser, error) {
	var users []*models.OnlineUser
	err := r.db.Preload("User").Preload("Session").Find(&users).Error
	return users, err
}

func (r *repository) CleanInactiveUsers(timeout time.Duration) error {
	cutoff := time.Now().Add(-timeout)
	return r.db.Where("last_active < ?", cutoff).Delete(&models.OnlineUser{}).Error
}
