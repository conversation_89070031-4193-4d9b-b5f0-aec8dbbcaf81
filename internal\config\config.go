package config

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"qweb-verification/internal/logger"
	"qweb-verification/internal/security"
)

// Config 应用配置
type Config struct {
	Server   *ServerConfig   `yaml:"server"`
	Database *DatabaseConfig `yaml:"database"`
	Security *SecurityConfig `yaml:"security"`
	Logger   *LoggerConfig   `yaml:"logger"`
	Web      *WebConfig      `yaml:"web"`
	Monitor  *MonitorConfig  `yaml:"monitor"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	TCPPort           int           `yaml:"tcp_port"`
	WebPort           int           `yaml:"web_port"`
	Host              string        `yaml:"host"`
	MaxConnections    int           `yaml:"max_connections"`
	ConnectionTimeout time.Duration `yaml:"connection_timeout"`
	HeartbeatInterval time.Duration `yaml:"heartbeat_interval"`
	TLS               *security.TLSConfig `yaml:"tls"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Path            string        `yaml:"path"`
	MaxIdleConns    int           `yaml:"max_idle_conns"`
	MaxOpenConns    int           `yaml:"max_open_conns"`
	ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
	LogLevel        string        `yaml:"log_level"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	TLS                        *security.TLSConfig `yaml:"tls"`
	MaxFailedAttempts          int                 `yaml:"max_failed_attempts"`
	BanDuration                string              `yaml:"ban_duration"`
	RateLimit                  float64             `yaml:"rate_limit"`
	RateBurst                  int                 `yaml:"rate_burst"`
	SessionTimeout             string              `yaml:"session_timeout"`
	EncryptionKey              string              `yaml:"encryption_key"`
	EnableBruteForceProtection bool                `yaml:"enable_brute_force_protection"`
	SecretKey                  string              `yaml:"secret_key"`
	JWTSecret                  string              `yaml:"jwt_secret"`
}

// LoggerConfig 日志配置
type LoggerConfig struct {
	Level      string `yaml:"level"`
	Format     string `yaml:"format"`
	Output     string `yaml:"output"`
	Filename   string `yaml:"filename"`
	MaxSize    int    `yaml:"max_size"`
	MaxBackups int    `yaml:"max_backups"`
	MaxAge     int    `yaml:"max_age"`
	Compress   bool   `yaml:"compress"`
}

// WebConfig Web配置
type WebConfig struct {
	Port         int    `yaml:"port"`
	Host         string `yaml:"host"`
	StaticPath   string `yaml:"static_path"`
	TemplatePath string `yaml:"template_path"`
	JWTSecret    string `yaml:"jwt_secret"`
	CORSEnabled  bool   `yaml:"cors_enabled"`
	CORSOrigins  []string `yaml:"cors_origins"`
}

// MonitorConfig 监控配置
type MonitorConfig struct {
	Enabled         bool          `yaml:"enabled"`
	MetricsInterval time.Duration `yaml:"metrics_interval"`
	AlertsEnabled   bool          `yaml:"alerts_enabled"`
	EmailEnabled    bool          `yaml:"email_enabled"`
	WebhookEnabled  bool          `yaml:"webhook_enabled"`
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		Server: &ServerConfig{
			TCPPort:           9999,
			WebPort:           8080,
			Host:              "0.0.0.0",
			MaxConnections:    1000,
			ConnectionTimeout: 5 * time.Minute,
			HeartbeatInterval: 30 * time.Second,
			TLS: &security.TLSConfig{
				MinVersion:         "1.2",
				PreferServerCipher: true,
				ClientAuth:         "none",
			},
		},
		Database: &DatabaseConfig{
			Path:            "data/verification.db",
			MaxIdleConns:    10,
			MaxOpenConns:    100,
			ConnMaxLifetime: time.Hour,
			LogLevel:        "info",
		},
		Security: &SecurityConfig{
			MaxFailedAttempts:          5,
			BanDuration:                "30m",
			RateLimit:                  10.0,
			RateBurst:                  20,
			SessionTimeout:             "24h",
			EncryptionKey:              "default-encryption-key-change-me",
			EnableBruteForceProtection: true,
			SecretKey:                  "default-secret-key-change-me",
			JWTSecret:                  "default-jwt-secret-change-me",
		},
		Logger: &LoggerConfig{
			Level:      "info",
			Format:     "json",
			Output:     "both",
			Filename:   "logs/qweb.log",
			MaxSize:    100,
			MaxBackups: 10,
			MaxAge:     30,
			Compress:   true,
		},
		Web: &WebConfig{
			Port:         8080,
			Host:         "0.0.0.0",
			StaticPath:   "web/static",
			TemplatePath: "web/templates",
			JWTSecret:    "default-jwt-secret-change-me",
			CORSEnabled:  true,
			CORSOrigins:  []string{"*"},
		},
		Monitor: &MonitorConfig{
			Enabled:         true,
			MetricsInterval: 30 * time.Second,
			AlertsEnabled:   true,
			EmailEnabled:    false,
			WebhookEnabled:  false,
		},
	}
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	// 如果配置文件不存在，创建默认配置
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		config := DefaultConfig()
		if err := SaveConfig(config, configPath); err != nil {
			return nil, fmt.Errorf("failed to save default config: %w", err)
		}
		return config, nil
	}

	// 这里应该实现YAML配置文件的加载
	// 为了简化，直接返回默认配置
	return DefaultConfig(), nil
}

// SaveConfig 保存配置文件
func SaveConfig(config *Config, configPath string) error {
	// 确保配置目录存在
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}

	// 这里应该实现YAML配置文件的保存
	// 为了简化，仅创建空文件
	file, err := os.Create(configPath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 写入示例配置
	exampleConfig := `# Qweb网络验证系统配置文件

server:
  tcp_port: 9999
  web_port: 8080
  host: "0.0.0.0"
  max_connections: 1000
  connection_timeout: "5m"
  heartbeat_interval: "30s"

database:
  path: "data/verification.db"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: "1h"
  log_level: "info"

security:
  max_failed_attempts: 5
  ban_duration: "30m"
  rate_limit: 10.0
  rate_burst: 20
  session_timeout: "24h"
  encryption_key: "change-me-to-secure-key"
  enable_brute_force_protection: true
  secret_key: "change-me-to-secure-secret"
  jwt_secret: "change-me-to-jwt-secret"

logger:
  level: "info"
  format: "json"
  output: "both"
  filename: "logs/qweb.log"
  max_size: 100
  max_backups: 10
  max_age: 30
  compress: true

web:
  port: 8080
  host: "0.0.0.0"
  static_path: "web/static"
  template_path: "web/templates"
  jwt_secret: "change-me-to-jwt-secret"
  cors_enabled: true
  cors_origins: ["*"]

monitor:
  enabled: true
  metrics_interval: "30s"
  alerts_enabled: true
  email_enabled: false
  webhook_enabled: false
`

	_, err = file.WriteString(exampleConfig)
	return err
}

// Validate 验证配置
func (c *Config) Validate() error {
	if c.Server.TCPPort <= 0 || c.Server.TCPPort > 65535 {
		return fmt.Errorf("invalid TCP port: %d", c.Server.TCPPort)
	}

	if c.Server.WebPort <= 0 || c.Server.WebPort > 65535 {
		return fmt.Errorf("invalid web port: %d", c.Server.WebPort)
	}

	if c.Server.MaxConnections <= 0 {
		return fmt.Errorf("max connections must be positive")
	}

	if c.Database.Path == "" {
		return fmt.Errorf("database path is required")
	}

	if c.Security.EncryptionKey == "" || c.Security.EncryptionKey == "default-encryption-key-change-me" {
		logger.Warn("Using default encryption key, please change it in production")
	}

	if c.Security.SecretKey == "" || c.Security.SecretKey == "default-secret-key-change-me" {
		logger.Warn("Using default secret key, please change it in production")
	}

	if c.Security.JWTSecret == "" || c.Security.JWTSecret == "default-jwt-secret-change-me" {
		logger.Warn("Using default JWT secret, please change it in production")
	}

	return nil
}

// GetDatabasePath 获取数据库路径
func (c *Config) GetDatabasePath() string {
	if filepath.IsAbs(c.Database.Path) {
		return c.Database.Path
	}
	return filepath.Join(".", c.Database.Path)
}

// GetLogPath 获取日志路径
func (c *Config) GetLogPath() string {
	if filepath.IsAbs(c.Logger.Filename) {
		return c.Logger.Filename
	}
	return filepath.Join(".", c.Logger.Filename)
}

// GetStaticPath 获取静态文件路径
func (c *Config) GetStaticPath() string {
	if filepath.IsAbs(c.Web.StaticPath) {
		return c.Web.StaticPath
	}
	return filepath.Join(".", c.Web.StaticPath)
}

// GetTemplatePath 获取模板路径
func (c *Config) GetTemplatePath() string {
	if filepath.IsAbs(c.Web.TemplatePath) {
		return c.Web.TemplatePath
	}
	return filepath.Join(".", c.Web.TemplatePath)
}