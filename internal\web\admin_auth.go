package web

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"go.uber.org/zap"

	"qweb-verification/internal/database"
	"qweb-verification/internal/logger"
	"qweb-verification/internal/models"
	"qweb-verification/internal/security"
)

// AdminAuthService 管理员认证服务
type AdminAuthService struct {
	db        *database.Database
	jwtSecret []byte
	encryptor security.Encryptor
	securityMW *SecurityMiddleware
}

// AdminClaims JWT声明
type AdminClaims struct {
	AdminID  uint   `json:"admin_id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	jwt.RegisteredClaims
}

// AdminInfo 管理员信息
type AdminInfo struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	Email    string `json:"email"`
	LastLogin *time.Time `json:"last_login"`
	CreatedAt time.Time `json:"created_at"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	AdminInfo AdminInfo `json:"admin_info"`
}

// NewAdminAuthService 创建管理员认证服务
func NewAdminAuthService(db *database.Database, jwtSecret string) *AdminAuthService {
	encryptor := security.NewAESEncryptor("admin-auth-key")
	
	service := &AdminAuthService{
		db:        db,
		jwtSecret: []byte(jwtSecret),
		encryptor: encryptor,
	}

	// 初始化默认管理员
	service.initDefaultAdmin()

	return service
}

// SetSecurityMiddleware 设置安全中间件
func (a *AdminAuthService) SetSecurityMiddleware(securityMW *SecurityMiddleware) {
	a.securityMW = securityMW
}

// initDefaultAdmin 初始化默认管理员账户
func (a *AdminAuthService) initDefaultAdmin() {
	repo := database.NewRepository(a.db.GetDB())
	
	// 检查是否已存在管理员
	var count int64
	a.db.GetDB().Model(&models.SystemConfig{}).Where("key = ?", "admin.initialized").Count(&count)
	if count > 0 {
		return
	}

	// 创建默认管理员账户
	salt := a.encryptor.GenerateSalt()
	passwordHash := a.encryptor.HashPassword("admin123", salt)

	adminConfig := &models.SystemConfig{
		Key:      "admin.default",
		Value:    fmt.Sprintf(`{"username":"admin","password_hash":"%s","salt":"%s","role":"super_admin","email":"<EMAIL>"}`, passwordHash, salt),
		Type:     models.ConfigTypeJSON,
		Category: "admin",
		Comment:  "默认管理员账户",
	}

	repo.SetConfig(adminConfig)

	// 标记已初始化
	initFlag := &models.SystemConfig{
		Key:      "admin.initialized",
		Value:    "true",
		Type:     models.ConfigTypeBool,
		Category: "admin",
		Comment:  "管理员初始化标记",
	}
	repo.SetConfig(initFlag)

	logger.Info("Default admin account initialized", zap.String("username", "admin"))
}

// Login 管理员登录
func (a *AdminAuthService) Login(username, password, clientIP string) (*LoginResponse, error) {
	repo := database.NewRepository(a.db.GetDB())
	
	// 获取管理员信息
	adminConfig, err := repo.GetConfig("admin.default")
	if err != nil {
		// 记录登录失败
		if a.securityMW != nil {
			a.securityMW.RecordLoginFailure(clientIP)
		}
		return nil, fmt.Errorf("admin not found")
	}

	// 解析管理员数据
	var adminData map[string]interface{}
	if err := json.Unmarshal([]byte(adminConfig.Value), &adminData); err != nil {
		if a.securityMW != nil {
			a.securityMW.RecordLoginFailure(clientIP)
		}
		return nil, fmt.Errorf("invalid admin data")
	}

	// 验证用户名
	if adminData["username"] != username {
		if a.securityMW != nil {
			a.securityMW.RecordLoginFailure(clientIP)
		}
		return nil, fmt.Errorf("invalid credentials")
	}

	// 验证密码
	storedHash := adminData["password_hash"].(string)
	salt := adminData["salt"].(string)
	
	if !a.encryptor.VerifyPassword(password, storedHash, salt) {
		if a.securityMW != nil {
			a.securityMW.RecordLoginFailure(clientIP)
		}
		return nil, fmt.Errorf("invalid credentials")
	}

	// 登录成功，清除失败记录
	if a.securityMW != nil {
		a.securityMW.ClearLoginAttempts(clientIP)
	}

	// 生成JWT令牌
	now := time.Now()
	expiresAt := now.Add(24 * time.Hour)

	claims := &AdminClaims{
		AdminID:  1, // 默认管理员ID
		Username: username,
		Role:     adminData["role"].(string),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "qweb-verification",
			Subject:   username,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(a.jwtSecret)
	if err != nil {
		return nil, fmt.Errorf("failed to generate token")
	}

	// 更新最后登录时间
	adminData["last_login"] = now.Format(time.RFC3339)
	updatedValue, _ := json.Marshal(adminData)
	adminConfig.Value = string(updatedValue)
	repo.SetConfig(adminConfig)

	// 记录登录日志
	logEntry := &models.OperationLog{
		Operation: "admin_login",
		Details:   fmt.Sprintf("Admin %s logged in from IP %s", username, clientIP),
		Success:   true,
	}
	repo.CreateOperationLog(logEntry)

	return &LoginResponse{
		Token:     tokenString,
		ExpiresAt: expiresAt,
		AdminInfo: AdminInfo{
			ID:       1,
			Username: username,
			Role:     adminData["role"].(string),
			Email:    adminData["email"].(string),
		},
	}, nil
}

// ValidateToken 验证JWT令牌
func (a *AdminAuthService) ValidateToken(tokenString string) (*AdminClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &AdminClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return a.jwtSecret, nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*AdminClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// RequireAuth 认证中间件
func (a *AdminAuthService) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   true,
				"message": "Missing authorization header",
			})
			c.Abort()
			return
		}

		tokenParts := strings.SplitN(authHeader, " ", 2)
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   true,
				"message": "Invalid authorization header format",
			})
			c.Abort()
			return
		}

		claims, err := a.ValidateToken(tokenParts[1])
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   true,
				"message": "Invalid token",
			})
			c.Abort()
			return
		}

		// 将管理员信息存储到上下文
		c.Set("admin_id", claims.AdminID)
		c.Set("admin_username", claims.Username)
		c.Set("admin_role", claims.Role)

		c.Next()
	}
}

// RequireRole 角色权限中间件
func (a *AdminAuthService) RequireRole(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		adminRole, exists := c.Get("admin_role")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   true,
				"message": "Role not found in context",
			})
			c.Abort()
			return
		}

		roleStr := adminRole.(string)
		for _, role := range roles {
			if roleStr == role || roleStr == "super_admin" {
				c.Next()
				return
			}
		}

		c.JSON(http.StatusForbidden, gin.H{
			"error":   true,
			"message": "Insufficient permissions",
		})
		c.Abort()
	}
}

// GetAdminInfo 获取管理员信息
func (a *AdminAuthService) GetAdminInfo(adminID uint) (*AdminInfo, error) {
	repo := database.NewRepository(a.db.GetDB())
	
	adminConfig, err := repo.GetConfig("admin.default")
	if err != nil {
		return nil, fmt.Errorf("admin not found")
	}

	var adminData map[string]interface{}
	if err := json.Unmarshal([]byte(adminConfig.Value), &adminData); err != nil {
		return nil, fmt.Errorf("invalid admin data")
	}

	info := &AdminInfo{
		ID:       adminID,
		Username: adminData["username"].(string),
		Role:     adminData["role"].(string),
		Email:    adminData["email"].(string),
	}

	if lastLoginStr, exists := adminData["last_login"]; exists && lastLoginStr != nil {
		if lastLogin, err := time.Parse(time.RFC3339, lastLoginStr.(string)); err == nil {
			info.LastLogin = &lastLogin
		}
	}

	return info, nil
}

// ChangePassword 修改管理员密码
func (a *AdminAuthService) ChangePassword(adminID uint, oldPassword, newPassword string) error {
	repo := database.NewRepository(a.db.GetDB())
	
	adminConfig, err := repo.GetConfig("admin.default")
	if err != nil {
		return fmt.Errorf("admin not found")
	}

	var adminData map[string]interface{}
	if err := json.Unmarshal([]byte(adminConfig.Value), &adminData); err != nil {
		return fmt.Errorf("invalid admin data")
	}

	// 验证旧密码
	storedHash := adminData["password_hash"].(string)
	salt := adminData["salt"].(string)
	
	if !a.encryptor.VerifyPassword(oldPassword, storedHash, salt) {
		return fmt.Errorf("invalid old password")
	}

	// 生成新密码哈希
	newSalt := a.encryptor.GenerateSalt()
	newHash := a.encryptor.HashPassword(newPassword, newSalt)

	// 更新密码
	adminData["password_hash"] = newHash
	adminData["salt"] = newSalt
	adminData["updated_at"] = time.Now().Format(time.RFC3339)

	updatedValue, _ := json.Marshal(adminData)
	adminConfig.Value = string(updatedValue)
	
	return repo.SetConfig(adminConfig)
}

// Logout 管理员登出（这里主要是记录日志，JWT本身是无状态的）
func (a *AdminAuthService) Logout(adminID uint, username string) error {
	repo := database.NewRepository(a.db.GetDB())
	
	// 记录登出日志
	logEntry := &models.OperationLog{
		Operation: "admin_logout",
		Details:   fmt.Sprintf("Admin %s logged out", username),
		Success:   true,
	}
	
	return repo.CreateOperationLog(logEntry)
}

// 工具函数

// GetPaginationParams 获取分页参数
func GetPaginationParams(c *gin.Context) (page, size, offset int) {
	page = 1
	size = 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if sizeStr := c.Query("size"); sizeStr != "" {
		if s, err := strconv.Atoi(sizeStr); err == nil && s > 0 && s <= 100 {
			size = s
		}
	}

	offset = (page - 1) * size
	return
}

// GetFilterParams 获取过滤参数
func GetFilterParams(c *gin.Context) map[string]interface{} {
	filters := make(map[string]interface{})

	if status := c.Query("status"); status != "" {
		if s, err := strconv.Atoi(status); err == nil {
			filters["status"] = s
		}
	}

	if agentID := c.Query("agent_id"); agentID != "" {
		if id, err := strconv.ParseUint(agentID, 10, 32); err == nil {
			filters["agent_id"] = uint(id)
		}
	}

	if userID := c.Query("user_id"); userID != "" {
		if id, err := strconv.ParseUint(userID, 10, 32); err == nil {
			filters["user_id"] = uint(id)
		}
	}

	if keyword := c.Query("keyword"); keyword != "" {
		filters["keyword"] = keyword
	}

	return filters
}

