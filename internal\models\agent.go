package models

import (
	"time"
	"gorm.io/gorm"
)

// Agent 代理商表模型
type Agent struct {
	ID             uint           `json:"id" gorm:"primaryKey"`
	Username       string         `json:"username" gorm:"uniqueIndex;not null;size:50"`
	PasswordHash   string         `json:"-" gorm:"not null;size:128"`
	Salt           string         `json:"-" gorm:"not null;size:32"`
	ParentID       *uint          `json:"parent_id" gorm:"index"`
	Parent         *Agent         `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Level          int            `json:"level" gorm:"not null;default:1"`
	CommissionRate float64        `json:"commission_rate" gorm:"type:decimal(5,2);default:0.00"`
	Status         int            `json:"status" gorm:"default:1"` // 1:启用 0:禁用
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Children       []Agent        `json:"children,omitempty" gorm:"foreignKey:ParentID"`
	Users          []User         `json:"users,omitempty" gorm:"foreignKey:AgentID"`
	Cards          []Card         `json:"cards,omitempty" gorm:"foreignKey:AgentID"`
	Commissions    []Commission   `json:"commissions,omitempty" gorm:"foreignKey:AgentID"`
}

// TableName 指定表名
func (Agent) TableName() string {
	return "agents"
}

// IsActive 检查代理商是否激活
func (a *Agent) IsActive() bool {
	return a.Status == 1
}

// BeforeCreate GORM钩子：创建前
func (a *Agent) BeforeCreate(tx *gorm.DB) error {
	a.CreatedAt = time.Now()
	a.UpdatedAt = time.Now()
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (a *Agent) BeforeUpdate(tx *gorm.DB) error {
	a.UpdatedAt = time.Now()
	return nil
}

// Commission 佣金记录表模型
type Commission struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	AgentID   uint      `json:"agent_id" gorm:"not null;index"`
	Agent     Agent     `json:"agent,omitempty" gorm:"foreignKey:AgentID"`
	CardID    uint      `json:"card_id" gorm:"not null;index"`
	Card      Card      `json:"card,omitempty" gorm:"foreignKey:CardID"`
	Amount    float64   `json:"amount" gorm:"type:decimal(10,2);not null"`
	Rate      float64   `json:"rate" gorm:"type:decimal(5,2);not null"`
	Type      string    `json:"type" gorm:"size:20;not null"` // direct:直接佣金 indirect:间接佣金
	Status    int       `json:"status" gorm:"default:0"` // 0:待结算 1:已结算
	CreatedAt time.Time `json:"created_at"`
}

// TableName 指定表名
func (Commission) TableName() string {
	return "commissions"
}