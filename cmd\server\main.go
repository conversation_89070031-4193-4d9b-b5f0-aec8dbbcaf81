package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"qweb-verification/internal/agent"
	"qweb-verification/internal/auth"
	"qweb-verification/internal/config"
	"qweb-verification/internal/database"
	"qweb-verification/internal/logger"
	"qweb-verification/internal/monitor"
	"qweb-verification/internal/security"
	"qweb-verification/internal/server"
	"qweb-verification/internal/web"

	"go.uber.org/zap"
)

const (
	appName    = "Qweb网络验证系统"
	appVersion = "1.0.0"
)

var (
	configFile = flag.String("config", "configs/config.yaml", "配置文件路径")
	initDB     = flag.Bool("init-db", false, "初始化数据库")
	version    = flag.Bool("version", false, "显示版本信息")
	help       = flag.Bool("help", false, "显示帮助信息")
)

// Application 应用程序结构
type Application struct {
	config         *config.Config
	database       *database.Database
	securityMgr    *security.SecurityManager
	authService    *auth.AuthenticationService
	agentService   agent.AgentService
	tcpServer      *server.TCPServer
	webServer      *web.WebServer
	metricsCollector monitor.MetricsCollector
	ctx            context.Context
	cancel         context.CancelFunc
	wg             sync.WaitGroup
}

func main() {
	flag.Parse()

	// 显示版本信息
	if *version {
		fmt.Printf("%s v%s\n", appName, appVersion)
		os.Exit(0)
	}

	// 显示帮助信息
	if *help {
		fmt.Printf("%s v%s\n\n", appName, appVersion)
		fmt.Println("Usage:")
		flag.PrintDefaults()
		os.Exit(0)
	}

	// 创建应用程序
	app, err := NewApplication(*configFile)
	if err != nil {
		fmt.Printf("Failed to create application: %v\n", err)
		os.Exit(1)
	}

	// 初始化数据库
	if *initDB {
		if err := app.InitDatabase(); err != nil {
			fmt.Printf("Failed to initialize database: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("Database initialized successfully")
		return
	}

	// 启动应用程序
	if err := app.Start(); err != nil {
		fmt.Printf("Failed to start application: %v\n", err)
		os.Exit(1)
	}

	// 等待退出信号
	app.WaitForShutdown()

	// 停止应用程序
	if err := app.Stop(); err != nil {
		fmt.Printf("Failed to stop application: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("Application stopped gracefully")
}

// NewApplication 创建新的应用程序实例
func NewApplication(configFile string) (*Application, error) {
	// 加载配置
	cfg, err := config.LoadConfig(configFile)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	// 初始化日志
	if err := logger.InitGlobalLogger(&logger.LoggerConfig{
		Level:      cfg.Logger.Level,
		Format:     cfg.Logger.Format,
		Output:     cfg.Logger.Output,
		Filename:   cfg.GetLogPath(),
		MaxSize:    cfg.Logger.MaxSize,
		MaxBackups: cfg.Logger.MaxBackups,
		MaxAge:     cfg.Logger.MaxAge,
		Compress:   cfg.Logger.Compress,
	}); err != nil {
		return nil, fmt.Errorf("failed to initialize logger: %w", err)
	}

	logger.Info("Starting application",
		zap.String("name", appName),
		zap.String("version", appVersion),
		zap.String("config", configFile),
	)

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())

	return &Application{
		config: cfg,
		ctx:    ctx,
		cancel: cancel,
	}, nil
}

// InitDatabase 初始化数据库
func (app *Application) InitDatabase() error {
	// 创建数据库连接
	dbConfig := &database.Config{
		Path:            app.config.GetDatabasePath(),
		MaxIdleConns:    app.config.Database.MaxIdleConns,
		MaxOpenConns:    app.config.Database.MaxOpenConns,
		ConnMaxLifetime: app.config.Database.ConnMaxLifetime,
		LogLevel:        app.config.Database.LogLevel,
	}

	db, err := database.NewDatabase(dbConfig)
	if err != nil {
		return fmt.Errorf("failed to create database: %w", err)
	}

	// 执行数据库迁移
	if err := db.Migrate(); err != nil {
		return fmt.Errorf("failed to migrate database: %w", err)
	}

	// 关闭数据库连接
	return db.Close()
}

// Start 启动应用程序
func (app *Application) Start() error {
	logger.Info("Starting application components...")

	// 创建数据库连接
	if err := app.initDatabase(); err != nil {
		return fmt.Errorf("failed to initialize database: %w", err)
	}

	// 创建安全管理器
	if err := app.initSecurity(); err != nil {
		return fmt.Errorf("failed to initialize security: %w", err)
	}

	// 创建认证服务
	if err := app.initAuth(); err != nil {
		return fmt.Errorf("failed to initialize auth: %w", err)
	}

	// 创建代理商服务
	if err := app.initAgent(); err != nil {
		return fmt.Errorf("failed to initialize agent: %w", err)
	}

	// 创建监控服务
	if err := app.initMonitor(); err != nil {
		return fmt.Errorf("failed to initialize monitor: %w", err)
	}

	// 启动TCP服务器
	if err := app.startTCPServer(); err != nil {
		return fmt.Errorf("failed to start TCP server: %w", err)
	}

	// 启动Web服务器
	if err := app.startWebServer(); err != nil {
		return fmt.Errorf("failed to start web server: %w", err)
	}

	logger.Info("Application started successfully",
		zap.Int("tcp_port", app.config.Server.TCPPort),
		zap.Int("web_port", app.config.Server.WebPort),
	)

	return nil
}

// Stop 停止应用程序
func (app *Application) Stop() error {
	logger.Info("Stopping application...")

	// 取消上下文
	app.cancel()

	// 停止TCP服务器
	if app.tcpServer != nil {
		if err := app.tcpServer.Stop(); err != nil {
			logger.Error("Failed to stop TCP server", zap.Error(err))
		}
	}

	// 停止Web服务器
	if app.webServer != nil {
		shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()
		if err := app.webServer.Shutdown(shutdownCtx); err != nil {
			logger.Error("Failed to stop web server", zap.Error(err))
		}
	}

	// 停止监控服务
	if app.metricsCollector != nil {
		if err := app.metricsCollector.Stop(); err != nil {
			logger.Error("Failed to stop metrics collector", zap.Error(err))
		}
	}

	// 关闭数据库连接
	if app.database != nil {
		if err := app.database.Close(); err != nil {
			logger.Error("Failed to close database", zap.Error(err))
		}
	}

	// 等待所有协程结束
	app.wg.Wait()

	// 同步日志
	logger.GetLogger().Sync()

	logger.Info("Application stopped")
	return nil
}

// WaitForShutdown 等待关闭信号
func (app *Application) WaitForShutdown() {
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	sig := <-sigCh
	logger.Info("Received shutdown signal", zap.String("signal", sig.String()))
}

// 初始化方法

func (app *Application) initDatabase() error {
	dbConfig := &database.Config{
		Path:            app.config.GetDatabasePath(),
		MaxIdleConns:    app.config.Database.MaxIdleConns,
		MaxOpenConns:    app.config.Database.MaxOpenConns,
		ConnMaxLifetime: app.config.Database.ConnMaxLifetime,
		LogLevel:        app.config.Database.LogLevel,
	}

	db, err := database.NewDatabase(dbConfig)
	if err != nil {
		return err
	}

	// 执行数据库迁移
	if err := db.Migrate(); err != nil {
		return err
	}

	app.database = db
	logger.Info("Database initialized", zap.String("path", dbConfig.Path))
	return nil
}

func (app *Application) initSecurity() error {
	secConfig := &security.SecurityConfig{
		TLS:                        app.config.Security.TLS,
		MaxFailedAttempts:          app.config.Security.MaxFailedAttempts,
		BanDuration:                app.config.Security.BanDuration,
		RateLimit:                  app.config.Security.RateLimit,
		RateBurst:                  app.config.Security.RateBurst,
		SessionTimeout:             app.config.Security.SessionTimeout,
		EncryptionKey:              app.config.Security.EncryptionKey,
		EnableBruteForceProtection: app.config.Security.EnableBruteForceProtection,
	}

	secMgr, err := security.NewSecurityManager(secConfig)
	if err != nil {
		return err
	}

	app.securityMgr = secMgr
	logger.Info("Security manager initialized")
	return nil
}

func (app *Application) initAuth() error {
	repo := database.NewRepository(app.database.GetDB())
	encryptor := app.securityMgr.GetEncryptor()
	sessionTimeout := app.securityMgr.GetSessionTimeout()

	app.authService = auth.NewAuthenticationService(repo, encryptor, app.config.Security.SecretKey, sessionTimeout)
	logger.Info("Authentication service initialized")
	return nil
}

func (app *Application) initAgent() error {
	repo := database.NewRepository(app.database.GetDB())
	encryptor := app.securityMgr.GetEncryptor()

	app.agentService = agent.NewAgentService(repo, encryptor)
	logger.Info("Agent service initialized")
	return nil
}

func (app *Application) initMonitor() error {
	if !app.config.Monitor.Enabled {
		logger.Info("Monitoring disabled")
		return nil
	}

	repo := database.NewRepository(app.database.GetDB())
	app.metricsCollector = monitor.NewMetricsCollector(repo)

	if err := app.metricsCollector.Start(app.ctx); err != nil {
		return err
	}

	logger.Info("Metrics collector started")
	return nil
}

func (app *Application) startTCPServer() error {
	serverConfig := &server.ServerConfig{
		Port:              app.config.Server.TCPPort,
		MaxConnections:    app.config.Server.MaxConnections,
		ConnTimeout:       app.config.Server.ConnectionTimeout,
		HeartbeatInterval: app.config.Server.HeartbeatInterval,
		TLS:               app.config.Security.TLS,
	}

	repo := database.NewRepository(app.database.GetDB())
	app.tcpServer = server.NewTCPServer(serverConfig, app.authService, app.securityMgr, repo)

	if err := app.tcpServer.Start(); err != nil {
		return err
	}

	logger.Info("TCP server started", zap.Int("port", app.config.Server.TCPPort))
	return nil
}

func (app *Application) startWebServer() error {
	webConfig := &web.Config{
		Port:         app.config.Web.Port,
		Host:         app.config.Web.Host,
		StaticPath:   app.config.GetStaticPath(),
		TemplatePath: app.config.GetTemplatePath(),
		JWTSecret:    app.config.Web.JWTSecret,
		CORSEnabled:  app.config.Web.CORSEnabled,
		CORSOrigins:  app.config.Web.CORSOrigins,
	}

	deps := &web.Dependencies{
		Database:         app.database,
		AuthService:      app.authService,
		AgentService:     app.agentService,
		SecurityManager:  app.securityMgr,
		MetricsCollector: app.metricsCollector,
		TCPServer:        app.tcpServer,
	}

	webServer, err := web.NewWebServer(webConfig, deps)
	if err != nil {
		return err
	}

	app.webServer = webServer

	// 在后台启动Web服务器
	app.wg.Add(1)
	go func() {
		defer app.wg.Done()
		if err := app.webServer.Start(); err != nil {
			logger.Error("Web server error", zap.Error(err))
		}
	}()

	logger.Info("Web server started", zap.Int("port", app.config.Web.Port))
	return nil
}