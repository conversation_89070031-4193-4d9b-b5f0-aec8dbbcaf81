package database

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	
	"qweb-verification/internal/models"
)

// Database 数据库连接包装器
type Database struct {
	DB *gorm.DB
}

// Config 数据库配置
type Config struct {
	Path            string        `yaml:"path"`
	MaxIdleConns    int           `yaml:"max_idle_conns"`
	MaxOpenConns    int           `yaml:"max_open_conns"`
	ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
	LogLevel        string        `yaml:"log_level"`
}

// DefaultConfig 返回默认数据库配置
func DefaultConfig() *Config {
	return &Config{
		Path:            "data/verification.db",
		MaxIdleConns:    10,
		MaxOpenConns:    100,
		ConnMaxLifetime: time.Hour,
		LogLevel:        "info",
	}
}

// NewDatabase 创建新的数据库连接
func NewDatabase(config *Config) (*Database, error) {
	if config == nil {
		config = DefaultConfig()
	}

	// 确保数据目录存在
	dir := filepath.Dir(config.Path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create data directory: %w", err)
	}

	// 配置GORM日志级别
	logLevel := logger.Info
	switch config.LogLevel {
	case "silent":
		logLevel = logger.Silent
	case "error":
		logLevel = logger.Error
	case "warn":
		logLevel = logger.Warn
	case "info":
		logLevel = logger.Info
	}

	// GORM配置
	gormConfig := &gorm.Config{
		Logger: logger.New(
			log.New(os.Stdout, "\r\n", log.LstdFlags),
			logger.Config{
				SlowThreshold:             time.Second,
				LogLevel:                  logLevel,
				IgnoreRecordNotFoundError: true,
				Colorful:                  true,
			},
		),
		// 禁用外键约束，让应用层处理
		DisableForeignKeyConstraintWhenMigrating: true,
	}

	// 连接数据库
	db, err := gorm.Open(sqlite.Open(config.Path), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 获取底层SQL DB
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(config.MaxIdleConns)
	sqlDB.SetMaxOpenConns(config.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(config.ConnMaxLifetime)

	// 启用WAL模式和其他SQLite优化
	if err := optimizeSQLite(db); err != nil {
		return nil, fmt.Errorf("failed to optimize SQLite: %w", err)
	}

	return &Database{DB: db}, nil
}

// optimizeSQLite 优化SQLite配置
func optimizeSQLite(db *gorm.DB) error {
	// 启用WAL模式
	if err := db.Exec("PRAGMA journal_mode=WAL").Error; err != nil {
		return err
	}

	// 启用外键约束
	if err := db.Exec("PRAGMA foreign_keys=ON").Error; err != nil {
		return err
	}

	// 设置同步模式
	if err := db.Exec("PRAGMA synchronous=NORMAL").Error; err != nil {
		return err
	}

	// 设置缓存大小 (10MB)
	if err := db.Exec("PRAGMA cache_size=10000").Error; err != nil {
		return err
	}

	// 设置临时存储模式
	if err := db.Exec("PRAGMA temp_store=MEMORY").Error; err != nil {
		return err
	}

	// 设置mmap大小 (256MB)
	if err := db.Exec("PRAGMA mmap_size=268435456").Error; err != nil {
		return err
	}

	return nil
}

// Migrate 执行数据库迁移
func (d *Database) Migrate() error {
	// 按依赖顺序执行迁移
	err := d.DB.AutoMigrate(
		&models.Agent{},
		&models.User{},
		&models.Card{},
		&models.Session{},
		&models.Commission{},
		&models.OperationLog{},
		&models.IPBlacklist{},
		&models.SystemConfig{},
		&models.OnlineUser{},
	)
	
	if err != nil {
		return fmt.Errorf("failed to migrate database: %w", err)
	}

	// 创建索引
	if err := d.createIndexes(); err != nil {
		return fmt.Errorf("failed to create indexes: %w", err)
	}

	// 初始化系统数据
	if err := d.initSystemData(); err != nil {
		return fmt.Errorf("failed to initialize system data: %w", err)
	}

	return nil
}

// createIndexes 创建额外的索引
func (d *Database) createIndexes() error {
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_operation_logs_created_at ON operation_logs(created_at)",
		"CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON sessions(expires_at)",
		"CREATE INDEX IF NOT EXISTS idx_cards_status ON cards(status)",
		"CREATE INDEX IF NOT EXISTS idx_cards_expires_at ON cards(expires_at)",
		"CREATE INDEX IF NOT EXISTS idx_ip_blacklist_banned_until ON ip_blacklist(banned_until)",
		"CREATE INDEX IF NOT EXISTS idx_online_users_last_active ON online_users(last_active)",
		"CREATE INDEX IF NOT EXISTS idx_commissions_status ON commissions(status)",
	}

	for _, index := range indexes {
		if err := d.DB.Exec(index).Error; err != nil {
			return fmt.Errorf("failed to create index: %s, error: %w", index, err)
		}
	}

	return nil
}

// initSystemData 初始化系统数据
func (d *Database) initSystemData() error {
	// 初始化系统配置
	configs := []models.SystemConfig{
		{
			Key:      "system.name",
			Value:    "Qweb网络验证系统",
			Type:     models.ConfigTypeString,
			Category: models.ConfigCategorySystem,
			Comment:  "系统名称",
		},
		{
			Key:      "system.version",
			Value:    "1.0.0",
			Type:     models.ConfigTypeString,
			Category: models.ConfigCategorySystem,
			Comment:  "系统版本",
		},
		{
			Key:      "security.max_failed_attempts",
			Value:    "5",
			Type:     models.ConfigTypeInt,
			Category: models.ConfigCategorySecurity,
			Comment:  "最大失败尝试次数",
		},
		{
			Key:      "security.ban_duration",
			Value:    "1800", // 30分钟
			Type:     models.ConfigTypeInt,
			Category: models.ConfigCategorySecurity,
			Comment:  "封禁时长（秒）",
		},
		{
			Key:      "security.session_timeout",
			Value:    "86400", // 24小时
			Type:     models.ConfigTypeInt,
			Category: models.ConfigCategorySecurity,
			Comment:  "会话超时时间（秒）",
		},
		{
			Key:      "agent.default_commission_rate",
			Value:    "0.10", // 10%
			Type:     models.ConfigTypeString,
			Category: models.ConfigCategoryAgent,
			Comment:  "默认佣金比例",
		},
	}

	for _, config := range configs {
		var existingConfig models.SystemConfig
		err := d.DB.Where("key = ?", config.Key).First(&existingConfig).Error
		if err == gorm.ErrRecordNotFound {
			// 配置不存在，创建新配置
			if err := d.DB.Create(&config).Error; err != nil {
				return fmt.Errorf("failed to create system config %s: %w", config.Key, err)
			}
		}
	}

	return nil
}

// Close 关闭数据库连接
func (d *Database) Close() error {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// GetDB 获取GORM数据库实例
func (d *Database) GetDB() *gorm.DB {
	return d.DB
}

// HealthCheck 健康检查
func (d *Database) HealthCheck() error {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}

// GetStats 获取数据库统计信息
func (d *Database) GetStats() (map[string]interface{}, error) {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return nil, err
	}

	stats := sqlDB.Stats()
	
	return map[string]interface{}{
		"max_open_connections": stats.MaxOpenConnections,
		"open_connections":     stats.OpenConnections,
		"in_use":              stats.InUse,
		"idle":                stats.Idle,
		"wait_count":          stats.WaitCount,
		"wait_duration":       stats.WaitDuration.String(),
		"max_idle_closed":     stats.MaxIdleClosed,
		"max_idle_time_closed": stats.MaxIdleTimeClosed,
		"max_lifetime_closed": stats.MaxLifetimeClosed,
	}, nil
}