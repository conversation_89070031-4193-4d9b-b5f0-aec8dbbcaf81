<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qweb网络验证系统 - 管理控制台</title>
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="shortcut icon" href="/static/img/favicon.ico">
</head>
<body class="admin-layout">
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <svg class="logo-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                </svg>
                <span class="logo-text">Qweb验证系统</span>
            </div>
        </div>

        <div class="sidebar-content">
            <ul class="nav-menu">
                <li>
                    <a href="#" class="nav-item active" data-page="dashboard">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                        </svg>
                        <span class="nav-text">仪表板</span>
                    </a>
                </li>
                <li>
                    <a href="#" class="nav-item" data-page="users">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                        </svg>
                        <span class="nav-text">用户管理</span>
                    </a>
                </li>
                <li>
                    <a href="#" class="nav-item" data-page="agents">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                        <span class="nav-text">代理商管理</span>
                    </a>
                </li>
                <li>
                    <a href="#" class="nav-item" data-page="cards">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <rect x="1" y="4" width="22" height="16" rx="2" ry="2"/>
                            <line x1="1" y1="10" x2="23" y2="10"/>
                        </svg>
                        <span class="nav-text">卡号管理</span>
                    </a>
                </li>
                <li>
                    <a href="#" class="nav-item" data-page="logs">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        <span class="nav-text">操作日志</span>
                    </a>
                </li>
                <li>
                    <a href="#" class="nav-item" data-page="settings">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <circle cx="12" cy="12" r="3"/>
                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                        </svg>
                        <span class="nav-text">系统设置</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="main-content" id="main-content-wrapper">
        <!-- 顶部导航栏 -->
        <header class="header">
            <div class="header-left">
                <button class="sidebar-toggle" data-toggle-sidebar>
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <line x1="3" y1="6" x2="21" y2="6"/>
                        <line x1="3" y1="12" x2="21" y2="12"/>
                        <line x1="3" y1="18" x2="21" y2="18"/>
                    </svg>
                </button>
                <div class="breadcrumb">
                    <span id="current-page-title">仪表板</span>
                </div>
            </div>

            <div class="header-right">
                <!-- 实时状态指示器 -->
                <div class="status-indicators">
                    <div class="status-indicator" id="ws-status" title="WebSocket连接状态">
                        <div class="status-dot status-success"></div>
                        <span class="status-text">在线</span>
                    </div>
                </div>

                <!-- 通知 -->
                <div class="notification-dropdown">
                    <button class="notification-btn" onclick="toggleNotifications()">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
                            <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
                        </svg>
                        <span class="notification-badge" id="notification-count">0</span>
                    </button>
                    <div class="notification-panel" id="notification-panel">
                        <div class="notification-header">
                            <h3>通知</h3>
                            <button class="btn btn-sm btn-secondary" onclick="clearAllNotifications()">清空</button>
                        </div>
                        <div class="notification-list" id="notification-list">
                            <div class="notification-empty">
                                <p>暂无新通知</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户菜单 -->
                <div class="user-dropdown">
                    <button class="user-btn" onclick="toggleUserMenu()">
                        <div class="user-avatar">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                        </div>
                        <span class="user-name" id="user-name">管理员</span>
                        <svg class="dropdown-arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <polyline points="6,9 12,15 18,9"/>
                        </svg>
                    </button>
                    <div class="user-menu" id="user-menu">
                        <a href="#" class="user-menu-item">
                            <svg class="menu-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                            个人资料
                        </a>
                        <a href="#" class="user-menu-item">
                            <svg class="menu-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="12" cy="12" r="3"/>
                                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                            </svg>
                            设置
                        </a>
                        <hr class="user-menu-divider">
                        <a href="#" class="user-menu-item" data-logout>
                            <svg class="menu-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                            </svg>
                            退出登录
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- 页面内容区域 -->
        <main class="content" id="main-content">
            <!-- 内容将通过JavaScript动态加载 -->
            <div class="loading-container">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>加载中...</p>
                </div>
            </div>
        </main>
    </div>

    <!-- 通知容器 -->
    <div id="alerts-container" class="alerts-container"></div>

    <!-- 通用模态框容器 -->
    <div class="modal-container" id="modal-container"></div>

    <!-- 加载主JavaScript文件 -->
    <script src="/static/js/main.js"></script>
    <script>
        // 额外的界面交互逻辑
        
        // 切换通知面板
        function toggleNotifications() {
            const panel = document.getElementById('notification-panel');
            panel.classList.toggle('show');
            
            // 点击外部关闭
            document.addEventListener('click', function closeNotifications(e) {
                if (!e.target.closest('.notification-dropdown')) {
                    panel.classList.remove('show');
                    document.removeEventListener('click', closeNotifications);
                }
            });
        }
        
        // 切换用户菜单
        function toggleUserMenu() {
            const menu = document.getElementById('user-menu');
            menu.classList.toggle('show');
            
            // 点击外部关闭
            document.addEventListener('click', function closeUserMenu(e) {
                if (!e.target.closest('.user-dropdown')) {
                    menu.classList.remove('show');
                    document.removeEventListener('click', closeUserMenu);
                }
            });
        }
        
        // 清空所有通知
        function clearAllNotifications() {
            const notificationList = document.getElementById('notification-list');
            notificationList.innerHTML = '<div class="notification-empty"><p>暂无新通知</p></div>';
            
            const badge = document.getElementById('notification-count');
            badge.textContent = '0';
            badge.style.display = 'none';
        }
        
        // 更新页面标题
        function updatePageTitle(title) {
            const titleElement = document.getElementById('current-page-title');
            if (titleElement) {
                titleElement.textContent = title;
            }
        }
        
        // 更新WebSocket状态
        function updateWebSocketStatus(connected) {
            const statusIndicator = document.getElementById('ws-status');
            const dot = statusIndicator.querySelector('.status-dot');
            const text = statusIndicator.querySelector('.status-text');
            
            if (connected) {
                dot.className = 'status-dot status-success';
                text.textContent = '在线';
                statusIndicator.title = 'WebSocket已连接';
            } else {
                dot.className = 'status-dot status-error';
                text.textContent = '离线';
                statusIndicator.title = 'WebSocket连接断开';
            }
        }
        
        // 添加通知
        function addNotification(notification) {
            const notificationList = document.getElementById('notification-list');
            const badge = document.getElementById('notification-count');
            
            // 移除空状态
            const emptyState = notificationList.querySelector('.notification-empty');
            if (emptyState) {
                emptyState.remove();
            }
            
            // 创建通知元素
            const notificationElement = document.createElement('div');
            notificationElement.className = 'notification-item';
            notificationElement.innerHTML = `
                <div class="notification-content">
                    <h4 class="notification-title">${notification.title}</h4>
                    <p class="notification-message">${notification.message}</p>
                    <span class="notification-time">${new Date(notification.timestamp).toLocaleString()}</span>
                </div>
                <button class="notification-close" onclick="this.parentElement.remove()">×</button>
            `;
            
            // 添加到列表顶部
            notificationList.insertBefore(notificationElement, notificationList.firstChild);
            
            // 更新徽章
            const currentCount = parseInt(badge.textContent) || 0;
            badge.textContent = currentCount + 1;
            badge.style.display = 'inline-block';
        }
    </script>
</body>
</html>