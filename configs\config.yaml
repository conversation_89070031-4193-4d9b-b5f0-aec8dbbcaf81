# Qweb网络验证系统配置文件

server:
  tcp_port: 9999
  web_port: 8080
  host: "0.0.0.0"
  max_connections: 1000
  connection_timeout: "5m"
  heartbeat_interval: "30s"

database:
  path: "data/verification.db"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: "1h"
  log_level: "info"

security:
  max_failed_attempts: 5
  ban_duration: "30m"
  rate_limit: 10.0
  rate_burst: 20
  session_timeout: "24h"
  encryption_key: "change-me-to-secure-key"
  enable_brute_force_protection: true
  secret_key: "change-me-to-secure-secret"
  jwt_secret: "change-me-to-jwt-secret"

logger:
  level: "info"
  format: "json"
  output: "both"
  filename: "logs/qweb.log"
  max_size: 100
  max_backups: 10
  max_age: 30
  compress: true

web:
  port: 8080
  host: "0.0.0.0"
  static_path: "web/static"
  template_path: "web/templates"
  jwt_secret: "change-me-to-jwt-secret"
  cors_enabled: true
  cors_origins: ["*"]

monitor:
  enabled: true
  metrics_interval: "30s"
  alerts_enabled: true
  email_enabled: false
  webhook_enabled: false
