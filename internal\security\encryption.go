package security

import (
	"crypto/aes"
	"crypto/cipher"
	cryptorand "crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"math/rand"
	"time"

	"golang.org/x/crypto/pbkdf2"
	"golang.org/x/crypto/scrypt"
)

// Encryptor 加密器接口
type Encryptor interface {
	Encrypt(plaintext string) (string, error)
	Decrypt(ciphertext string) (string, error)
	HashPassword(password, salt string) string
	GenerateSalt() string
	VerifyPassword(password, hash, salt string) bool
}

// AESEncryptor AES加密器实现
type AESEncryptor struct {
	key []byte
}

// NewAESEncryptor 创建新的AES加密器
func NewAESEncryptor(password string) *AESEncryptor {
	// 使用PBKDF2生成密钥
	key := pbkdf2.Key([]byte(password), []byte("qweb-verification-salt"), 4096, 32, sha256.New)
	return &AESEncryptor{key: key}
}

// Encrypt 加密字符串
func (e *AESEncryptor) Encrypt(plaintext string) (string, error) {
	if plaintext == "" {
		return "", nil
	}

	block, err := aes.NewCipher(e.key)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %w", err)
	}

	// 生成随机IV
	iv := make([]byte, aes.BlockSize)
	if _, err := io.ReadFull(cryptorand.Reader, iv); err != nil {
		return "", fmt.Errorf("failed to generate IV: %w", err)
	}

	// 使用CFB模式加密
	stream := cipher.NewCFBEncrypter(block, iv)
	ciphertext := make([]byte, len(plaintext))
	stream.XORKeyStream(ciphertext, []byte(plaintext))

	// 将IV和密文组合并转换为十六进制
	result := append(iv, ciphertext...)
	return hex.EncodeToString(result), nil
}

// Decrypt 解密字符串
func (e *AESEncryptor) Decrypt(ciphertext string) (string, error) {
	if ciphertext == "" {
		return "", nil
	}

	// 解码十六进制
	data, err := hex.DecodeString(ciphertext)
	if err != nil {
		return "", fmt.Errorf("failed to decode hex: %w", err)
	}

	if len(data) < aes.BlockSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	block, err := aes.NewCipher(e.key)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %w", err)
	}

	// 分离IV和密文
	iv := data[:aes.BlockSize]
	cipherData := data[aes.BlockSize:]

	// 使用CFB模式解密
	stream := cipher.NewCFBDecrypter(block, iv)
	plaintext := make([]byte, len(cipherData))
	stream.XORKeyStream(plaintext, cipherData)

	return string(plaintext), nil
}

// HashPassword 使用scrypt哈希密码
func (e *AESEncryptor) HashPassword(password, salt string) string {
	if password == "" {
		return ""
	}

	hash, err := scrypt.Key([]byte(password), []byte(salt), 32768, 8, 1, 64)
	if err != nil {
		// 如果scrypt失败，降级到PBKDF2
		hash = pbkdf2.Key([]byte(password), []byte(salt), 10000, 64, sha256.New)
	}

	return hex.EncodeToString(hash)
}

// GenerateSalt 生成随机盐值
func (e *AESEncryptor) GenerateSalt() string {
	salt := make([]byte, 16)
	if _, err := cryptorand.Read(salt); err != nil {
		// 如果随机数生成失败，使用时间戳作为备选
		r := rand.New(rand.NewSource(time.Now().UnixNano()))
		return fmt.Sprintf("%x", sha256.Sum256([]byte(fmt.Sprintf("%d", r.Int63()))))[:32]
	}
	return hex.EncodeToString(salt)
}

// VerifyPassword 验证密码
func (e *AESEncryptor) VerifyPassword(password, hash, salt string) bool {
	if password == "" || hash == "" || salt == "" {
		return false
	}

	expectedHash := e.HashPassword(password, salt)
	return expectedHash == hash
}

// 辅助函数

// GenerateSecureToken 生成安全令牌
func GenerateSecureToken(length int) (string, error) {
	if length <= 0 {
		length = 32
	}

	bytes := make([]byte, length)
	if _, err := cryptorand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate secure token: %w", err)
	}

	return hex.EncodeToString(bytes), nil
}

// HashString 计算字符串的SHA256哈希
func HashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	return hex.EncodeToString(hash[:])
}

// ValidatePasswordStrength 验证密码强度
func ValidatePasswordStrength(password string) error {
	if len(password) < 8 {
		return fmt.Errorf("password must be at least 8 characters long")
	}

	hasUpper := false
	hasLower := false
	hasDigit := false
	hasSpecial := false

	for _, char := range password {
		switch {
		case char >= 'A' && char <= 'Z':
			hasUpper = true
		case char >= 'a' && char <= 'z':
			hasLower = true
		case char >= '0' && char <= '9':
			hasDigit = true
		case char >= 33 && char <= 126 && !(char >= '0' && char <= '9') && !(char >= 'A' && char <= 'Z') && !(char >= 'a' && char <= 'z'):
			hasSpecial = true
		}
	}

	if !hasUpper {
		return fmt.Errorf("password must contain at least one uppercase letter")
	}
	if !hasLower {
		return fmt.Errorf("password must contain at least one lowercase letter")
	}
	if !hasDigit {
		return fmt.Errorf("password must contain at least one digit")
	}
	if !hasSpecial {
		return fmt.Errorf("password must contain at least one special character")
	}

	return nil
}
