/**
 * Qweb网络验证系统 - 主JavaScript文件
 * 包含WebSocket连接、API调用、UI交互等核心功能
 */

class QwebAdmin {
    constructor() {
        this.ws = null;
        this.wsReconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 5000;
        this.apiBase = '/api';
        this.token = localStorage.getItem('admin_token');
        this.currentPage = 'dashboard';
        this.subscriptions = new Set();
        
        this.init();
    }

    /**
     * 初始化应用
     */
    init() {
        this.setupEventListeners();
        this.initWebSocket();
        this.checkAuth();
        this.loadDashboard();
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 侧边栏导航
        document.addEventListener('click', (e) => {
            if (e.target.matches('.nav-item')) {
                e.preventDefault();
                const page = e.target.dataset.page;
                if (page) {
                    this.navigateTo(page);
                }
            }
        });

        // 表单提交
        document.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleFormSubmit(e);
        });

        // 模态框控制
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-modal]')) {
                const modalId = e.target.dataset.modal;
                this.showModal(modalId);
            }
            
            if (e.target.matches('.modal-overlay') || e.target.matches('[data-close-modal]')) {
                this.hideModal();
            }
        });

        // 侧边栏折叠
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-toggle-sidebar]')) {
                this.toggleSidebar();
            }
        });

        // 登出
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-logout]')) {
                this.logout();
            }
        });
    }

    /**
     * 初始化WebSocket连接
     */
    initWebSocket() {
        if (!this.token) return;

        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            this.ws = new WebSocket(wsUrl);
            
            this.ws.onopen = () => {
                console.log('WebSocket连接已建立');
                this.wsReconnectAttempts = 0;
                this.authenticateWebSocket();
                this.subscribeToTopics(['metrics', 'alerts', 'user_events', 'system_events']);
            };
            
            this.ws.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    this.handleWebSocketMessage(message);
                } catch (error) {
                    console.error('WebSocket消息解析错误:', error);
                }
            };
            
            this.ws.onclose = () => {
                console.log('WebSocket连接已关闭');
                this.scheduleReconnect();
            };
            
            this.ws.onerror = (error) => {
                console.error('WebSocket错误:', error);
            };
            
        } catch (error) {
            console.error('WebSocket连接失败:', error);
            this.scheduleReconnect();
        }
    }

    /**
     * WebSocket认证
     */
    authenticateWebSocket() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN && this.token) {
            this.sendWebSocketMessage({
                type: 'auth',
                data: { token: this.token },
                timestamp: Math.floor(Date.now() / 1000)
            });
        }
    }

    /**
     * 订阅WebSocket主题
     */
    subscribeToTopics(topics) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.sendWebSocketMessage({
                type: 'subscribe',
                data: { topics: topics },
                timestamp: Math.floor(Date.now() / 1000)
            });
            
            topics.forEach(topic => this.subscriptions.add(topic));
        }
    }

    /**
     * 发送WebSocket消息
     */
    sendWebSocketMessage(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        }
    }

    /**
     * 处理WebSocket消息
     */
    handleWebSocketMessage(message) {
        switch (message.type) {
            case 'connected':
                console.log('WebSocket连接确认:', message.data);
                break;
                
            case 'auth_response':
                if (message.data.success) {
                    console.log('WebSocket认证成功');
                } else {
                    console.error('WebSocket认证失败');
                }
                break;
                
            case 'metrics':
                this.updateMetrics(message.data);
                break;
                
            case 'alert':
                this.showAlert(message.data);
                break;
                
            case 'user_online':
            case 'user_offline':
                this.updateUserStatus(message.type, message.data);
                break;
                
            case 'system_event':
                this.handleSystemEvent(message.data);
                break;
                
            case 'error':
                console.error('WebSocket错误:', message.data);
                break;
                
            default:
                console.log('未知WebSocket消息:', message);
        }
    }

    /**
     * 计划重连
     */
    scheduleReconnect() {
        if (this.wsReconnectAttempts < this.maxReconnectAttempts) {
            this.wsReconnectAttempts++;
            console.log(`计划在${this.reconnectInterval / 1000}秒后重连WebSocket (尝试 ${this.wsReconnectAttempts}/${this.maxReconnectAttempts})`);
            
            setTimeout(() => {
                this.initWebSocket();
            }, this.reconnectInterval);
        } else {
            console.error('WebSocket重连次数已达上限');
        }
    }

    /**
     * 检查认证状态
     */
    async checkAuth() {
        if (!this.token) {
            this.redirectToLogin();
            return false;
        }

        try {
            const response = await this.apiCall('GET', '/auth/profile');
            if (response.success) {
                this.updateUserInfo(response.data);
                return true;
            } else {
                this.redirectToLogin();
                return false;
            }
        } catch (error) {
            console.error('认证检查失败:', error);
            this.redirectToLogin();
            return false;
        }
    }

    /**
     * API调用封装
     */
    async apiCall(method, endpoint, data = null) {
        const config = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };

        if (this.token) {
            config.headers.Authorization = `Bearer ${this.token}`;
        }

        if (data && (method === 'POST' || method === 'PUT')) {
            config.body = JSON.stringify(data);
        }

        const response = await fetch(`${this.apiBase}${endpoint}`, config);
        
        if (response.status === 401) {
            this.redirectToLogin();
            throw new Error('未授权访问');
        }

        const result = await response.json();
        return result;
    }

    /**
     * 页面导航
     */
    navigateTo(page) {
        // 移除当前活动状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // 设置新的活动状态
        const navItem = document.querySelector(`[data-page="${page}"]`);
        if (navItem) {
            navItem.classList.add('active');
        }

        this.currentPage = page;
        this.loadPageContent(page);
    }

    /**
     * 加载页面内容
     */
    async loadPageContent(page) {
        const contentArea = document.getElementById('main-content');
        if (!contentArea) return;

        // 显示加载状态
        contentArea.innerHTML = this.getLoadingHTML();

        try {
            let content = '';
            
            switch (page) {
                case 'dashboard':
                    content = await this.loadDashboard();
                    break;
                case 'users':
                    content = await this.loadUsersPage();
                    break;
                case 'agents':
                    content = await this.loadAgentsPage();
                    break;
                case 'cards':
                    content = await this.loadCardsPage();
                    break;
                case 'logs':
                    content = await this.loadLogsPage();
                    break;
                case 'settings':
                    content = await this.loadSettingsPage();
                    break;
                default:
                    content = '<div class="text-center"><h2>页面未找到</h2></div>';
            }

            contentArea.innerHTML = content;
            
            // 页面加载完成后的处理
            this.onPageLoaded(page);
            
        } catch (error) {
            console.error('页面加载失败:', error);
            contentArea.innerHTML = this.getErrorHTML('页面加载失败，请稍后重试');
        }
    }

    /**
     * 加载仪表板
     */
    async loadDashboard() {
        const [statsResponse, metricsResponse] = await Promise.all([
            this.apiCall('GET', '/v1/system/stats'),
            this.apiCall('GET', '/v1/monitor/metrics')
        ]);

        const stats = statsResponse.data || {};
        const metrics = metricsResponse.data || {};

        return `
            <div class="fade-in">
                <div class="flex items-center justify-between mb-6">
                    <h1 class="text-2xl font-bold">仪表板</h1>
                    <div class="flex gap-2">
                        <button class="btn btn-secondary btn-sm" onclick="qweb.refreshDashboard()">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            刷新
                        </button>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    ${this.getStatCard('总用户数', stats.total_users || 0, 'users', 'info')}
                    ${this.getStatCard('在线用户', stats.online_users || 0, 'user-check', 'success')}
                    ${this.getStatCard('代理商数', stats.total_agents || 0, 'users', 'warning')}
                    ${this.getStatCard('卡号数量', stats.total_cards || 0, 'credit-card', 'primary')}
                </div>

                <!-- 图表和实时数据 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="font-semibold">系统监控</h3>
                        </div>
                        <div class="card-body">
                            <div id="system-metrics">
                                ${this.getMetricsHTML(metrics)}
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="font-semibold">最近活动</h3>
                        </div>
                        <div class="card-body">
                            <div id="recent-activities">
                                ${this.getRecentActivitiesHTML()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 加载用户管理页面
     */
    async loadUsersPage() {
        const response = await this.apiCall('GET', '/v1/users?page=1&size=20');
        const users = response.data || [];
        const pagination = response.pagination || {};

        return `
            <div class="fade-in">
                <div class="flex items-center justify-between mb-6">
                    <h1 class="text-2xl font-bold">用户管理</h1>
                    <button class="btn btn-primary" data-modal="user-modal">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        添加用户
                    </button>
                </div>

                <!-- 搜索和筛选 -->
                <div class="card mb-6">
                    <div class="card-body">
                        <div class="flex flex-wrap gap-4">
                            <div class="flex-1 min-w-64">
                                <input type="text" class="form-input" placeholder="搜索用户..." id="user-search">
                            </div>
                            <select class="form-input w-48" id="user-status-filter">
                                <option value="">所有状态</option>
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                            <button class="btn btn-secondary" onclick="qweb.searchUsers()">搜索</button>
                        </div>
                    </div>
                </div>

                <!-- 用户表格 -->
                <div class="card">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>用户名</th>
                                    <th>代理商</th>
                                    <th>状态</th>
                                    <th>最后登录</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="users-table-body">
                                ${this.getUsersTableHTML(users)}
                            </tbody>
                        </table>
                    </div>
                    ${this.getPaginationHTML(pagination)}
                </div>
            </div>

            ${this.getUserModalHTML()}
        `;
    }

    /**
     * 获取统计卡片HTML
     */
    getStatCard(title, value, icon, color) {
        return `
            <div class="card">
                <div class="card-body">
                    <div class="flex items-center">
                        <div class="flex-1">
                            <p class="text-sm text-secondary mb-1">${title}</p>
                            <p class="text-2xl font-bold">${this.formatNumber(value)}</p>
                        </div>
                        <div class="w-12 h-12 bg-${color}-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-${color}-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                ${this.getIconPath(icon)}
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 获取指标HTML
     */
    getMetricsHTML(metrics) {
        if (!metrics) {
            return '<p class="text-secondary">暂无监控数据</p>';
        }

        return `
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm">CPU使用率</span>
                    <span class="font-semibold">${(metrics.cpu?.usage_percent || 0).toFixed(1)}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-primary-color h-2 rounded-full" style="width: ${metrics.cpu?.usage_percent || 0}%"></div>
                </div>

                <div class="flex justify-between items-center">
                    <span class="text-sm">内存使用率</span>
                    <span class="font-semibold">${(metrics.memory?.usage_percent || 0).toFixed(1)}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-success-color h-2 rounded-full" style="width: ${metrics.memory?.usage_percent || 0}%"></div>
                </div>

                <div class="flex justify-between items-center">
                    <span class="text-sm">活跃连接</span>
                    <span class="font-semibold">${metrics.network?.active_connections || 0}</span>
                </div>
            </div>
        `;
    }

    /**
     * 获取加载HTML
     */
    getLoadingHTML() {
        return `
            <div class="flex items-center justify-center h-64">
                <div class="loading">
                    <div class="w-8 h-8 border-4 border-primary-color border-t-transparent rounded-full animate-spin"></div>
                    <p class="mt-4 text-secondary">加载中...</p>
                </div>
            </div>
        `;
    }

    /**
     * 获取错误HTML
     */
    getErrorHTML(message) {
        return `
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-error-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-error-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold mb-2">出错了</h3>
                <p class="text-secondary">${message}</p>
                <button class="btn btn-primary mt-4" onclick="location.reload()">重新加载</button>
            </div>
        `;
    }

    /**
     * 工具方法
     */
    formatNumber(num) {
        return new Intl.NumberFormat('zh-CN').format(num);
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleString('zh-CN');
    }

    getIconPath(icon) {
        const icons = {
            'users': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>',
            'user-check': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>',
            'credit-card': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>'
        };
        return icons[icon] || '';
    }

    /**
     * 模态框控制
     */
    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('show');
        }
    }

    hideModal() {
        document.querySelectorAll('.modal-overlay.show').forEach(modal => {
            modal.classList.remove('show');
        });
    }

    /**
     * 侧边栏切换
     */
    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const mainContent = document.querySelector('.main-content');
        
        if (sidebar && mainContent) {
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
        }
    }

    /**
     * 登出
     */
    async logout() {
        try {
            await this.apiCall('POST', '/auth/logout');
        } catch (error) {
            console.error('登出失败:', error);
        } finally {
            localStorage.removeItem('admin_token');
            this.token = null;
            if (this.ws) {
                this.ws.close();
            }
            this.redirectToLogin();
        }
    }

    /**
     * 跳转到登录页
     */
    redirectToLogin() {
        window.location.href = '/login';
    }

    /**
     * 更新用户信息
     */
    updateUserInfo(userInfo) {
        const userNameElement = document.getElementById('user-name');
        if (userNameElement) {
            userNameElement.textContent = userInfo.username || '管理员';
        }
    }

    /**
     * 更新系统指标
     */
    updateMetrics(metrics) {
        const metricsContainer = document.getElementById('system-metrics');
        if (metricsContainer) {
            metricsContainer.innerHTML = this.getMetricsHTML(metrics);
        }
    }

    /**
     * 显示告警
     */
    showAlert(alert) {
        const alertsContainer = document.getElementById('alerts-container');
        if (!alertsContainer) return;

        const alertElement = document.createElement('div');
        alertElement.className = `alert alert-${alert.severity} fade-in`;
        alertElement.innerHTML = `
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="font-semibold">${alert.alert_name}</h4>
                    <p class="text-sm">${alert.message}</p>
                </div>
                <button class="btn btn-sm" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        alertsContainer.appendChild(alertElement);

        // 5秒后自动移除
        setTimeout(() => {
            if (alertElement.parentElement) {
                alertElement.remove();
            }
        }, 5000);
    }

    /**
     * 处理系统事件
     */
    handleSystemEvent(event) {
        console.log('系统事件:', event);
        
        // 根据事件类型更新界面
        if (event.event === 'user_created' || event.event === 'user_updated' || event.event === 'user_deleted') {
            if (this.currentPage === 'users') {
                this.refreshCurrentPage();
            }
        }
    }

    /**
     * 刷新当前页面
     */
    refreshCurrentPage() {
        this.loadPageContent(this.currentPage);
    }

    /**
     * 页面加载完成处理
     */
    onPageLoaded(page) {
        // 可以在这里添加页面特定的初始化逻辑
        console.log(`页面 ${page} 加载完成`);
    }
}

// 初始化应用
let qweb;
document.addEventListener('DOMContentLoaded', () => {
    qweb = new QwebAdmin();
});

// 导出全局对象供HTML模板使用
window.qweb = qweb;