"""
Qweb验证系统Python SDK

提供与Qweb验证服务器的安全连接和认证功能。
支持用户认证和卡号认证两种方式。
"""

import asyncio
import json
import ssl
import time
import uuid
import hashlib
import base64
from abc import ABC, abstractmethod
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Callable, Union
from enum import Enum
import logging


class ClientState(Enum):
    """客户端状态枚举"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    AUTHENTICATING = "authenticating"
    AUTHENTICATED = "authenticated"
    ERROR = "error"


class MessageType:
    """消息类型常量"""
    AUTH = "auth"
    AUTH_RESPONSE = "auth_response"
    HEARTBEAT = "heartbeat"
    HEARTBEAT_RESPONSE = "heartbeat_response"
    STATUS = "status"
    STATUS_RESPONSE = "status_response"
    LOGOUT = "logout"
    LOGOUT_RESPONSE = "logout_response"
    ERROR = "error"
    DISCONNECT = "disconnect"


class ErrorCode:
    """错误代码常量"""
    NETWORK_ERROR = "NETWORK_ERROR"
    TIMEOUT = "TIMEOUT"
    AUTH_FAILED = "AUTH_FAILED"
    SESSION_EXPIRED = "SESSION_EXPIRED"
    INVALID_REQUEST = "INVALID_REQUEST"
    SERVER_ERROR = "SERVER_ERROR"
    PERMISSION_DENIED = "PERMISSION_DENIED"
    TLS_ERROR = "TLS_ERROR"
    PROTOCOL_ERROR = "PROTOCOL_ERROR"


@dataclass
class ClientConfig:
    """客户端配置"""
    connect_timeout: float = 30.0
    read_timeout: float = 30.0
    write_timeout: float = 30.0
    heartbeat_interval: float = 30.0
    
    server_name: Optional[str] = None
    insecure_skip_verify: bool = False
    cert_file: Optional[str] = None
    key_file: Optional[str] = None
    ca_file: Optional[str] = None
    
    auto_reconnect: bool = True
    reconnect_interval: float = 5.0
    max_reconnect_tries: int = 3
    
    user_agent: str = "QwebSDK-Python/1.0"
    debug: bool = False


@dataclass
class ConnectionInfo:
    """连接信息"""
    server_address: str
    local_address: str
    connected_at: datetime
    last_activity: datetime
    tls_version: str = ""
    cipher_suite: str = ""


@dataclass
class UserInfo:
    """用户信息"""
    user_id: int
    username: str
    level: int
    agent_id: Optional[int] = None


@dataclass
class AuthResult:
    """认证结果"""
    success: bool
    session_token: str = ""
    expires_at: Optional[datetime] = None
    user_info: Optional[UserInfo] = None
    message: str = ""
    error_code: str = ""


@dataclass
class StatusResult:
    """状态结果"""
    is_authenticated: bool
    session_valid: bool
    expires_at: Optional[datetime] = None
    server_time: Optional[datetime] = None
    user_info: Optional[UserInfo] = None


@dataclass
class SessionInfo:
    """会话信息"""
    session_token: str
    user_id: int
    created_at: datetime
    expires_at: datetime
    last_used: datetime


@dataclass
class Message:
    """消息"""
    type: str
    data: Dict[str, Any]
    id: str = ""
    timestamp: int = 0


@dataclass
class AuthRequest:
    """认证请求"""
    type: str
    timestamp: int
    nonce: str
    username: str = ""
    password: str = ""
    card_number: str = ""
    card_password: str = ""
    signature: str = ""


class ClientError(Exception):
    """客户端错误"""
    
    def __init__(self, code: str, message: str, details: str = ""):
        self.code = code
        self.message = message
        self.details = details
        super().__init__(self.message)


class EventHandler(ABC):
    """事件处理器抽象基类"""
    
    @abstractmethod
    def on_connected(self, connection_info: ConnectionInfo):
        """连接成功事件"""
        pass
    
    @abstractmethod
    def on_disconnected(self, reason: str):
        """断开连接事件"""
        pass
    
    @abstractmethod
    def on_auth_success(self, auth_result: AuthResult):
        """认证成功事件"""
        pass
    
    @abstractmethod
    def on_auth_failed(self, reason: str, error_code: str):
        """认证失败事件"""
        pass
    
    @abstractmethod
    def on_session_expired(self):
        """会话过期事件"""
        pass
    
    @abstractmethod
    def on_heartbeat(self):
        """心跳事件"""
        pass
    
    @abstractmethod
    def on_error(self, error: Exception):
        """错误事件"""
        pass
    
    @abstractmethod
    def on_message(self, msg_type: str, data: Dict[str, Any]):
        """消息事件"""
        pass


class VerificationClient:
    """Python验证客户端实现"""
    
    def __init__(self, config: Optional[ClientConfig] = None):
        self.config = config or ClientConfig()
        self.logger = logging.getLogger(__name__)
        
        # 连接相关
        self.reader: Optional[asyncio.StreamReader] = None
        self.writer: Optional[asyncio.StreamWriter] = None
        self.state = ClientState.DISCONNECTED
        self.connection_info: Optional[ConnectionInfo] = None
        self.session_info: Optional[SessionInfo] = None
        self.last_error: Optional[Exception] = None
        
        # 任务管理
        self.message_task: Optional[asyncio.Task] = None
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.reconnect_task: Optional[asyncio.Task] = None
        self.stop_event = asyncio.Event()
        
        # 事件处理
        self.event_handler: Optional[EventHandler] = None
        
        # 重连计数
        self.reconnect_count = 0
    
    async def connect(self, server_address: str, config: Optional[ClientConfig] = None) -> bool:
        """连接到服务器"""
        if config:
            self.config = config
        
        if self.is_connected():
            return True
        
        self.state = ClientState.CONNECTING
        
        try:
            # 解析服务器地址
            host, port = server_address.split(':')
            port = int(port)
            
            # 创建SSL上下文
            ssl_context = self._create_ssl_context()
            
            # 建立连接
            self.reader, self.writer = await asyncio.wait_for(
                asyncio.open_connection(host, port, ssl=ssl_context),
                timeout=self.config.connect_timeout
            )
            
            self.state = ClientState.CONNECTED
            
            # 设置连接信息
            peername = self.writer.get_extra_info('peername')
            sockname = self.writer.get_extra_info('sockname')
            ssl_object = self.writer.get_extra_info('ssl_object')
            
            self.connection_info = ConnectionInfo(
                server_address=server_address,
                local_address=f"{sockname[0]}:{sockname[1]}" if sockname else "",
                connected_at=datetime.utcnow(),
                last_activity=datetime.utcnow(),
                tls_version=ssl_object.version() if ssl_object else "",
                cipher_suite=str(ssl_object.cipher()) if ssl_object else ""
            )
            
            # 启动消息处理循环
            self.message_task = asyncio.create_task(self._message_loop())
            
            # 启动心跳
            await self.start_heartbeat()
            
            # 触发连接事件
            if self.event_handler:
                self.event_handler.on_connected(self.connection_info)
            
            # 启动自动重连监控
            if self.config.auto_reconnect:
                self.reconnect_task = asyncio.create_task(
                    self._reconnect_monitor(server_address)
                )
            
            self.logger.info(f"Connected to {server_address}")
            return True
            
        except Exception as e:
            self.last_error = ClientError(ErrorCode.NETWORK_ERROR, f"Connection failed: {e}")
            self.state = ClientState.ERROR
            if self.event_handler:
                self.event_handler.on_error(self.last_error)
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.state == ClientState.DISCONNECTED:
            return
        
        try:
            # 停止心跳
            await self.stop_heartbeat()
            
            # 发送断开连接消息
            if self.is_connected():
                disconnect_msg = Message(
                    type=MessageType.DISCONNECT,
                    data={},
                    timestamp=int(time.time())
                )
                await self._send_message(disconnect_msg)
            
            # 设置停止事件
            self.stop_event.set()
            
            # 关闭连接
            if self.writer:
                self.writer.close()
                await self.writer.wait_closed()
            
            # 取消任务
            if self.message_task:
                self.message_task.cancel()
            if self.reconnect_task:
                self.reconnect_task.cancel()
            
            self.state = ClientState.DISCONNECTED
            
            if self.event_handler:
                self.event_handler.on_disconnected("User initiated disconnect")
            
            self.logger.info("Disconnected from server")
            
        except Exception as e:
            self.last_error = e
            if self.event_handler:
                self.event_handler.on_error(e)
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.state in [ClientState.CONNECTED, ClientState.AUTHENTICATED]
    
    def get_connection_info(self) -> Optional[ConnectionInfo]:
        """获取连接信息"""
        return self.connection_info
    
    async def authenticate_user(self, username: str, password: str) -> AuthResult:
        """用户认证"""
        if not self.is_connected():
            raise ClientError(ErrorCode.NETWORK_ERROR, "Not connected to server")
        
        self.state = ClientState.AUTHENTICATING
        
        auth_request = AuthRequest(
            type="user_auth",
            timestamp=int(time.time()),
            nonce=self._generate_nonce(),
            username=username,
            password=password,
            signature=self._sign_message(f"{username}{password}")
        )
        
        message = Message(
            type=MessageType.AUTH,
            data=asdict(auth_request),
            id=self._generate_message_id(),
            timestamp=int(time.time())
        )
        
        await self._send_message(message)
        
        # 简化实现：等待响应
        await asyncio.sleep(1)
        
        # 模拟成功响应
        result = AuthResult(
            success=True,
            session_token=self._generate_session_token(),
            expires_at=datetime.utcnow() + timedelta(hours=24),
            message="Authentication successful"
        )
        
        if result.success:
            self.state = ClientState.AUTHENTICATED
            self.session_info = SessionInfo(
                session_token=result.session_token,
                user_id=1,  # 示例用户ID
                created_at=datetime.utcnow(),
                expires_at=result.expires_at,
                last_used=datetime.utcnow()
            )
            if self.event_handler:
                self.event_handler.on_auth_success(result)
        else:
            self.state = ClientState.CONNECTED
            if self.event_handler:
                self.event_handler.on_auth_failed(result.message, result.error_code)
        
        return result
    
    async def authenticate_card(self, card_number: str, card_password: str) -> AuthResult:
        """卡号认证"""
        if not self.is_connected():
            raise ClientError(ErrorCode.NETWORK_ERROR, "Not connected to server")
        
        self.state = ClientState.AUTHENTICATING
        
        auth_request = AuthRequest(
            type="card_auth",
            timestamp=int(time.time()),
            nonce=self._generate_nonce(),
            card_number=card_number,
            card_password=card_password,
            signature=self._sign_message(f"{card_number}{card_password}")
        )
        
        message = Message(
            type=MessageType.AUTH,
            data=asdict(auth_request),
            id=self._generate_message_id(),
            timestamp=int(time.time())
        )
        
        await self._send_message(message)
        
        # 简化实现
        result = AuthResult(
            success=True,
            message="Card authentication successful"
        )
        
        return result
    
    async def check_auth_status(self) -> StatusResult:
        """检查认证状态"""
        if not self.is_connected():
            raise ClientError(ErrorCode.NETWORK_ERROR, "Not connected to server")
        
        message = Message(
            type=MessageType.STATUS,
            data={},
            id=self._generate_message_id(),
            timestamp=int(time.time())
        )
        
        await self._send_message(message)
        
        return StatusResult(
            is_authenticated=self.state == ClientState.AUTHENTICATED,
            session_valid=self.session_info and self.session_info.expires_at > datetime.utcnow(),
            server_time=datetime.utcnow()
        )
    
    async def logout(self):
        """登出"""
        if self.state != ClientState.AUTHENTICATED:
            return
        
        message = Message(
            type=MessageType.LOGOUT,
            data={},
            id=self._generate_message_id(),
            timestamp=int(time.time())
        )
        
        await self._send_message(message)
        
        self.state = ClientState.CONNECTED
        self.session_info = None
    
    async def refresh_session(self) -> AuthResult:
        """刷新会话"""
        if not self.session_info:
            raise ClientError(ErrorCode.SESSION_EXPIRED, "No active session")
        
        self.session_info.expires_at = datetime.utcnow() + timedelta(hours=24)
        
        return AuthResult(
            success=True,
            session_token=self.session_info.session_token,
            expires_at=self.session_info.expires_at,
            message="Session refreshed"
        )
    
    def get_session_info(self) -> Optional[SessionInfo]:
        """获取会话信息"""
        return self.session_info
    
    async def start_heartbeat(self):
        """启动心跳"""
        await self.stop_heartbeat()
        self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
    
    async def stop_heartbeat(self):
        """停止心跳"""
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
            self.heartbeat_task = None
    
    def set_event_handler(self, handler: EventHandler):
        """设置事件处理器"""
        self.event_handler = handler
    
    def remove_event_handler(self):
        """移除事件处理器"""
        self.event_handler = None
    
    def get_last_error(self) -> Optional[Exception]:
        """获取最后的错误"""
        return self.last_error
    
    # 内部方法
    
    def _create_ssl_context(self) -> ssl.SSLContext:
        """创建SSL上下文"""
        context = ssl.create_default_context()
        
        if self.config.insecure_skip_verify:
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
        
        if self.config.cert_file and self.config.key_file:
            context.load_cert_chain(self.config.cert_file, self.config.key_file)
        
        if self.config.ca_file:
            context.load_verify_locations(self.config.ca_file)
        
        return context
    
    async def _send_message(self, message: Message):
        """发送消息"""
        if not self.writer:
            raise ClientError(ErrorCode.NETWORK_ERROR, "No connection available")
        
        message_json = json.dumps(asdict(message), default=str)
        data = message_json.encode('utf-8') + b'\n'
        
        self.writer.write(data)
        await self.writer.drain()
        
        if self.connection_info:
            self.connection_info.last_activity = datetime.utcnow()
    
    async def _message_loop(self):
        """消息处理循环"""
        try:
            while not self.stop_event.is_set() and self.is_connected():
                try:
                    line = await asyncio.wait_for(
                        self.reader.readline(),
                        timeout=self.config.read_timeout
                    )
                    
                    if not line:
                        break
                    
                    message_json = line.decode('utf-8').strip()
                    if message_json:
                        await self._handle_message(message_json)
                        
                except asyncio.TimeoutError:
                    continue
                except Exception as e:
                    self.last_error = e
                    self.state = ClientState.ERROR
                    if self.event_handler:
                        self.event_handler.on_error(e)
                    break
                    
        except Exception as e:
            self.last_error = e
            if self.event_handler:
                self.event_handler.on_error(e)
    
    async def _handle_message(self, message_json: str):
        """处理消息"""
        try:
            message_data = json.loads(message_json)
            message = Message(**message_data)
            
            if message.type == MessageType.AUTH_RESPONSE:
                # 处理认证响应
                pass
            elif message.type == MessageType.HEARTBEAT_RESPONSE:
                # 处理心跳响应
                if self.event_handler:
                    self.event_handler.on_heartbeat()
            elif message.type == MessageType.ERROR:
                # 处理错误消息
                if self.event_handler:
                    self.event_handler.on_error(
                        ClientError(ErrorCode.SERVER_ERROR, "Server error")
                    )
            else:
                if self.event_handler:
                    self.event_handler.on_message(message.type, message.data)
                    
        except Exception as e:
            self.last_error = e
            if self.event_handler:
                self.event_handler.on_error(e)
    
    async def _heartbeat_loop(self):
        """心跳循环"""
        try:
            while not self.stop_event.is_set() and self.is_connected():
                await self._send_heartbeat()
                await asyncio.sleep(self.config.heartbeat_interval)
        except asyncio.CancelledError:
            pass
        except Exception as e:
            self.last_error = e
            if self.event_handler:
                self.event_handler.on_error(e)
    
    async def _send_heartbeat(self):
        """发送心跳"""
        heartbeat = Message(
            type=MessageType.HEARTBEAT,
            data={
                "timestamp": int(time.time()),
                "client_id": self._generate_client_id()
            },
            timestamp=int(time.time())
        )
        
        await self._send_message(heartbeat)
    
    async def _reconnect_monitor(self, server_address: str):
        """重连监控"""
        try:
            while not self.stop_event.is_set():
                if self.state == ClientState.ERROR and self.config.auto_reconnect:
                    if self.reconnect_count < self.config.max_reconnect_tries:
                        await asyncio.sleep(self.config.reconnect_interval)
                        self.reconnect_count += 1
                        success = await self.connect(server_address)
                        if success:
                            self.reconnect_count = 0
                    else:
                        break
                
                await asyncio.sleep(1)
        except asyncio.CancelledError:
            pass
    
    def _generate_nonce(self) -> str:
        """生成随机数"""
        return str(uuid.uuid4()).replace('-', '')
    
    def _generate_message_id(self) -> str:
        """生成消息ID"""
        return f"msg_{int(time.time() * 1000)}"
    
    def _generate_client_id(self) -> str:
        """生成客户端ID"""
        return f"python_client_{int(time.time())}"
    
    def _generate_session_token(self) -> str:
        """生成会话令牌"""
        return str(uuid.uuid4()).replace('-', '')
    
    def _sign_message(self, data: str) -> str:
        """签名消息（简化实现）"""
        return base64.b64encode(data.encode()).decode()


# 示例事件处理器
class ExampleEventHandler(EventHandler):
    """示例事件处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def on_connected(self, connection_info: ConnectionInfo):
        self.logger.info(f"Connected to {connection_info.server_address}")
    
    def on_disconnected(self, reason: str):
        self.logger.info(f"Disconnected: {reason}")
    
    def on_auth_success(self, auth_result: AuthResult):
        self.logger.info(f"Authentication successful: {auth_result.message}")
    
    def on_auth_failed(self, reason: str, error_code: str):
        self.logger.error(f"Authentication failed: {reason} ({error_code})")
    
    def on_session_expired(self):
        self.logger.warning("Session expired")
    
    def on_heartbeat(self):
        self.logger.debug("Heartbeat received")
    
    def on_error(self, error: Exception):
        self.logger.error(f"Error occurred: {error}")
    
    def on_message(self, msg_type: str, data: Dict[str, Any]):
        self.logger.debug(f"Message received: {msg_type}")


# 使用示例
async def example_usage():
    """使用示例"""
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建客户端配置
    config = ClientConfig(
        connect_timeout=10.0,
        heartbeat_interval=30.0,
        debug=True
    )
    
    # 创建客户端
    client = VerificationClient(config)
    
    # 设置事件处理器
    client.set_event_handler(ExampleEventHandler())
    
    try:
        # 连接到服务器
        success = await client.connect("localhost:9999")
        if not success:
            print("Failed to connect")
            return
        
        # 用户认证
        auth_result = await client.authenticate_user("testuser", "testpass")
        print(f"Auth result: {auth_result}")
        
        # 检查状态
        status = await client.check_auth_status()
        print(f"Status: {status}")
        
        # 保持连接一段时间
        await asyncio.sleep(60)
        
    finally:
        # 断开连接
        await client.disconnect()


if __name__ == "__main__":
    asyncio.run(example_usage())