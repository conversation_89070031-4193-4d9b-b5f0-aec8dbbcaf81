package web

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"qweb-verification/internal/logger"
	"qweb-verification/internal/models"
	"qweb-verification/internal/monitor"
)

// 卡号管理API

// ListCards 获取卡号列表
func (h *APIV1Handler) ListCards(c *gin.Context) {
	page, size, offset := GetPaginationParams(c)
	filters := GetFilterParams(c)

	cards, total, err := h.repo.ListCards(offset, size, filters)
	if err != nil {
		logger.Error("Failed to list cards", zap.Error(err))
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": "Failed to get cards",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    cards,
		"pagination": gin.H{
			"total": total,
			"page":  page,
			"size":  size,
			"pages": (total + int64(size) - 1) / int64(size),
		},
	})
}

// CreateCard 创建单个卡号
func (h *APIV1Handler) CreateCard(c *gin.Context) {
	var req struct {
		CardNumber   string  `json:"card_number" binding:"required"`
		CardPassword string  `json:"card_password" binding:"required"`
		DurationDays int     `json:"duration_days" binding:"required"`
		Price        float64 `json:"price" binding:"required"`
		AgentID      uint    `json:"agent_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid request format",
		})
		return
	}

	card := &models.Card{
		CardNumber:   req.CardNumber,
		CardPassword: req.CardPassword,
		DurationDays: req.DurationDays,
		Price:        req.Price,
		AgentID:      req.AgentID,
		Status:       models.CardStatusUnused,
	}

	if err := h.repo.CreateCard(card); err != nil {
		logger.Error("Failed to create card", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": "Failed to create card",
		})
		return
	}

	h.logOperation(c, models.OpTypeCardGenerate, fmt.Sprintf("Created card: %s", req.CardNumber), true, "")

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    card,
	})
}

// BatchCreateCards 批量创建卡号
func (h *APIV1Handler) BatchCreateCards(c *gin.Context) {
	var req struct {
		Count        int     `json:"count" binding:"required,min=1,max=1000"`
		DurationDays int     `json:"duration_days" binding:"required"`
		Price        float64 `json:"price" binding:"required"`
		AgentID      uint    `json:"agent_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid request format",
		})
		return
	}

	// 使用代理商服务生成卡号
	cards, err := h.deps.AgentService.GenerateCards(req.AgentID, req.Count, req.DurationDays, req.Price)
	if err != nil {
		logger.Error("Failed to generate cards", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": err.Error(),
		})
		return
	}

	h.logOperation(c, models.OpTypeCardGenerate, fmt.Sprintf("Generated %d cards for agent %d", req.Count, req.AgentID), true, "")

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data": gin.H{
			"count": len(cards),
			"cards": cards,
		},
	})
}

// GetCard 获取卡号详情
func (h *APIV1Handler) GetCard(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid card ID",
		})
		return
	}

	card, err := h.repo.GetCardByID(uint(id))
	if err != nil {
		logger.Error("Failed to get card", zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{
			"error":   true,
			"message": "Card not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    card,
	})
}

// UpdateCard 更新卡号
func (h *APIV1Handler) UpdateCard(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid card ID",
		})
		return
	}

	card, err := h.repo.GetCardByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   true,
			"message": "Card not found",
		})
		return
	}

	var req struct {
		CardPassword *string  `json:"card_password"`
		DurationDays *int     `json:"duration_days"`
		Price        *float64 `json:"price"`
		Status       *int     `json:"status"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid request format",
		})
		return
	}

	// 更新字段
	if req.CardPassword != nil {
		card.CardPassword = *req.CardPassword
	}
	if req.DurationDays != nil {
		card.DurationDays = *req.DurationDays
	}
	if req.Price != nil {
		card.Price = *req.Price
	}
	if req.Status != nil {
		card.Status = *req.Status
	}

	if err := h.repo.UpdateCard(card); err != nil {
		logger.Error("Failed to update card", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": "Failed to update card",
		})
		return
	}

	h.logOperation(c, "card_update", fmt.Sprintf("Updated card: %s", card.CardNumber), true, "")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    card,
	})
}

// DeleteCard 删除卡号
func (h *APIV1Handler) DeleteCard(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid card ID",
		})
		return
	}

	card, err := h.repo.GetCardByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   true,
			"message": "Card not found",
		})
		return
	}

	// 检查卡号是否可以删除
	if card.Status != models.CardStatusUnused {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Cannot delete used or expired card",
		})
		return
	}

	if err := h.repo.DeleteCard(uint(id)); err != nil {
		logger.Error("Failed to delete card", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": "Failed to delete card",
		})
		return
	}

	h.logOperation(c, "card_delete", fmt.Sprintf("Deleted card: %s", card.CardNumber), true, "")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Card deleted successfully",
	})
}

// 系统管理API

// GetSystemStats 获取系统统计
func (h *APIV1Handler) GetSystemStats(c *gin.Context) {
	// 获取基础统计
	userCount, _ := h.repo.GetUserCount()
	agentCount, _ := h.repo.GetAgentCount()
	cardCount, _ := h.repo.GetCardCount()
	onlineUserCount, _ := h.repo.GetOnlineUserCount()
	
	// 获取今日统计
	todayStats, _ := h.repo.GetTodayStats()

	// 获取TCP服务器统计
	var tcpStats map[string]interface{}
	if h.deps.TCPServer != nil {
		tcpStats = h.deps.TCPServer.GetStats()
	}

	// 获取数据库统计
	dbStats, _ := h.deps.Database.GetStats()

	stats := gin.H{
		"overview": gin.H{
			"total_users":   userCount,
			"total_agents":  agentCount,
			"total_cards":   cardCount,
			"online_users":  onlineUserCount,
		},
		"today": todayStats,
		"tcp_server": tcpStats,
		"database": dbStats,
		"timestamp": time.Now(),
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// GetLogs 获取操作日志
func (h *APIV1Handler) GetLogs(c *gin.Context) {
	page, size, offset := GetPaginationParams(c)
	filters := GetFilterParams(c)

	logs, total, err := h.repo.ListOperationLogs(offset, size, filters)
	if err != nil {
		logger.Error("Failed to get logs", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": "Failed to get logs",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    logs,
		"pagination": gin.H{
			"total": total,
			"page":  page,
			"size":  size,
			"pages": (total + int64(size) - 1) / int64(size),
		},
	})
}

// GetConfig 获取系统配置
func (h *APIV1Handler) GetConfig(c *gin.Context) {
	category := c.Query("category")
	
	configs, err := h.repo.ListConfigs(category)
	if err != nil {
		logger.Error("Failed to get config", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": "Failed to get config",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    configs,
	})
}

// UpdateConfig 更新系统配置
func (h *APIV1Handler) UpdateConfig(c *gin.Context) {
	var req struct {
		Configs []struct {
			Key   string `json:"key" binding:"required"`
			Value string `json:"value" binding:"required"`
		} `json:"configs" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid request format",
		})
		return
	}

	// 更新配置
	for _, configReq := range req.Configs {
		config := &models.SystemConfig{
			Key:   configReq.Key,
			Value: configReq.Value,
		}
		
		if err := h.repo.SetConfig(config); err != nil {
			logger.Error("Failed to update config", 
				zap.String("key", configReq.Key),
				zap.Error(err),
			)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   true,
				"message": fmt.Sprintf("Failed to update config: %s", configReq.Key),
			})
			return
		}
	}

	h.logOperation(c, models.OpTypeConfigUpdate, fmt.Sprintf("Updated %d configs", len(req.Configs)), true, "")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Config updated successfully",
	})
}

// BackupDatabase 备份数据库
func (h *APIV1Handler) BackupDatabase(c *gin.Context) {
	// 这里应该实现数据库备份逻辑
	// 为了简化，返回成功响应
	
	h.logOperation(c, "database_backup", "Database backup initiated", true, "")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Database backup initiated",
		"timestamp": time.Now(),
	})
}

// 监控相关API

// GetMetrics 获取系统指标
func (h *APIV1Handler) GetMetrics(c *gin.Context) {
	if h.deps.MetricsCollector == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":   true,
			"message": "Metrics collector not available",
		})
		return
	}

	metrics := h.deps.MetricsCollector.GetMetrics()
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    metrics,
	})
}

// GetAlerts 获取告警列表
func (h *APIV1Handler) GetAlerts(c *gin.Context) {
	if h.deps.MetricsCollector == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":   true,
			"message": "Metrics collector not available",
		})
		return
	}

	alerts := h.deps.MetricsCollector.CheckAlerts()
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    alerts,
	})
}

// CreateAlert 创建告警规则
func (h *APIV1Handler) CreateAlert(c *gin.Context) {
	var req monitor.Alert

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid request format",
		})
		return
	}

	if h.deps.MetricsCollector == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":   true,
			"message": "Metrics collector not available",
		})
		return
	}

	if err := h.deps.MetricsCollector.RegisterAlert(req); err != nil {
		logger.Error("Failed to create alert", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": "Failed to create alert",
		})
		return
	}

	h.logOperation(c, "alert_create", fmt.Sprintf("Created alert: %s", req.Name), true, "")

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    req,
	})
}

// UpdateAlert 更新告警规则
func (h *APIV1Handler) UpdateAlert(c *gin.Context) {
	alertID := c.Param("id")
	
	var req monitor.Alert
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   true,
			"message": "Invalid request format",
		})
		return
	}

	req.ID = alertID

	if h.deps.MetricsCollector == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":   true,
			"message": "Metrics collector not available",
		})
		return
	}

	if err := h.deps.MetricsCollector.RegisterAlert(req); err != nil {
		logger.Error("Failed to update alert", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   true,
			"message": "Failed to update alert",
		})
		return
	}

	h.logOperation(c, "alert_update", fmt.Sprintf("Updated alert: %s", req.Name), true, "")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    req,
	})
}

// DeleteAlert 删除告警规则
func (h *APIV1Handler) DeleteAlert(c *gin.Context) {
	alertID := c.Param("id")

	// 这里应该实现删除告警的逻辑
	// 为了简化，直接返回成功

	h.logOperation(c, "alert_delete", fmt.Sprintf("Deleted alert: %s", alertID), true, "")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Alert deleted successfully",
	})
}

// GetSecurityStats 获取安全统计信息
func (h *APIV1Handler) GetSecurityStats(c *gin.Context) {
	// 需要从服务器获取安全中间件的引用
	// 这里返回默认的统计信息
	stats := map[string]interface{}{
		"active_rate_limiters": 0,
		"login_attempt_records": 0,
		"locked_ips": 0,
		"allowed_ip_ranges": 0,
		"blocked_ip_ranges": 0,
		"security_config": map[string]interface{}{
			"ip_check_enabled": false,
			"rate_limit_enabled": false,
			"tls_enabled": false,
			"security_headers_enabled": false,
			"brute_force_check_enabled": false,
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}