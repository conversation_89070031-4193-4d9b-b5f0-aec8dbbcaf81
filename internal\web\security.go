package web

import (
	"crypto/tls"
	"fmt"
	"net"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"golang.org/x/time/rate"

	"qweb-verification/internal/logger"
)

// SecurityConfig 安全配置
type SecurityConfig struct {
	// IP限制
	AllowedIPs    []string `yaml:"allowed_ips"`
	BlockedIPs    []string `yaml:"blocked_ips"`
	EnableIPCheck bool     `yaml:"enable_ip_check"`
	
	// 频率限制
	RateLimit         int           `yaml:"rate_limit"`          // 每分钟请求数
	BurstLimit        int           `yaml:"burst_limit"`         // 突发请求数
	EnableRateLimit   bool          `yaml:"enable_rate_limit"`
	RateLimitWindow   time.Duration `yaml:"rate_limit_window"`   // 速率限制窗口
	
	// TLS配置
	TLSCertFile       string `yaml:"tls_cert_file"`
	TLSKeyFile        string `yaml:"tls_key_file"`
	EnableTLS         bool   `yaml:"enable_tls"`
	TLSMinVersion     string `yaml:"tls_min_version"`     // TLS1.2, TLS1.3
	
	// 安全头
	EnableSecurityHeaders bool     `yaml:"enable_security_headers"`
	CSPPolicy            string   `yaml:"csp_policy"`
	HSTSMaxAge           int      `yaml:"hsts_max_age"`
	
	// 防暴力破解
	LoginAttemptLimit     int           `yaml:"login_attempt_limit"`
	LoginLockoutDuration  time.Duration `yaml:"login_lockout_duration"`
	EnableBruteForceCheck bool          `yaml:"enable_brute_force_check"`
}

// SecurityMiddleware 安全中间件
type SecurityMiddleware struct {
	config      *SecurityConfig
	rateLimiters map[string]*rate.Limiter
	rateMutex   sync.RWMutex
	
	// 登录失败计数
	loginAttempts map[string]*LoginAttemptRecord
	loginMutex    sync.RWMutex
	
	// IP缓存
	allowedIPNets []*net.IPNet
	blockedIPNets []*net.IPNet
}

// LoginAttemptRecord 登录尝试记录
type LoginAttemptRecord struct {
	Count     int
	LastAttempt time.Time
	LockedUntil time.Time
}

// NewSecurityMiddleware 创建安全中间件
func NewSecurityMiddleware(config *SecurityConfig) (*SecurityMiddleware, error) {
	sm := &SecurityMiddleware{
		config:        config,
		rateLimiters:  make(map[string]*rate.Limiter),
		loginAttempts: make(map[string]*LoginAttemptRecord),
	}

	// 解析允许的IP
	if err := sm.parseAllowedIPs(); err != nil {
		return nil, err
	}

	// 解析阻止的IP
	if err := sm.parseBlockedIPs(); err != nil {
		return nil, err
	}

	// 启动清理goroutine
	go sm.cleanupExpiredRecords()

	return sm, nil
}

// parseAllowedIPs 解析允许的IP列表
func (sm *SecurityMiddleware) parseAllowedIPs() error {
	for _, ipStr := range sm.config.AllowedIPs {
		if ipStr == "" {
			continue
		}
		
		// 检查是否是CIDR格式
		if !strings.Contains(ipStr, "/") {
			ipStr += "/32" // 单个IP默认/32
		}
		
		_, ipNet, err := net.ParseCIDR(ipStr)
		if err != nil {
			return err
		}
		
		sm.allowedIPNets = append(sm.allowedIPNets, ipNet)
	}
	return nil
}

// parseBlockedIPs 解析阻止的IP列表
func (sm *SecurityMiddleware) parseBlockedIPs() error {
	for _, ipStr := range sm.config.BlockedIPs {
		if ipStr == "" {
			continue
		}
		
		if !strings.Contains(ipStr, "/") {
			ipStr += "/32"
		}
		
		_, ipNet, err := net.ParseCIDR(ipStr)
		if err != nil {
			return err
		}
		
		sm.blockedIPNets = append(sm.blockedIPNets, ipNet)
	}
	return nil
}

// IPFilterMiddleware IP过滤中间件
func (sm *SecurityMiddleware) IPFilterMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		if !sm.config.EnableIPCheck {
			c.Next()
			return
		}

		clientIP := c.ClientIP()
		ip := net.ParseIP(clientIP)
		if ip == nil {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Invalid IP address",
			})
			c.Abort()
			return
		}

		// 检查是否在阻止列表中
		for _, blockedNet := range sm.blockedIPNets {
			if blockedNet.Contains(ip) {
				logger.Warn("Blocked IP attempted access", 
					zap.String("ip", clientIP),
					zap.String("path", c.Request.URL.Path),
				)
				c.JSON(http.StatusForbidden, gin.H{
					"error": "Access denied",
				})
				c.Abort()
				return
			}
		}

		// 如果有允许列表且不为空，检查IP是否在允许列表中
		if len(sm.allowedIPNets) > 0 {
			allowed := false
			for _, allowedNet := range sm.allowedIPNets {
				if allowedNet.Contains(ip) {
					allowed = true
					break
				}
			}
			
			if !allowed {
				logger.Warn("Unauthorized IP attempted access", 
					zap.String("ip", clientIP),
					zap.String("path", c.Request.URL.Path),
				)
				c.JSON(http.StatusForbidden, gin.H{
					"error": "Access denied",
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// RateLimitMiddleware 频率限制中间件
func (sm *SecurityMiddleware) RateLimitMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		if !sm.config.EnableRateLimit {
			c.Next()
			return
		}

		clientIP := c.ClientIP()
		limiter := sm.getRateLimiter(clientIP)

		if !limiter.Allow() {
			logger.Warn("Rate limit exceeded", 
				zap.String("ip", clientIP),
				zap.String("path", c.Request.URL.Path),
			)
			
			c.Header("X-RateLimit-Limit", "60")
			c.Header("X-RateLimit-Remaining", "0")
			c.Header("Retry-After", "60")
			
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Rate limit exceeded. Please try again later.",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// getRateLimiter 获取或创建速率限制器
func (sm *SecurityMiddleware) getRateLimiter(clientIP string) *rate.Limiter {
	sm.rateMutex.RLock()
	limiter, exists := sm.rateLimiters[clientIP]
	sm.rateMutex.RUnlock()

	if !exists {
		sm.rateMutex.Lock()
		// 双重检查
		if limiter, exists = sm.rateLimiters[clientIP]; !exists {
			// 创建新的限制器: 每分钟允许的请求数，突发请求数
			r := rate.Every(time.Minute / time.Duration(sm.config.RateLimit))
			limiter = rate.NewLimiter(r, sm.config.BurstLimit)
			sm.rateLimiters[clientIP] = limiter
		}
		sm.rateMutex.Unlock()
	}

	return limiter
}

// SecurityHeadersMiddleware 安全头中间件
func (sm *SecurityMiddleware) SecurityHeadersMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		if sm.config.EnableSecurityHeaders {
			// HSTS
			if sm.config.HSTSMaxAge > 0 {
				c.Header("Strict-Transport-Security", fmt.Sprintf("max-age=%d; includeSubDomains", sm.config.HSTSMaxAge))
			}
			
			// CSP
			if sm.config.CSPPolicy != "" {
				c.Header("Content-Security-Policy", sm.config.CSPPolicy)
			} else {
				c.Header("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'")
			}
			
			// 其他安全头
			c.Header("X-Frame-Options", "DENY")
			c.Header("X-Content-Type-Options", "nosniff")
			c.Header("X-XSS-Protection", "1; mode=block")
			c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
			c.Header("Permissions-Policy", "camera=(), microphone=(), geolocation=()")
		}

		c.Next()
	}
}

// BruteForceProtectionMiddleware 防暴力破解中间件
func (sm *SecurityMiddleware) BruteForceProtectionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		if !sm.config.EnableBruteForceCheck {
			c.Next()
			return
		}

		// 只对登录接口进行检查
		if c.Request.URL.Path != "/api/auth/login" || c.Request.Method != "POST" {
			c.Next()
			return
		}

		clientIP := c.ClientIP()
		
		// 检查是否被锁定
		if sm.isLockedOut(clientIP) {
			logger.Warn("Login attempt from locked IP", zap.String("ip", clientIP))
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Too many failed login attempts. Please try again later.",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RecordLoginFailure 记录登录失败
func (sm *SecurityMiddleware) RecordLoginFailure(clientIP string) {
	sm.loginMutex.Lock()
	defer sm.loginMutex.Unlock()

	now := time.Now()
	record, exists := sm.loginAttempts[clientIP]
	
	if !exists {
		sm.loginAttempts[clientIP] = &LoginAttemptRecord{
			Count:       1,
			LastAttempt: now,
		}
	} else {
		record.Count++
		record.LastAttempt = now
		
		// 如果达到限制，设置锁定时间
		if record.Count >= sm.config.LoginAttemptLimit {
			record.LockedUntil = now.Add(sm.config.LoginLockoutDuration)
			logger.Warn("IP locked due to too many login failures", 
				zap.String("ip", clientIP),
				zap.Int("attempts", record.Count),
				zap.Time("locked_until", record.LockedUntil),
			)
		}
	}
}

// ClearLoginAttempts 清除登录尝试记录
func (sm *SecurityMiddleware) ClearLoginAttempts(clientIP string) {
	sm.loginMutex.Lock()
	defer sm.loginMutex.Unlock()
	
	delete(sm.loginAttempts, clientIP)
}

// isLockedOut 检查IP是否被锁定
func (sm *SecurityMiddleware) isLockedOut(clientIP string) bool {
	sm.loginMutex.RLock()
	defer sm.loginMutex.RUnlock()

	record, exists := sm.loginAttempts[clientIP]
	if !exists {
		return false
	}

	return time.Now().Before(record.LockedUntil)
}

// cleanupExpiredRecords 清理过期记录
func (sm *SecurityMiddleware) cleanupExpiredRecords() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			sm.cleanupRateLimiters()
			sm.cleanupLoginAttempts()
		}
	}
}

// cleanupRateLimiters 清理不活跃的速率限制器
func (sm *SecurityMiddleware) cleanupRateLimiters() {
	sm.rateMutex.Lock()
	defer sm.rateMutex.Unlock()

	// 清理超过1小时没有活动的限制器
	cutoff := time.Now().Add(-1 * time.Hour)
	for ip, limiter := range sm.rateLimiters {
		// 这里简单处理，实际可能需要更复杂的逻辑
		if limiter.TokensAt(cutoff) == float64(sm.config.BurstLimit) {
			delete(sm.rateLimiters, ip)
		}
	}
}

// cleanupLoginAttempts 清理过期的登录尝试记录
func (sm *SecurityMiddleware) cleanupLoginAttempts() {
	sm.loginMutex.Lock()
	defer sm.loginMutex.Unlock()

	now := time.Now()
	for ip, record := range sm.loginAttempts {
		// 清理锁定已过期且超过1小时的记录
		if now.After(record.LockedUntil) && now.Sub(record.LastAttempt) > time.Hour {
			delete(sm.loginAttempts, ip)
		}
	}
}

// GetTLSConfig 获取TLS配置
func (sm *SecurityMiddleware) GetTLSConfig() *tls.Config {
	if !sm.config.EnableTLS {
		return &tls.Config{
			MinVersion: tls.VersionTLS12,
		}
	}

	tlsConfig := &tls.Config{
		MinVersion: tls.VersionTLS12,
		CipherSuites: []uint16{
			tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
			tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,
			tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
		},
		PreferServerCipherSuites: true,
		CurvePreferences: []tls.CurveID{
			tls.CurveP256,
			tls.X25519,
		},
	}

	// 设置最小TLS版本
	switch strings.ToUpper(sm.config.TLSMinVersion) {
	case "TLS1.3":
		tlsConfig.MinVersion = tls.VersionTLS13
	case "TLS1.2":
		tlsConfig.MinVersion = tls.VersionTLS12
	default:
		tlsConfig.MinVersion = tls.VersionTLS12
	}

	return tlsConfig
}

// GetSecurityStats 获取安全统计信息
func (sm *SecurityMiddleware) GetSecurityStats() map[string]interface{} {
	sm.rateMutex.RLock()
	sm.loginMutex.RLock()
	defer sm.rateMutex.RUnlock()
	defer sm.loginMutex.RUnlock()

	var lockedIPs int
	for _, record := range sm.loginAttempts {
		if time.Now().Before(record.LockedUntil) {
			lockedIPs++
		}
	}

	return map[string]interface{}{
		"active_rate_limiters": len(sm.rateLimiters),
		"login_attempt_records": len(sm.loginAttempts),
		"locked_ips": lockedIPs,
		"allowed_ip_ranges": len(sm.allowedIPNets),
		"blocked_ip_ranges": len(sm.blockedIPNets),
		"security_config": map[string]interface{}{
			"ip_check_enabled": sm.config.EnableIPCheck,
			"rate_limit_enabled": sm.config.EnableRateLimit,
			"tls_enabled": sm.config.EnableTLS,
			"security_headers_enabled": sm.config.EnableSecurityHeaders,
			"brute_force_check_enabled": sm.config.EnableBruteForceCheck,
		},
	}
}