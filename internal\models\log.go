package models

import (
	"time"
)

// OperationLog 操作日志表模型
type OperationLog struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    *uint     `json:"user_id" gorm:"index"`
	User      *User     `json:"user,omitempty" gorm:"foreignKey:UserID"`
	AgentID   *uint     `json:"agent_id" gorm:"index"`
	Agent     *Agent    `json:"agent,omitempty" gorm:"foreignKey:AgentID"`
	Operation string    `json:"operation" gorm:"not null;size:50"`
	Details   string    `json:"details" gorm:"type:text"`
	IPAddress string    `json:"ip_address" gorm:"not null;size:45"`
	UserAgent string    `json:"user_agent" gorm:"size:255"`
	Success   bool      `json:"success" gorm:"default:true"`
	ErrorMsg  string    `json:"error_msg" gorm:"size:255"`
	CreatedAt time.Time `json:"created_at" gorm:"index"`
}

// TableName 指定表名
func (OperationLog) TableName() string {
	return "operation_logs"
}

// IPBlacklist IP黑名单表模型
type IPBlacklist struct {
	ID             uint      `json:"id" gorm:"primaryKey"`
	IPAddress      string    `json:"ip_address" gorm:"uniqueIndex;not null;size:45"`
	FailedAttempts int       `json:"failed_attempts" gorm:"default:1"`
	BannedUntil    *time.Time `json:"banned_until"`
	Reason         string    `json:"reason" gorm:"size:255"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// TableName 指定表名
func (IPBlacklist) TableName() string {
	return "ip_blacklist"
}

// IsBanned 检查IP是否被封禁
func (ip *IPBlacklist) IsBanned() bool {
	if ip.BannedUntil == nil {
		return false
	}
	return ip.BannedUntil.After(time.Now())
}

// SystemConfig 系统配置表模型
type SystemConfig struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Key       string    `json:"key" gorm:"uniqueIndex;not null;size:100"`
	Value     string    `json:"value" gorm:"type:text"`
	Type      string    `json:"type" gorm:"size:20;default:'string'"` // string, int, bool, json
	Category  string    `json:"category" gorm:"size:50"`
	Comment   string    `json:"comment" gorm:"size:255"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// TableName 指定表名
func (SystemConfig) TableName() string {
	return "system_configs"
}

// OnlineUser 在线用户统计表模型
type OnlineUser struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	UserID     uint      `json:"user_id" gorm:"not null;index"`
	User       User      `json:"user,omitempty" gorm:"foreignKey:UserID"`
	SessionID  uint      `json:"session_id" gorm:"not null;index"`
	Session    Session   `json:"session,omitempty" gorm:"foreignKey:SessionID"`
	IPAddress  string    `json:"ip_address" gorm:"not null;size:45"`
	LoginTime  time.Time `json:"login_time" gorm:"not null"`
	LastActive time.Time `json:"last_active" gorm:"not null;index"`
}

// TableName 指定表名
func (OnlineUser) TableName() string {
	return "online_users"
}