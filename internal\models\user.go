package models

import (
	"time"
	"gorm.io/gorm"
)

// User 用户表模型
type User struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Username    string         `json:"username" gorm:"uniqueIndex;not null;size:50"`
	PasswordHash string        `json:"-" gorm:"not null;size:128"`
	Salt        string         `json:"-" gorm:"not null;size:32"`
	Status      int            `json:"status" gorm:"default:1"` // 1:启用 0:禁用
	AgentID     *uint          `json:"agent_id" gorm:"index"`
	Agent       *Agent         `json:"agent,omitempty" gorm:"foreignKey:AgentID"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	LastLogin   *time.Time     `json:"last_login"`
	
	// 关联关系
	Cards       []Card         `json:"cards,omitempty" gorm:"foreignKey:UserID"`
	Sessions    []Session      `json:"sessions,omitempty" gorm:"foreignKey:UserID"`
	OperationLogs []OperationLog `json:"operation_logs,omitempty" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// IsActive 检查用户是否激活
func (u *User) IsActive() bool {
	return u.Status == 1
}

// BeforeCreate GORM钩子：创建前
func (u *User) BeforeCreate(tx *gorm.DB) error {
	u.CreatedAt = time.Now()
	u.UpdatedAt = time.Now()
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (u *User) BeforeUpdate(tx *gorm.DB) error {
	u.UpdatedAt = time.Now()
	return nil
}