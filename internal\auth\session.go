package auth

import (
	"fmt"
	"time"

	"qweb-verification/internal/database"
	"qweb-verification/internal/models"
	"qweb-verification/internal/security"
)

// SessionManager 会话管理器接口
type SessionManager interface {
	CreateSession(userID uint, ipAddress, userAgent string) (*models.Session, error)
	GetSession(token string) (*models.Session, error)
	ValidateSession(token string) (*models.Session, error)
	RefreshSession(token string) (*models.Session, error)
	DeleteSession(token string) error
	DeleteUserSessions(userID uint) error
	CleanExpiredSessions() error
	GetUserActiveSessions(userID uint) ([]*models.Session, error)
}

// DatabaseSessionManager 数据库会话管理器实现
type DatabaseSessionManager struct {
	repo           database.Repository
	encryptor      security.Encryptor
	sessionTimeout time.Duration
}

// NewDatabaseSessionManager 创建数据库会话管理器
func NewDatabaseSessionManager(repo database.Repository, encryptor security.Encryptor, sessionTimeout time.Duration) *DatabaseSessionManager {
	return &DatabaseSessionManager{
		repo:           repo,
		encryptor:      encryptor,
		sessionTimeout: sessionTimeout,
	}
}

// CreateSession 创建会话
func (sm *DatabaseSessionManager) CreateSession(userID uint, ipAddress, userAgent string) (*models.Session, error) {
	// 生成会话令牌
	token, err := security.GenerateSecureToken(32)
	if err != nil {
		return nil, fmt.Errorf("failed to generate session token: %w", err)
	}

	session := &models.Session{
		UserID:       userID,
		SessionToken: token,
		IPAddress:    ipAddress,
		UserAgent:    userAgent,
		ExpiresAt:    time.Now().Add(sm.sessionTimeout),
	}

	if err := sm.repo.CreateSession(session); err != nil {
		return nil, fmt.Errorf("failed to create session: %w", err)
	}

	return session, nil
}

// GetSession 获取会话
func (sm *DatabaseSessionManager) GetSession(token string) (*models.Session, error) {
	session, err := sm.repo.GetSessionByToken(token)
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	return session, nil
}

// ValidateSession 验证会话
func (sm *DatabaseSessionManager) ValidateSession(token string) (*models.Session, error) {
	session, err := sm.GetSession(token)
	if err != nil {
		return nil, err
	}

	if !session.IsValid() {
		// 删除无效会话
		sm.DeleteSession(token)
		return nil, models.ErrSessionExpired
	}

	return session, nil
}

// RefreshSession 刷新会话
func (sm *DatabaseSessionManager) RefreshSession(token string) (*models.Session, error) {
	session, err := sm.ValidateSession(token)
	if err != nil {
		return nil, err
	}

	// 延长会话过期时间
	session.ExpiresAt = time.Now().Add(sm.sessionTimeout)

	if err := sm.repo.UpdateSession(session); err != nil {
		return nil, fmt.Errorf("failed to refresh session: %w", err)
	}

	return session, nil
}

// DeleteSession 删除会话
func (sm *DatabaseSessionManager) DeleteSession(token string) error {
	session, err := sm.GetSession(token)
	if err != nil {
		return err
	}

	return sm.repo.DeleteSession(session.ID)
}

// DeleteUserSessions 删除用户的所有会话
func (sm *DatabaseSessionManager) DeleteUserSessions(userID uint) error {
	sessions, err := sm.repo.GetUserActiveSessions(userID)
	if err != nil {
		return fmt.Errorf("failed to get user sessions: %w", err)
	}

	for _, session := range sessions {
		if err := sm.repo.DeleteSession(session.ID); err != nil {
			return fmt.Errorf("failed to delete session %d: %w", session.ID, err)
		}
	}

	return nil
}

// CleanExpiredSessions 清理过期会话
func (sm *DatabaseSessionManager) CleanExpiredSessions() error {
	return sm.repo.DeleteExpiredSessions()
}

// GetUserActiveSessions 获取用户活跃会话
func (sm *DatabaseSessionManager) GetUserActiveSessions(userID uint) ([]*models.Session, error) {
	return sm.repo.GetUserActiveSessions(userID)
}

// AuthenticationService 认证服务
type AuthenticationService struct {
	repo           database.Repository
	sessionManager SessionManager
	authService    *AuthService
	encryptor      security.Encryptor
}

// NewAuthenticationService 创建认证服务
func NewAuthenticationService(repo database.Repository, encryptor security.Encryptor, secretKey string, sessionTimeout time.Duration) *AuthenticationService {
	sessionManager := NewDatabaseSessionManager(repo, encryptor, sessionTimeout)
	authService := NewAuthService(encryptor, secretKey)

	return &AuthenticationService{
		repo:           repo,
		sessionManager: sessionManager,
		authService:    authService,
		encryptor:      encryptor,
	}
}

// AuthenticateUser 用户认证
func (as *AuthenticationService) AuthenticateUser(req *AuthRequest, ipAddress, userAgent string) (*AuthResponse, error) {
	// 验证认证请求
	if err := as.authService.ValidateAuthRequest(req); err != nil {
		return CreateErrorResponse(err.Error(), "INVALID_REQUEST"), nil
	}

	// 解密敏感信息
	if err := as.authService.DecryptAuthRequest(req); err != nil {
		return CreateErrorResponse("Failed to decrypt request", "DECRYPTION_ERROR"), nil
	}

	// 验证用户凭据
	user, err := as.repo.GetUserByUsername(req.Username)
	if err != nil {
		return CreateErrorResponse("Invalid credentials", "INVALID_CREDENTIALS"), nil
	}

	if !user.IsActive() {
		return CreateErrorResponse("User account is disabled", "USER_DISABLED"), nil
	}

	// 验证密码
	if !as.encryptor.VerifyPassword(req.Password, user.PasswordHash, user.Salt) {
		return CreateErrorResponse("Invalid credentials", "INVALID_CREDENTIALS"), nil
	}

	// 创建会话
	session, err := as.sessionManager.CreateSession(user.ID, ipAddress, userAgent)
	if err != nil {
		return CreateErrorResponse("Failed to create session", "SESSION_ERROR"), nil
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLogin = &now
	as.repo.UpdateUser(user)

	// 记录操作日志
	as.logOperation(user.ID, nil, models.OpTypeLogin, "User login successful", ipAddress, userAgent, true, "")

	// 创建用户信息
	userInfo := &AuthUserInfo{
		UserID:   user.ID,
		Username: user.Username,
		Level:    1, // 普通用户级别
		AgentID:  user.AgentID,
	}

	return CreateSuccessResponse(session.SessionToken, session.ExpiresAt, userInfo), nil
}

// AuthenticateCard 卡号认证
func (as *AuthenticationService) AuthenticateCard(req *AuthRequest, ipAddress, userAgent string) (*AuthResponse, error) {
	// 验证认证请求
	if err := as.authService.ValidateAuthRequest(req); err != nil {
		return CreateErrorResponse(err.Error(), "INVALID_REQUEST"), nil
	}

	// 解密敏感信息
	if err := as.authService.DecryptAuthRequest(req); err != nil {
		return CreateErrorResponse("Failed to decrypt request", "DECRYPTION_ERROR"), nil
	}

	// 查找卡号
	card, err := as.repo.GetCardByNumber(req.CardNumber)
	if err != nil {
		return CreateErrorResponse("Invalid card credentials", "INVALID_CARD"), nil
	}

	// 验证卡密
	if card.CardPassword != req.CardPassword {
		return CreateErrorResponse("Invalid card credentials", "INVALID_CARD"), nil
	}

	// 检查卡号状态
	if card.IsExpired() {
		return CreateErrorResponse("Card has expired", "CARD_EXPIRED"), nil
	}

	var user *models.User

	if card.IsUnused() {
		// 卡号未使用，需要绑定用户
		// 这里可以自动创建用户或要求提供用户信息
		// 简化实现：自动创建用户
		username := fmt.Sprintf("card_%s", card.CardNumber)
		salt := as.encryptor.GenerateSalt()
		password, _ := security.GenerateSecureToken(12)
		passwordHash := as.encryptor.HashPassword(password, salt)

		user = &models.User{
			Username:     username,
			PasswordHash: passwordHash,
			Salt:         salt,
			Status:       models.UserStatusEnabled,
			AgentID:      &card.AgentID,
		}

		if err := as.repo.CreateUser(user); err != nil {
			return CreateErrorResponse("Failed to create user", "USER_CREATION_ERROR"), nil
		}

		// 使用卡号
		if err := card.Use(user.ID); err != nil {
			return CreateErrorResponse("Failed to use card", "CARD_USE_ERROR"), nil
		}

		if err := as.repo.UpdateCard(card); err != nil {
			return CreateErrorResponse("Failed to update card", "CARD_UPDATE_ERROR"), nil
		}

		// 记录卡号使用日志
		as.logOperation(user.ID, &card.AgentID, models.OpTypeCardUse, fmt.Sprintf("Card %s used", card.CardNumber), ipAddress, userAgent, true, "")

	} else if card.IsUsed() {
		// 卡号已使用，获取绑定的用户
		if card.UserID == nil {
			return CreateErrorResponse("Card is not properly bound to user", "CARD_BINDING_ERROR"), nil
		}

		user, err = as.repo.GetUserByID(*card.UserID)
		if err != nil {
			return CreateErrorResponse("User not found", "USER_NOT_FOUND"), nil
		}

		if !user.IsActive() {
			return CreateErrorResponse("User account is disabled", "USER_DISABLED"), nil
		}
	} else {
		return CreateErrorResponse("Card is not available", "CARD_NOT_AVAILABLE"), nil
	}

	// 创建会话
	session, err := as.sessionManager.CreateSession(user.ID, ipAddress, userAgent)
	if err != nil {
		return CreateErrorResponse("Failed to create session", "SESSION_ERROR"), nil
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLogin = &now
	as.repo.UpdateUser(user)

	// 记录操作日志
	as.logOperation(user.ID, user.AgentID, models.OpTypeLogin, "Card login successful", ipAddress, userAgent, true, "")

	// 创建用户信息
	userInfo := &AuthUserInfo{
		UserID:   user.ID,
		Username: user.Username,
		Level:    1, // 普通用户级别
		AgentID:  user.AgentID,
	}

	return CreateSuccessResponse(session.SessionToken, session.ExpiresAt, userInfo), nil
}

// ValidateSession 验证会话
func (as *AuthenticationService) ValidateSession(token string) (*models.Session, error) {
	return as.sessionManager.ValidateSession(token)
}

// RefreshSession 刷新会话
func (as *AuthenticationService) RefreshSession(token string) (*models.Session, error) {
	return as.sessionManager.RefreshSession(token)
}

// Logout 登出
func (as *AuthenticationService) Logout(token string, ipAddress, userAgent string) error {
	// 获取会话信息
	session, err := as.sessionManager.GetSession(token)
	if err != nil {
		return err
	}

	// 记录登出日志
	as.logOperation(session.UserID, nil, models.OpTypeLogout, "User logout", ipAddress, userAgent, true, "")

	// 删除会话
	return as.sessionManager.DeleteSession(token)
}

// LogoutAll 登出所有设备
func (as *AuthenticationService) LogoutAll(userID uint, ipAddress, userAgent string) error {
	// 记录登出日志
	as.logOperation(userID, nil, models.OpTypeLogout, "User logout from all devices", ipAddress, userAgent, true, "")

	// 删除用户所有会话
	return as.sessionManager.DeleteUserSessions(userID)
}

// logOperation 记录操作日志
func (as *AuthenticationService) logOperation(userID uint, agentID *uint, operation, details, ipAddress, userAgent string, success bool, errorMsg string) {
	log := &models.OperationLog{
		UserID:    &userID,
		AgentID:   agentID,
		Operation: operation,
		Details:   details,
		IPAddress: ipAddress,
		UserAgent: userAgent,
		Success:   success,
		ErrorMsg:  errorMsg,
	}

	// 忽略日志记录错误，避免影响主要业务流程
	as.repo.CreateOperationLog(log)
}

// GetSessionManager 获取会话管理器
func (as *AuthenticationService) GetSessionManager() SessionManager {
	return as.sessionManager
}
