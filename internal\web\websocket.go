package web

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"go.uber.org/zap"

	"qweb-verification/internal/logger"
	"qweb-verification/internal/monitor"
)

// WebSocketManager WebSocket连接管理器
type WebSocketManager struct {
	clients    map[string]*WebSocketClient
	broadcast  chan []byte
	register   chan *WebSocketClient
	unregister chan *WebSocketClient
	mutex      sync.RWMutex
}

// WebSocketClient WebSocket客户端
type WebSocketClient struct {
	ID         string
	Conn       *websocket.Conn
	Manager    *WebSocketManager
	Send       chan []byte
	IPAddress  string
	UserAgent  string
	ConnectedAt time.Time
	LastActive  time.Time
	IsAdmin     bool
	AdminID     uint
	Subscriptions map[string]bool // 订阅主题列表
}

// WSMessage WebSocket消息
type WSMessage struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	Timestamp int64       `json:"timestamp"`
	ID        string      `json:"id,omitempty"`
}

// WSMessageType WebSocket消息类型
const (
	WSMsgTypeHeartbeat     = "heartbeat"
	WSMsgTypeAuth          = "auth"
	WSMsgTypeStats         = "stats"
	WSMsgTypeMetrics       = "metrics"
	WSMsgTypeAlert         = "alert"
	WSMsgTypeUserOnline    = "user_online"
	WSMsgTypeUserOffline   = "user_offline"
	WSMsgTypeSystemEvent   = "system_event"
	WSMsgTypeError         = "error"
	WSMsgTypeSubscribe     = "subscribe"
	WSMsgTypeUnsubscribe   = "unsubscribe"
)

// NewWebSocketManager 创建WebSocket管理器
func NewWebSocketManager() *WebSocketManager {
	manager := &WebSocketManager{
		clients:    make(map[string]*WebSocketClient),
		broadcast:  make(chan []byte),
		register:   make(chan *WebSocketClient),
		unregister: make(chan *WebSocketClient),
	}

	go manager.run()
	return manager
}

// NewWebSocketClient 创建WebSocket客户端
func NewWebSocketClient(conn *websocket.Conn, ipAddress string) *WebSocketClient {
	return &WebSocketClient{
		ID:            generateClientID(),
		Conn:          conn,
		Send:          make(chan []byte, 256),
		IPAddress:     ipAddress,
		ConnectedAt:   time.Now(),
		LastActive:    time.Now(),
		Subscriptions: make(map[string]bool),
	}
}

// AddClient 添加客户端
func (wm *WebSocketManager) AddClient(client *WebSocketClient) {
	wm.register <- client
}

// RemoveClient 移除客户端
func (wm *WebSocketManager) RemoveClient(client *WebSocketClient) {
	wm.unregister <- client
}

// BroadcastMessage 广播消息
func (wm *WebSocketManager) BroadcastMessage(message *WSMessage) {
	data, err := json.Marshal(message)
	if err != nil {
		logger.Error("Failed to marshal WebSocket message", zap.Error(err))
		return
	}
	
	wm.broadcast <- data
}

// SendToClient 发送消息给特定客户端
func (wm *WebSocketManager) SendToClient(clientID string, message *WSMessage) {
	wm.mutex.RLock()
	client, exists := wm.clients[clientID]
	wm.mutex.RUnlock()

	if !exists {
		return
	}

	data, err := json.Marshal(message)
	if err != nil {
		logger.Error("Failed to marshal WebSocket message", zap.Error(err))
		return
	}

	select {
	case client.Send <- data:
	default:
		close(client.Send)
		wm.RemoveClient(client)
	}
}

// SendToAdmins 发送消息给所有管理员
func (wm *WebSocketManager) SendToAdmins(message *WSMessage) {
	wm.mutex.RLock()
	defer wm.mutex.RUnlock()

	data, err := json.Marshal(message)
	if err != nil {
		logger.Error("Failed to marshal WebSocket message", zap.Error(err))
		return
	}

	for _, client := range wm.clients {
		if client.IsAdmin {
			select {
			case client.Send <- data:
			default:
				close(client.Send)
				wm.unregister <- client
			}
		}
	}
}

// SendToSubscribers 发送消息给订阅了特定主题的客户端
func (wm *WebSocketManager) SendToSubscribers(topic string, message *WSMessage) {
	wm.mutex.RLock()
	defer wm.mutex.RUnlock()

	data, err := json.Marshal(message)
	if err != nil {
		logger.Error("Failed to marshal WebSocket message", zap.Error(err))
		return
	}

	for _, client := range wm.clients {
		if client.IsAdmin && client.Subscriptions[topic] {
			select {
			case client.Send <- data:
			default:
				close(client.Send)
				wm.unregister <- client
			}
		}
	}
}

// GetClientCount 获取客户端数量
func (wm *WebSocketManager) GetClientCount() int {
	wm.mutex.RLock()
	defer wm.mutex.RUnlock()
	return len(wm.clients)
}

// GetAdminCount 获取管理员客户端数量
func (wm *WebSocketManager) GetAdminCount() int {
	wm.mutex.RLock()
	defer wm.mutex.RUnlock()
	
	count := 0
	for _, client := range wm.clients {
		if client.IsAdmin {
			count++
		}
	}
	return count
}

// run WebSocket管理器运行循环
func (wm *WebSocketManager) run() {
	for {
		select {
		case client := <-wm.register:
			wm.mutex.Lock()
			wm.clients[client.ID] = client
			wm.mutex.Unlock()

			logger.Info("WebSocket client connected",
				zap.String("client_id", client.ID),
				zap.String("ip", client.IPAddress),
			)

			// 发送连接成功消息
			welcomeMsg := &WSMessage{
				Type: "connected",
				Data: map[string]interface{}{
					"client_id": client.ID,
					"timestamp": time.Now().Unix(),
				},
				Timestamp: time.Now().Unix(),
			}
			wm.SendToClient(client.ID, welcomeMsg)

		case client := <-wm.unregister:
			wm.mutex.Lock()
			if _, ok := wm.clients[client.ID]; ok {
				delete(wm.clients, client.ID)
				close(client.Send)
			}
			wm.mutex.Unlock()

			logger.Info("WebSocket client disconnected",
				zap.String("client_id", client.ID),
				zap.String("ip", client.IPAddress),
			)

		case message := <-wm.broadcast:
			wm.mutex.RLock()
			for _, client := range wm.clients {
				select {
				case client.Send <- message:
				default:
					close(client.Send)
					delete(wm.clients, client.ID)
				}
			}
			wm.mutex.RUnlock()
		}
	}
}

// HandleMessages 处理WebSocket消息
func (wc *WebSocketClient) HandleMessages(manager *WebSocketManager, adminAuth *AdminAuthService) {
	defer func() {
		manager.RemoveClient(wc)
		wc.Conn.Close()
	}()

	// 设置连接参数
	wc.Conn.SetReadLimit(512)
	wc.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	wc.Conn.SetPongHandler(func(string) error {
		wc.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	// 启动写入协程
	go wc.writePump()

	// 读取消息循环
	for {
		var message WSMessage
		err := wc.Conn.ReadJSON(&message)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				logger.Error("WebSocket error", zap.Error(err))
			}
			break
		}

		wc.LastActive = time.Now()
		wc.handleMessage(&message, manager, adminAuth)
	}
}

// writePump 写入消息泵
func (wc *WebSocketClient) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		wc.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-wc.Send:
			wc.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				wc.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := wc.Conn.WriteMessage(websocket.TextMessage, message); err != nil {
				return
			}

		case <-ticker.C:
			wc.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := wc.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage 处理收到的消息
func (wc *WebSocketClient) handleMessage(message *WSMessage, manager *WebSocketManager, adminAuth *AdminAuthService) {
	switch message.Type {
	case WSMsgTypeHeartbeat:
		// 处理心跳消息
		response := &WSMessage{
			Type:      "heartbeat_response",
			Data:      map[string]interface{}{"status": "ok"},
			Timestamp: time.Now().Unix(),
		}
		manager.SendToClient(wc.ID, response)

	case WSMsgTypeAuth:
		// 处理认证消息
		wc.handleAuth(message, manager, adminAuth)

	case WSMsgTypeSubscribe:
		// 处理订阅消息
		wc.handleSubscribe(message, manager)

	case WSMsgTypeUnsubscribe:
		// 处理取消订阅消息
		wc.handleUnsubscribe(message, manager)

	default:
		logger.Warn("Unknown WebSocket message type",
			zap.String("type", message.Type),
			zap.String("client_id", wc.ID),
		)
	}
}

// handleAuth 处理认证
func (wc *WebSocketClient) handleAuth(message *WSMessage, manager *WebSocketManager, adminAuth *AdminAuthService) {
	data, ok := message.Data.(map[string]interface{})
	if !ok {
		wc.sendError("Invalid auth data format")
		return
	}

	token, ok := data["token"].(string)
	if !ok || token == "" {
		wc.sendError("Missing token")
		return
	}

	// 验证JWT令牌
	claims, err := adminAuth.ValidateToken(token)
	if err != nil {
		logger.Warn("Invalid WebSocket auth token",
			zap.String("client_id", wc.ID),
			zap.Error(err),
		)
		wc.sendError("Invalid token")
		return
	}

	// 设置客户端管理员信息
	wc.IsAdmin = true
	wc.AdminID = claims.AdminID

	response := &WSMessage{
		Type: "auth_response",
		Data: map[string]interface{}{
			"success":   true,
			"is_admin":  wc.IsAdmin,
			"admin_id":  wc.AdminID,
			"username":  claims.Username,
			"role":      claims.Role,
		},
		Timestamp: time.Now().Unix(),
	}
	manager.SendToClient(wc.ID, response)

	logger.Info("WebSocket client authenticated",
		zap.String("client_id", wc.ID),
		zap.Bool("is_admin", wc.IsAdmin),
		zap.Uint("admin_id", wc.AdminID),
		zap.String("username", claims.Username),
	)
}

// handleSubscribe 处理订阅
func (wc *WebSocketClient) handleSubscribe(message *WSMessage, manager *WebSocketManager) {
	// 检查是否已认证
	if !wc.IsAdmin {
		wc.sendError("Not authenticated")
		return
	}

	data, ok := message.Data.(map[string]interface{})
	if !ok {
		wc.sendError("Invalid subscribe data format")
		return
	}

	topicsInterface, ok := data["topics"].([]interface{})
	if !ok {
		wc.sendError("Missing topics")
		return
	}

	// 转换主题列表
	var topics []string
	for _, topic := range topicsInterface {
		if topicStr, ok := topic.(string); ok {
			// 验证主题权限
			if wc.hasTopicPermission(topicStr) {
				wc.Subscriptions[topicStr] = true
				topics = append(topics, topicStr)
			}
		}
	}

	response := &WSMessage{
		Type: "subscribe_response",
		Data: map[string]interface{}{
			"success":     true,
			"topics":      topics,
			"total_subs":  len(wc.Subscriptions),
		},
		Timestamp: time.Now().Unix(),
	}
	manager.SendToClient(wc.ID, response)

	logger.Info("Client subscribed to topics",
		zap.String("client_id", wc.ID),
		zap.Strings("topics", topics),
	)
}

// handleUnsubscribe 处理取消订阅
func (wc *WebSocketClient) handleUnsubscribe(message *WSMessage, manager *WebSocketManager) {
	if !wc.IsAdmin {
		wc.sendError("Not authenticated")
		return
	}

	data, ok := message.Data.(map[string]interface{})
	if !ok {
		// 取消所有订阅
		wc.Subscriptions = make(map[string]bool)
		response := &WSMessage{
			Type: "unsubscribe_response",
			Data: map[string]interface{}{
				"success": true,
				"message": "All subscriptions cancelled",
			},
			Timestamp: time.Now().Unix(),
		}
		manager.SendToClient(wc.ID, response)
		return
	}

	topicsInterface, ok := data["topics"].([]interface{})
	if !ok {
		wc.sendError("Invalid topics format")
		return
	}

	// 移除指定主题
	var removedTopics []string
	for _, topic := range topicsInterface {
		if topicStr, ok := topic.(string); ok {
			if wc.Subscriptions[topicStr] {
				delete(wc.Subscriptions, topicStr)
				removedTopics = append(removedTopics, topicStr)
			}
		}
	}

	response := &WSMessage{
		Type: "unsubscribe_response",
		Data: map[string]interface{}{
			"success":        true,
			"removed_topics": removedTopics,
			"total_subs":     len(wc.Subscriptions),
		},
		Timestamp: time.Now().Unix(),
	}
	manager.SendToClient(wc.ID, response)

	logger.Info("Client unsubscribed from topics",
		zap.String("client_id", wc.ID),
		zap.Strings("removed_topics", removedTopics),
	)
}

// hasTopicPermission 检查主题权限
func (wc *WebSocketClient) hasTopicPermission(topic string) bool {
	if !wc.IsAdmin {
		return false
	}

	// 管理员可以订阅所有主题
	allowedTopics := []string{
		"metrics",      // 系统指标
		"alerts",       // 告警信息
		"user_events",  // 用户事件
		"system_events", // 系统事件
		"stats",        // 统计数据
		"logs",         // 日志信息
	}

	for _, allowed := range allowedTopics {
		if topic == allowed {
			return true
		}
	}

	return false
}

// sendError 发送错误消息
func (wc *WebSocketClient) sendError(message string) {
	errorMsg := &WSMessage{
		Type: WSMsgTypeError,
		Data: map[string]interface{}{
			"error":   true,
			"message": message,
		},
		Timestamp: time.Now().Unix(),
	}

	data, _ := json.Marshal(errorMsg)
	select {
	case wc.Send <- data:
	default:
		// 发送缓冲区已满，忽略
	}
}

// 实时数据推送功能

// PushSystemStats 推送系统统计数据
func (wm *WebSocketManager) PushSystemStats(stats interface{}) {
	message := &WSMessage{
		Type:      WSMsgTypeStats,
		Data:      stats,
		Timestamp: time.Now().Unix(),
	}
	wm.SendToSubscribers("stats", message)
}

// PushMetrics 推送系统指标
func (wm *WebSocketManager) PushMetrics(metrics *monitor.SystemMetrics) {
	message := &WSMessage{
		Type:      WSMsgTypeMetrics,
		Data:      metrics,
		Timestamp: time.Now().Unix(),
	}
	wm.SendToSubscribers("metrics", message)
}

// PushAlert 推送告警
func (wm *WebSocketManager) PushAlert(alert monitor.AlertEvent) {
	message := &WSMessage{
		Type:      WSMsgTypeAlert,
		Data:      alert,
		Timestamp: time.Now().Unix(),
	}
	wm.SendToSubscribers("alerts", message)
}

// PushUserEvent 推送用户事件
func (wm *WebSocketManager) PushUserEvent(eventType string, userInfo interface{}) {
	message := &WSMessage{
		Type:      eventType,
		Data:      userInfo,
		Timestamp: time.Now().Unix(),
	}
	wm.SendToSubscribers("user_events", message)
}

// PushSystemEvent 推送系统事件
func (wm *WebSocketManager) PushSystemEvent(event string, details interface{}) {
	message := &WSMessage{
		Type: WSMsgTypeSystemEvent,
		Data: map[string]interface{}{
			"event":   event,
			"details": details,
		},
		Timestamp: time.Now().Unix(),
	}
	wm.SendToSubscribers("system_events", message)
}

// PushLogEvent 推送日志事件
func (wm *WebSocketManager) PushLogEvent(level string, message string, fields map[string]interface{}) {
	logMessage := &WSMessage{
		Type: "log_event",
		Data: map[string]interface{}{
			"level":   level,
			"message": message,
			"fields":  fields,
		},
		Timestamp: time.Now().Unix(),
	}
	wm.SendToSubscribers("logs", logMessage)
}

// 辅助函数

// generateClientID 生成客户端ID
func generateClientID() string {
	return fmt.Sprintf("ws_%d_%d", time.Now().UnixNano(), time.Now().Nanosecond())
}

// WebSocket数据推送服务
type WebSocketPusher struct {
	manager *WebSocketManager
}

// NewWebSocketPusher 创建WebSocket推送服务
func NewWebSocketPusher(manager *WebSocketManager) *WebSocketPusher {
	return &WebSocketPusher{manager: manager}
}

// StartRealTimeUpdates 启动实时更新
func (wp *WebSocketPusher) StartRealTimeUpdates(metricsCollector monitor.MetricsCollector) {
	if metricsCollector == nil {
		return
	}

	// 每30秒推送一次系统指标
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for range ticker.C {
			if wp.manager.GetAdminCount() > 0 {
				metrics := metricsCollector.GetMetrics()
				wp.manager.PushMetrics(metrics)
			}
		}
	}()

	// 检查告警并推送
	go func() {
		ticker := time.NewTicker(10 * time.Second)
		defer ticker.Stop()

		for range ticker.C {
			if wp.manager.GetAdminCount() > 0 {
				alerts := metricsCollector.CheckAlerts()
				for _, alert := range alerts {
					wp.manager.PushAlert(alert)
				}
			}
		}
	}()
}

// NotifyUserOnline 通知用户上线
func (wp *WebSocketPusher) NotifyUserOnline(userID uint, username string) {
	wp.manager.PushUserEvent(WSMsgTypeUserOnline, map[string]interface{}{
		"user_id":  userID,
		"username": username,
		"timestamp": time.Now().Unix(),
	})
}

// NotifyUserOffline 通知用户下线
func (wp *WebSocketPusher) NotifyUserOffline(userID uint, username string) {
	wp.manager.PushUserEvent(WSMsgTypeUserOffline, map[string]interface{}{
		"user_id":  userID,
		"username": username,
		"timestamp": time.Now().Unix(),
	})
}