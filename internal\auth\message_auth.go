package auth

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"qweb-verification/internal/models"
	"qweb-verification/internal/security"
)

// MessageSigner 消息签名器接口
type MessageSigner interface {
	SignMessage(data interface{}, secretKey string) (string, error)
	VerifySignature(data interface{}, signature, secretKey string) error
}

// TimestampValidator 时间戳验证器接口
type TimestampValidator interface {
	ValidateTimestamp(timestamp int64, tolerance time.Duration) error
}

// NonceManager 随机数管理器接口
type NonceManager interface {
	IsNonceUsed(nonce string) bool
	MarkNonceUsed(nonce string) error
	CleanExpiredNonces() error
}

// AuthRequest 认证请求
type AuthRequest struct {
	Type      string `json:"type"`           // "user_auth" 或 "card_auth"
	Timestamp int64  `json:"timestamp"`      // 时间戳
	Nonce     string `json:"nonce"`          // 随机数
	Username  string `json:"username,omitempty"`   // 用户名（用户认证时使用）
	Password  string `json:"password,omitempty"`   // 密码
	CardNumber string `json:"card_number,omitempty"` // 卡号（卡密认证时使用）
	CardPassword string `json:"card_password,omitempty"` // 卡密
	Signature string `json:"signature"`      // 消息签名
}

// AuthResponse 认证响应
type AuthResponse struct {
	Success      bool               `json:"success"`
	SessionToken string             `json:"session_token,omitempty"`
	ExpiresAt    time.Time          `json:"expires_at,omitempty"`
	UserInfo     *AuthUserInfo      `json:"user_info,omitempty"`
	Message      string             `json:"message"`
	ErrorCode    string             `json:"error_code,omitempty"`
}

// AuthUserInfo 认证用户信息
type AuthUserInfo struct {
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	Level    int    `json:"level"`
	AgentID  *uint  `json:"agent_id,omitempty"`
}

// HMACMessageSigner HMAC消息签名器实现
type HMACMessageSigner struct{}

// NewHMACMessageSigner 创建HMAC消息签名器
func NewHMACMessageSigner() *HMACMessageSigner {
	return &HMACMessageSigner{}
}

// SignMessage 签名消息
func (s *HMACMessageSigner) SignMessage(data interface{}, secretKey string) (string, error) {
	// 序列化数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", fmt.Errorf("failed to marshal data: %w", err)
	}

	// 计算HMAC-SHA256签名
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write(jsonData)
	signature := hex.EncodeToString(h.Sum(nil))

	return signature, nil
}

// VerifySignature 验证签名
func (s *HMACMessageSigner) VerifySignature(data interface{}, signature, secretKey string) error {
	expectedSignature, err := s.SignMessage(data, secretKey)
	if err != nil {
		return fmt.Errorf("failed to generate expected signature: %w", err)
	}

	if !hmac.Equal([]byte(signature), []byte(expectedSignature)) {
		return models.ErrInvalidSignature
	}

	return nil
}

// SimpleTimestampValidator 简单时间戳验证器
type SimpleTimestampValidator struct{}

// NewSimpleTimestampValidator 创建简单时间戳验证器
func NewSimpleTimestampValidator() *SimpleTimestampValidator {
	return &SimpleTimestampValidator{}
}

// ValidateTimestamp 验证时间戳
func (v *SimpleTimestampValidator) ValidateTimestamp(timestamp int64, tolerance time.Duration) error {
	now := time.Now().Unix()
	diff := now - timestamp

	if diff < 0 {
		diff = -diff
	}

	if time.Duration(diff)*time.Second > tolerance {
		return models.ErrInvalidTimestamp
	}

	return nil
}

// MemoryNonceManager 内存随机数管理器
type MemoryNonceManager struct {
	usedNonces map[string]time.Time
	ttl        time.Duration
}

// NewMemoryNonceManager 创建内存随机数管理器
func NewMemoryNonceManager(ttl time.Duration) *MemoryNonceManager {
	nm := &MemoryNonceManager{
		usedNonces: make(map[string]time.Time),
		ttl:        ttl,
	}

	// 启动清理协程
	go nm.cleanupRoutine()

	return nm
}

// IsNonceUsed 检查随机数是否已使用
func (nm *MemoryNonceManager) IsNonceUsed(nonce string) bool {
	_, exists := nm.usedNonces[nonce]
	return exists
}

// MarkNonceUsed 标记随机数已使用
func (nm *MemoryNonceManager) MarkNonceUsed(nonce string) error {
	nm.usedNonces[nonce] = time.Now()
	return nil
}

// CleanExpiredNonces 清理过期随机数
func (nm *MemoryNonceManager) CleanExpiredNonces() error {
	cutoff := time.Now().Add(-nm.ttl)
	for nonce, timestamp := range nm.usedNonces {
		if timestamp.Before(cutoff) {
			delete(nm.usedNonces, nonce)
		}
	}
	return nil
}

// cleanupRoutine 清理协程
func (nm *MemoryNonceManager) cleanupRoutine() {
	ticker := time.NewTicker(nm.ttl / 2)
	defer ticker.Stop()

	for range ticker.C {
		nm.CleanExpiredNonces()
	}
}

// AuthService 认证服务
type AuthService struct {
	signer           MessageSigner
	timestampValidator TimestampValidator
	nonceManager     NonceManager
	encryptor        security.Encryptor
	secretKey        string
	timestampTolerance time.Duration
}

// NewAuthService 创建认证服务
func NewAuthService(encryptor security.Encryptor, secretKey string) *AuthService {
	return &AuthService{
		signer:             NewHMACMessageSigner(),
		timestampValidator: NewSimpleTimestampValidator(),
		nonceManager:       NewMemoryNonceManager(10 * time.Minute),
		encryptor:          encryptor,
		secretKey:          secretKey,
		timestampTolerance: 5 * time.Minute,
	}
}

// ValidateAuthRequest 验证认证请求
func (as *AuthService) ValidateAuthRequest(req *AuthRequest) error {
	// 验证时间戳
	if err := as.timestampValidator.ValidateTimestamp(req.Timestamp, as.timestampTolerance); err != nil {
		return err
	}

	// 检查随机数是否已使用
	if as.nonceManager.IsNonceUsed(req.Nonce) {
		return models.ErrReplayAttack
	}

	// 标记随机数已使用
	if err := as.nonceManager.MarkNonceUsed(req.Nonce); err != nil {
		return fmt.Errorf("failed to mark nonce as used: %w", err)
	}

	// 验证消息签名
	reqCopy := *req
	reqCopy.Signature = "" // 清空签名字段
	if err := as.signer.VerifySignature(reqCopy, req.Signature, as.secretKey); err != nil {
		return err
	}

	return nil
}

// CreateAuthRequest 创建认证请求（客户端使用）
func (as *AuthService) CreateAuthRequest(authType, username, password, cardNumber, cardPassword string) (*AuthRequest, error) {
	req := &AuthRequest{
		Type:      authType,
		Timestamp: time.Now().Unix(),
		Nonce:     generateNonce(),
	}

	switch authType {
	case "user_auth":
		if username == "" || password == "" {
			return nil, fmt.Errorf("username and password are required for user authentication")
		}
		// 加密密码
		encryptedPassword, err := as.encryptor.Encrypt(password)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt password: %w", err)
		}
		req.Username = username
		req.Password = encryptedPassword

	case "card_auth":
		if cardNumber == "" || cardPassword == "" {
			return nil, fmt.Errorf("card number and card password are required for card authentication")
		}
		// 加密卡密
		encryptedCardPassword, err := as.encryptor.Encrypt(cardPassword)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt card password: %w", err)
		}
		req.CardNumber = cardNumber
		req.CardPassword = encryptedCardPassword

	default:
		return nil, fmt.Errorf("invalid authentication type: %s", authType)
	}

	// 生成签名
	signature, err := as.signer.SignMessage(req, as.secretKey)
	if err != nil {
		return nil, fmt.Errorf("failed to sign message: %w", err)
	}
	req.Signature = signature

	return req, nil
}

// DecryptAuthRequest 解密认证请求中的敏感信息
func (as *AuthService) DecryptAuthRequest(req *AuthRequest) error {
	if req.Password != "" {
		decryptedPassword, err := as.encryptor.Decrypt(req.Password)
		if err != nil {
			return fmt.Errorf("failed to decrypt password: %w", err)
		}
		req.Password = decryptedPassword
	}

	if req.CardPassword != "" {
		decryptedCardPassword, err := as.encryptor.Decrypt(req.CardPassword)
		if err != nil {
			return fmt.Errorf("failed to decrypt card password: %w", err)
		}
		req.CardPassword = decryptedCardPassword
	}

	return nil
}

// generateNonce 生成随机数
func generateNonce() string {
	nonce, err := security.GenerateSecureToken(16)
	if err != nil {
		// 降级到时间戳+随机数
		return fmt.Sprintf("%d_%s", time.Now().UnixNano(), strconv.FormatInt(time.Now().UnixNano(), 36))
	}
	return nonce
}

// CreateSuccessResponse 创建成功响应
func CreateSuccessResponse(sessionToken string, expiresAt time.Time, userInfo *AuthUserInfo) *AuthResponse {
	return &AuthResponse{
		Success:      true,
		SessionToken: sessionToken,
		ExpiresAt:    expiresAt,
		UserInfo:     userInfo,
		Message:      "Authentication successful",
	}
}

// CreateErrorResponse 创建错误响应
func CreateErrorResponse(message, errorCode string) *AuthResponse {
	return &AuthResponse{
		Success:   false,
		Message:   message,
		ErrorCode: errorCode,
	}
}