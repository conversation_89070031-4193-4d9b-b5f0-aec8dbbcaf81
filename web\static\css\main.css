/* Qweb网络验证系统 - 现代化主样式文件 */

/* 导入现代字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* CSS变量定义 - 现代化配色方案 */
:root {
  /* 主色调 - 现代蓝紫渐变 */
  --primary-color: #667eea;
  --primary-dark: #5a67d8;
  --primary-light: #7c3aed;
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-gradient-hover: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);

  /* 辅助色彩 */
  --secondary-color: #6b7280;
  --success-color: #10b981;
  --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --warning-color: #f59e0b;
  --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --error-color: #ef4444;
  --error-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --info-color: #06b6d4;
  --info-gradient: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);

  /* 背景色彩 - 现代化渐变背景 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --bg-gradient-light: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  --bg-dark: #1e293b;
  --bg-darker: #0f172a;

  /* 玻璃拟态效果 */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --glass-backdrop: blur(8px);

  /* 文字颜色 */
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --text-light: #ffffff;
  --text-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  /* 边框和阴影 */
  --border-color: #e5e7eb;
  --border-dark: #d1d5db;
  --border-gradient: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);

  /* 现代化阴影系统 */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.3);
  --shadow-glow-hover: 0 0 30px rgba(102, 126, 234, 0.5);

  /* 圆角系统 */
  --radius-xs: 2px;
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 24px;
  --radius-3xl: 32px;

  /* 动画和过渡 */
  --transition-fast: all 0.15s ease-out;
  --transition-normal: all 0.3s ease-out;
  --transition-slow: all 0.5s ease-out;
  --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 全局重置和基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.6;
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-weight: 400;
  letter-spacing: -0.01em;
  overflow-x: hidden;
  position: relative;
}

/* 现代化背景效果 */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-gradient-light);
  z-index: -2;
  opacity: 0.5;
}

body::after {
  content: '';
  position: fixed;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
  z-index: -1;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
}

/* SVG图标通用样式修复 */
svg {
  display: block;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  flex-shrink: 0;
  max-width: 100%;
  max-height: 100%;
}

/* 确保SVG图标有默认尺寸 */
svg:not([width]):not([height]) {
  width: 24px;
  height: 24px;
}

/* 修复容器内的SVG图标 */
.logo-icon-wrapper svg,
.btn svg,
.input-group svg,
.feature-icon svg,
.nav-link svg,
.mobile-nav-link svg {
  width: 100%;
  height: 100%;
  display: block;
}

/* 防止布局错乱的额外修复 */
.modern-home-container {
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
}

.hero-container,
.section-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 确保flex容器正常工作 */
.nav-container,
.hero-content,
.modern-features-grid {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.modern-features-grid {
  display: grid;
}

/* Emoji图标样式 */
.logo-emoji,
.title-icon-text,
.metric-icon,
.feature-icon {
  font-size: 2rem;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-icon {
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 1rem;
  color: var(--text-tertiary);
  z-index: 1;
  transition: var(--transition-normal);
}

.password-toggle .eye-icon {
  font-size: 1.25rem;
}

/* 选择文本样式 */
::selection {
  background: var(--primary-color);
  color: var(--text-light);
}

::-moz-selection {
  background: var(--primary-color);
  color: var(--text-light);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-gradient);
  border-radius: var(--radius-lg);
  transition: var(--transition-normal);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-gradient-hover);
}

/* 通用工具类 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}

/* 网格布局系统 */
.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

/* 大屏幕适配 */
@media (min-width: 1024px) {
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* 中等屏幕适配 */
@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

/* 统计卡片样式 */
.stat-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
}

.stat-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.stat-card-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin: 0;
}

.stat-card-icon {
  width: 2rem;
  height: 2rem;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-card-icon svg {
  width: 1.25rem;
  height: 1.25rem;
}

.stat-card-icon.primary {
  background: var(--primary-color);
}

.stat-card-icon.success {
  background: var(--success-color);
}

.stat-card-icon.warning {
  background: var(--warning-color);
}

.stat-card-icon.error {
  background: var(--error-color);
}

.stat-card-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  line-height: 1;
}

.stat-card-change {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.stat-card-change.positive {
  color: var(--success-color);
}

.stat-card-change.negative {
  color: var(--error-color);
}

.stat-card-change svg {
  width: 0.875rem;
  height: 0.875rem;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 2rem;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: var(--text-secondary);
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 2rem;
  color: var(--text-secondary);
  text-align: center;
}

.empty-state svg {
  width: 4rem;
  height: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.empty-state p {
  margin: 0;
  font-size: 0.875rem;
}

/* 间距工具类 */
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }

.m-0 { margin: 0; }
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 0.75rem; }
.m-4 { margin: 1rem; }
.m-6 { margin: 1.5rem; }
.m-8 { margin: 2rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 0.25rem; }
.pt-2 { padding-top: 0.5rem; }
.pt-3 { padding-top: 0.75rem; }
.pt-4 { padding-top: 1rem; }
.pt-6 { padding-top: 1.5rem; }
.pt-8 { padding-top: 2rem; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 0.25rem; }
.pb-2 { padding-bottom: 0.5rem; }
.pb-3 { padding-bottom: 0.75rem; }
.pb-4 { padding-bottom: 1rem; }
.pb-6 { padding-bottom: 1.5rem; }
.pb-8 { padding-bottom: 2rem; }

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

.text-xs {
  font-size: 0.75rem;
}

.text-sm {
  font-size: 0.875rem;
}

.text-base {
  font-size: 1rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-3xl {
  font-size: 1.875rem;
}

.text-4xl {
  font-size: 2.25rem;
}

/* 颜色工具类 */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-tertiary {
  color: var(--text-tertiary);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-error {
  color: var(--error-color);
}

.text-info {
  color: var(--info-color);
}

.bg-primary {
  background-color: var(--bg-primary);
}

.bg-secondary {
  background-color: var(--bg-secondary);
}

.bg-tertiary {
  background-color: var(--bg-tertiary);
}

/* 显示/隐藏工具类 */
.hidden {
  display: none;
}

.block {
  display: block;
}

.inline {
  display: inline;
}

.inline-block {
  display: inline-block;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.grid {
  display: grid;
}

/* 宽度和高度工具类 */
.w-full {
  width: 100%;
}

.w-auto {
  width: auto;
}

.h-full {
  height: 100%;
}

.h-auto {
  height: auto;
}

.min-h-screen {
  min-height: 100vh;
}

/* 位置工具类 */
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

/* 边框工具类 */
.border {
  border: 1px solid var(--border-color);
}

.border-t {
  border-top: 1px solid var(--border-color);
}

.border-b {
  border-bottom: 1px solid var(--border-color);
}

.border-l {
  border-left: 1px solid var(--border-color);
}

.border-r {
  border-right: 1px solid var(--border-color);
}

.rounded {
  border-radius: var(--radius-md);
}

.rounded-sm {
  border-radius: var(--radius-sm);
}

.rounded-lg {
  border-radius: var(--radius-lg);
}

.rounded-xl {
  border-radius: var(--radius-xl);
}

.rounded-full {
  border-radius: 9999px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 现代化导航栏样式 */
.modern-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  transition: var(--transition-normal);
}

.modern-nav.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-lg);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

.modern-logo {
  display: flex;
  align-items: center;
  gap: 1rem;
  text-decoration: none;
  color: var(--text-primary);
}

.logo-text-only {
  display: flex;
  flex-direction: column;
}

.brand-text-wrapper {
  display: flex;
  flex-direction: column;
}

.brand-text {
  font-size: 1.5rem;
  font-weight: 800;
  background: var(--text-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
}

.brand-subtitle {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-top: 0.125rem;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-link {
  position: relative;
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.875rem;
  padding: 0.5rem 0;
  transition: var(--transition-normal);
}

.nav-link:hover {
  color: var(--primary-color);
}

.nav-link-underline {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-gradient);
  transition: var(--transition-normal);
}

.nav-link:hover .nav-link-underline {
  width: 100%;
}

.btn-nav {
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  border-radius: var(--radius-xl);
}

/* 移动端导航 */
.modern-mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
  font-weight: 500;
  color: var(--text-primary);
}

.modern-mobile-menu-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  color: var(--primary-color);
}

.menu-text {
  font-size: 0.875rem;
}

.modern-mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-xl);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-20px);
  transition: var(--transition-normal);
}

.modern-mobile-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.mobile-menu-content {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  border-radius: var(--radius-lg);
  transition: var(--transition-normal);
}

.mobile-nav-link:hover {
  background: rgba(102, 126, 234, 0.1);
  color: var(--primary-color);
}

.mobile-menu-divider {
  height: 1px;
  background: var(--border-color);
  margin: 0.5rem 0;
}

/* 布局组件 */
.sidebar {
  width: 280px;
  min-height: 100vh;
  background: var(--bg-primary);
  border-right: 1px solid var(--border-color);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: var(--transition-normal);
}

.sidebar.collapsed {
  width: 64px;
}

.main-content {
  margin-left: 280px;
  min-height: 100vh;
  background: var(--bg-secondary);
  transition: var(--transition-normal);
}

.main-content.expanded {
  margin-left: 64px;
}

/* 现代化英雄区域样式 */
.modern-hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding-top: 80px;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.hero-gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.7;
  animation: float 20s ease-in-out infinite;
}

.hero-orb-1 {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.4) 0%, transparent 70%);
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.hero-orb-2 {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(118, 75, 162, 0.3) 0%, transparent 70%);
  top: 60%;
  right: 20%;
  animation-delay: -7s;
}

.hero-orb-3 {
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(16, 185, 129, 0.2) 0%, transparent 70%);
  bottom: 20%;
  left: 60%;
  animation-delay: -14s;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: calc(100vh - 80px);
}

.hero-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-3xl);
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--primary-color);
  width: fit-content;
  box-shadow: var(--shadow-md);
  animation: fadeInUp 1s ease-out;
}

.badge-icon {
  width: 1rem;
  height: 1rem;
  color: var(--primary-color);
}

.modern-hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin: 0;
  animation: fadeInUp 1s ease-out 0.2s both;
}

.title-line {
  display: block;
  color: var(--text-primary);
}

.title-highlight {
  display: block;
  background: var(--text-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.title-highlight::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--primary-gradient);
  border-radius: 2px;
  opacity: 0.3;
}

.modern-hero-description {
  font-size: 1.25rem;
  line-height: 1.6;
  color: var(--text-secondary);
  max-width: 600px;
  animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-stats {
  display: flex;
  gap: 2rem;
  animation: fadeInUp 1s ease-out 0.6s both;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary-color);
  line-height: 1;
  position: relative;
}

.stat-unit {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-left: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-top: 0.5rem;
}

.modern-hero-actions {
  display: flex;
  gap: 1rem;
  animation: fadeInUp 1s ease-out 0.8s both;
}

.hero-btn-primary,
.hero-btn-secondary {
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: var(--radius-2xl);
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeInRight 1s ease-out 0.4s both;
}

.hero-dashboard {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: 2rem;
  box-shadow: var(--glass-shadow);
  width: 100%;
  max-width: 500px;
}

.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.dashboard-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
  color: var(--text-primary);
}

.title-icon {
  width: 2rem;
  height: 2rem;
  background: var(--primary-gradient);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.title-icon svg {
  width: 1.25rem;
  height: 1.25rem;
}

.dashboard-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--success-color);
}

.status-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background: var(--success-color);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 头部导航栏样式 */
.header {
  height: 64px;
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: 0 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 999;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sidebar-toggle {
  display: none;
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  transition: var(--transition);
}

.sidebar-toggle:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.sidebar-toggle svg {
  width: 1.25rem;
  height: 1.25rem;
}

.breadcrumb {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* 状态指示器 */
.status-indicators {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
}

.status-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-success {
  background: var(--success-color);
}

.status-warning {
  background: var(--warning-color);
}

.status-error {
  background: var(--error-color);
}

.status-text {
  font-weight: 500;
  color: var(--text-secondary);
}

/* 通知下拉菜单 */
.notification-dropdown {
  position: relative;
}

.notification-btn {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  transition: var(--transition);
  position: relative;
}

.notification-btn:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.notification-btn svg {
  width: 1.25rem;
  height: 1.25rem;
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: var(--error-color);
  color: white;
  font-size: 0.625rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 9999px;
  min-width: 1rem;
  height: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translate(25%, -25%);
}

.notification-panel {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  width: 320px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: var(--transition);
}

.notification-panel.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.notification-header h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-empty {
  padding: 2rem;
  text-align: center;
  color: var(--text-secondary);
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  transition: var(--transition);
}

.notification-item:hover {
  background: var(--bg-secondary);
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: var(--text-primary);
}

.notification-message {
  font-size: 0.75rem;
  margin: 0 0 0.25rem 0;
  color: var(--text-secondary);
  line-height: 1.4;
}

.notification-time {
  font-size: 0.625rem;
  color: var(--text-tertiary);
}

.notification-close {
  background: none;
  border: none;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
  cursor: pointer;
  transition: var(--transition);
  flex-shrink: 0;
}

.notification-close:hover {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

/* 用户下拉菜单 */
.user-dropdown {
  position: relative;
}

.user-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  transition: var(--transition);
}

.user-btn:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.user-avatar {
  width: 2rem;
  height: 2rem;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.user-avatar svg {
  width: 1.25rem;
  height: 1.25rem;
}

.user-name {
  font-weight: 500;
  font-size: 0.875rem;
}

.dropdown-arrow {
  width: 1rem;
  height: 1rem;
  transition: var(--transition);
}

.user-dropdown.open .dropdown-arrow {
  transform: rotate(180deg);
}

.user-menu {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  width: 200px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: var(--transition);
  overflow: hidden;
}

.user-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.user-menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition);
  font-size: 0.875rem;
}

.user-menu-item:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.menu-icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

.user-menu-divider {
  margin: 0;
  border: none;
  border-top: 1px solid var(--border-color);
}

/* 警告容器 */
.alerts-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 10000;
  max-width: 400px;
  pointer-events: none;
}

.alert {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 1rem;
  margin-bottom: 0.5rem;
  box-shadow: var(--shadow-lg);
  pointer-events: auto;
  opacity: 0;
  transform: translateX(100%);
  transition: var(--transition);
}

.alert.show {
  opacity: 1;
  transform: translateX(0);
}

.alert-success {
  border-left: 4px solid var(--success-color);
}

.alert-warning {
  border-left: 4px solid var(--warning-color);
}

.alert-error {
  border-left: 4px solid var(--error-color);
}

.alert-info {
  border-left: 4px solid var(--info-color);
}

.content {
  padding: 1.5rem;
}

/* 卡片组件 */
.card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-tertiary);
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  background: var(--bg-tertiary);
}

/* 现代化按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-normal);
  line-height: 1.5;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-md);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-normal);
}

.btn:hover::before {
  left: 100%;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-sm);
}

.btn:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn:not(:disabled):active {
  transform: translateY(0);
  box-shadow: var(--shadow-md);
}

/* 按钮变体 */
.btn-primary {
  background: var(--primary-gradient);
  color: var(--text-light);
  box-shadow: var(--shadow-md), var(--shadow-glow);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-gradient-hover);
  box-shadow: var(--shadow-lg), var(--shadow-glow-hover);
}

.btn-secondary {
  background: var(--glass-bg);
  color: var(--text-primary);
  border: 1px solid var(--glass-border);
  backdrop-filter: var(--glass-backdrop);
  box-shadow: var(--glass-shadow);
}

.btn-secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.35);
  border-color: rgba(255, 255, 255, 0.25);
}

.btn-success {
  background: var(--success-gradient);
  color: var(--text-light);
}

.btn-warning {
  background: var(--warning-gradient);
  color: var(--text-light);
}

.btn-error {
  background: var(--error-gradient);
  color: var(--text-light);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  box-shadow: none;
}

.btn-outline:hover:not(:disabled) {
  background: var(--primary-color);
  color: var(--text-light);
}

/* 按钮尺寸 */
.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
  border-radius: var(--radius-md);
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1rem;
  border-radius: var(--radius-xl);
}

.btn-xl {
  padding: 1.25rem 2.5rem;
  font-size: 1.125rem;
  border-radius: var(--radius-2xl);
}

/* 按钮图标 */
.btn-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

.btn-sm .btn-icon {
  width: 1rem;
  height: 1rem;
}

.btn-lg .btn-icon {
  width: 1.5rem;
  height: 1.5rem;
}

/* 全宽按钮 */
.btn-full {
  width: 100%;
}

/* 按钮加载状态 */
.btn-loading {
  display: none;
  align-items: center;
  gap: 0.5rem;
}

.btn-loading .spinner {
  width: 1rem;
  height: 1rem;
  border-width: 2px;
}

/* 现代化表单组件 */
.form-group {
  margin-bottom: 1.5rem;
  position: relative;
}

.form-label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 500;
  transition: var(--transition-normal);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  color: var(--text-primary);
  box-shadow: var(--shadow-sm);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--text-tertiary);
  font-weight: 400;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md), 0 0 0 4px rgba(102, 126, 234, 0.1);
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

.form-input:invalid,
.form-textarea:invalid,
.form-select:invalid {
  border-color: var(--error-color);
  box-shadow: var(--shadow-sm), 0 0 0 4px rgba(239, 68, 68, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.6;
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23667eea' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 1rem center;
  background-repeat: no-repeat;
  background-size: 1.25em 1.25em;
  padding-right: 3rem;
  cursor: pointer;
}

/* 输入组样式 */
.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1rem;
  width: 1.25rem;
  height: 1.25rem;
  color: var(--text-tertiary);
  z-index: 1;
  transition: var(--transition-normal);
}

.input-group .form-input {
  padding-left: 3rem;
}

.input-group .form-input:focus + .input-icon,
.input-group:focus-within .input-icon {
  color: var(--primary-color);
}

/* 密码切换按钮 */
.password-toggle {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
}

.password-toggle:hover {
  color: var(--primary-color);
  background: rgba(102, 126, 234, 0.1);
}

.password-toggle svg {
  width: 1.25rem;
  height: 1.25rem;
}

/* 复选框样式 */
.checkbox-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  user-select: none;
}

.checkbox-container input[type="checkbox"] {
  display: none;
}

.checkbox-checkmark {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  position: relative;
  transition: var(--transition-normal);
  flex-shrink: 0;
}

.checkbox-container input[type="checkbox"]:checked + .checkbox-checkmark {
  background: var(--primary-gradient);
  border-color: var(--primary-color);
}

.checkbox-container input[type="checkbox"]:checked + .checkbox-checkmark::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 0.375rem;
  height: 0.75rem;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: translate(-50%, -60%) rotate(45deg);
}

.checkbox-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}
}

/* 表格组件 */
.table-container {
  overflow-x: auto;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-primary);
}

.table th,
.table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.table th {
  background: var(--bg-tertiary);
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
  letter-spacing: 0.025em;
}

.table tbody tr:hover {
  background: var(--bg-secondary);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* 状态标签 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.125rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1.5;
}

.badge-success {
  background: rgb(16 185 129 / 0.1);
  color: var(--success-color);
}

.badge-warning {
  background: rgb(245 158 11 / 0.1);
  color: var(--warning-color);
}

.badge-error {
  background: rgb(239 68 68 / 0.1);
  color: var(--error-color);
}

.badge-info {
  background: rgb(6 182 212 / 0.1);
  color: var(--info-color);
}

.badge-secondary {
  background: rgb(100 116 139 / 0.1);
  color: var(--secondary-color);
}

/* 导航组件 */
.nav-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition);
  border-radius: var(--radius-md);
  margin: 0.125rem 0.5rem;
}

.nav-item:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.nav-item.active {
  background: var(--primary-color);
  color: var(--text-light);
}

/* 侧边栏头部样式 */
.sidebar-header {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid var(--border-color);
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo-icon {
  width: 2rem;
  height: 2rem;
  color: var(--primary-color);
  flex-shrink: 0;
}

.logo-text {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--text-primary);
  white-space: nowrap;
}

/* 侧边栏内容 */
.sidebar-content {
  padding: 1rem 0;
  height: calc(100vh - 120px);
  overflow-y: auto;
}

.nav-menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-menu li {
  margin: 0;
}

/* 导航图标样式 */
.nav-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.75rem;
  flex-shrink: 0;
  stroke-width: 2;
}

.nav-text {
  font-weight: 500;
  white-space: nowrap;
}

/* 侧边栏折叠状态 */
.sidebar.collapsed .logo-text,
.sidebar.collapsed .nav-text {
  display: none;
}

.sidebar.collapsed .logo {
  justify-content: center;
}

.sidebar.collapsed .nav-item {
  justify-content: center;
  padding: 0.75rem;
}

.sidebar.collapsed .nav-icon {
  margin-right: 0;
}

/* 模态框组件 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgb(0 0 0 / 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  transform: scale(0.95);
  transition: var(--transition);
}

.modal-overlay.show .modal {
  transform: scale(1);
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    width: 280px;
    z-index: 1001;
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .main-content.expanded {
    margin-left: 0;
  }
  
  .header {
    padding: 0 1rem;
  }
  
  .sidebar-toggle {
    display: flex;
  }
  
  .content {
    padding: 1rem;
  }
  
  .card-header,
  .card-body {
    padding: 1rem;
  }
  
  .table-container {
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4,
  .md\:grid-cols-2,
  .md\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .stat-card {
    padding: 1rem;
  }
  
  .stat-card-value {
    font-size: 1.5rem;
  }
  
  .notification-panel,
  .user-menu {
    width: 280px;
    right: -1rem;
  }
}

@media (max-width: 480px) {
  .btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .card-header,
  .card-body {
    padding: 0.75rem;
  }
  
  .table th,
  .table td {
    padding: 0.5rem;
    font-size: 0.8rem;
  }
  
  .modal {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }
  
  .stat-card {
    padding: 0.75rem;
  }
  
  .stat-card-value {
    font-size: 1.25rem;
  }
  
  .stat-card-icon {
    width: 1.5rem;
    height: 1.5rem;
  }
  
  .stat-card-icon svg {
    width: 1rem;
    height: 1rem;
  }
  
  .header {
    padding: 0 0.75rem;
  }
  
  .content {
    padding: 0.75rem;
  }
  
  .notification-panel,
  .user-menu {
    width: calc(100vw - 2rem);
    right: 1rem;
    left: 1rem;
  }
}

/* 现代化登录页面样式 */
.modern-login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -2;
}

.bg-gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.6;
  animation: float 25s ease-in-out infinite;
}

.bg-orb-1 {
  width: 500px;
  height: 500px;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%);
  top: -10%;
  left: -10%;
  animation-delay: 0s;
}

.bg-orb-2 {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(118, 75, 162, 0.25) 0%, transparent 70%);
  bottom: -10%;
  right: -10%;
  animation-delay: -10s;
}

.bg-orb-3 {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(16, 185, 129, 0.2) 0%, transparent 70%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: -20s;
}

.bg-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--primary-color);
  border-radius: 50%;
  opacity: 0.3;
  animation: particleFloat 15s linear infinite;
}

.particle:nth-child(1) {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.particle:nth-child(2) {
  top: 60%;
  left: 80%;
  animation-delay: -3s;
}

.particle:nth-child(3) {
  top: 80%;
  left: 40%;
  animation-delay: -6s;
}

.particle:nth-child(4) {
  top: 30%;
  left: 70%;
  animation-delay: -9s;
}

.particle:nth-child(5) {
  top: 70%;
  left: 10%;
  animation-delay: -12s;
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.8;
  }
}

.modern-login-container {
  position: relative;
  width: 100%;
  max-width: 480px;
  padding: 2rem;
  z-index: 1;
}

.login-nav {
  position: absolute;
  top: 2rem;
  left: 2rem;
  z-index: 10;
}

.back-home-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.875rem;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.back-home-link:hover {
  color: var(--primary-color);
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.back-icon {
  width: 1rem;
  height: 1rem;
}

.modern-login-box {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-3xl);
  padding: 3rem;
  box-shadow: var(--glass-shadow);
  animation: fadeInUp 0.8s ease-out;
}

.modern-login-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.login-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.logo-icon-container {
  width: 4rem;
  height: 4rem;
  background: var(--primary-gradient);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-glow);
  animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.logo-icon-container .logo-icon {
  width: 2rem;
  height: 2rem;
  color: white;
  stroke-width: 2.5;
}

.logo-title {
  font-size: 1.75rem;
  font-weight: 800;
  background: var(--text-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.logo-subtitle {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin: 0.25rem 0 0 0;
}

.login-welcome {
  margin-bottom: 1rem;
}

.welcome-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
}

.welcome-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 现代化功能特性样式 */
.modern-features-section {
  padding: 8rem 0;
  position: relative;
}

.modern-section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-3xl);
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  box-shadow: var(--shadow-md);
}

.modern-section-title {
  font-size: 3rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

.title-gradient {
  background: var(--text-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-normal {
  color: var(--text-primary);
}

.modern-section-description {
  font-size: 1.125rem;
  line-height: 1.6;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.modern-features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.modern-feature-card {
  position: relative;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: 2.5rem;
  transition: var(--transition-normal);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.modern-feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.feature-card-primary {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.05) 100%);
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.feature-card-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient);
  opacity: 0;
  transition: var(--transition-normal);
  border-radius: var(--radius-2xl);
}

.feature-card-primary:hover .feature-card-glow {
  opacity: 0.05;
}

.feature-icon-wrapper {
  margin-bottom: 1.5rem;
}

.feature-icon {
  width: 3.5rem;
  height: 3.5rem;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: var(--transition-normal);
}

.feature-icon svg {
  width: 2rem;
  height: 2rem;
  stroke-width: 2;
}

.feature-icon-security {
  background: var(--primary-gradient);
  box-shadow: var(--shadow-glow);
}

.feature-icon-agents {
  background: var(--success-gradient);
}

.feature-icon-monitor {
  background: var(--info-gradient);
}

.feature-icon-cards {
  background: var(--warning-gradient);
}

.feature-icon-audit {
  background: var(--error-gradient);
}

.feature-icon-performance {
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
}

.modern-feature-card:hover .feature-icon {
  transform: scale(1.1);
}

.feature-content {
  position: relative;
  z-index: 1;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.feature-description {
  font-size: 0.875rem;
  line-height: 1.6;
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

.feature-highlights {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.highlight-icon {
  width: 1rem;
  height: 1rem;
  color: var(--success-color);
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .nav-links {
    display: none;
  }

  .modern-mobile-menu-btn {
    display: flex;
  }

  .hero-container {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .modern-hero-title {
    font-size: 2.5rem;
  }

  .hero-stats {
    justify-content: center;
  }

  .modern-features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .nav-container {
    padding: 0 1rem;
  }

  .hero-container {
    padding: 0 1rem;
  }

  .modern-hero-title {
    font-size: 2rem;
  }

  .modern-hero-description {
    font-size: 1rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .modern-hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .modern-section-title {
    font-size: 2rem;
  }

  .modern-login-container {
    padding: 1rem;
  }

  .modern-login-box {
    padding: 2rem;
  }

  .login-nav {
    top: 1rem;
    left: 1rem;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

/* 加载状态 */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1e293b;
    --bg-secondary: #0f172a;
    --bg-tertiary: #334155;
    
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    
    --border-color: #334155;
    --border-dark: #475569;
  }
}