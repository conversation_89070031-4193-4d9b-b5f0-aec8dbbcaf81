<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qweb网络验证系统 - 现代化安全验证解决方案</title>
    <meta name="description" content="基于Go语言开发的现代化网络验证系统，提供安全可靠的TCP Socket通信、多级代理商管理、实时监控等企业级功能">
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="shortcut icon" href="/static/img/favicon.ico">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
</head>
<body>
    <!-- 现代化首页容器 -->
    <div class="modern-home-container">
        <!-- 现代化导航栏 -->
        <nav class="modern-nav" id="main-nav">
            <div class="nav-container">
                <div class="nav-brand">
                    <div class="modern-logo">
                        <div class="logo-icon-wrapper">
                            <svg class="logo-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                            </svg>
                        </div>
                        <div class="brand-text-wrapper">
                            <span class="brand-text">Qweb</span>
                            <span class="brand-subtitle">验证系统</span>
                        </div>
                    </div>
                </div>

                <div class="nav-links">
                    <a href="#features" class="nav-link">
                        <span>功能特性</span>
                        <div class="nav-link-underline"></div>
                    </a>
                    <a href="#architecture" class="nav-link">
                        <span>系统架构</span>
                        <div class="nav-link-underline"></div>
                    </a>
                    <a href="#contact" class="nav-link">
                        <span>联系我们</span>
                        <div class="nav-link-underline"></div>
                    </a>
                    <a href="/login" class="btn btn-primary btn-nav">
                        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                        </svg>
                        管理员登录
                    </a>
                </div>

                <!-- 现代化移动端菜单按钮 -->
                <button class="modern-mobile-menu-btn" onclick="toggleMobileMenu()" aria-label="切换菜单">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>
            </div>

            <!-- 现代化移动端菜单 -->
            <div class="modern-mobile-menu" id="mobile-menu">
                <div class="mobile-menu-content">
                    <a href="#features" class="mobile-nav-link">
                        <svg class="mobile-nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                        </svg>
                        功能特性
                    </a>
                    <a href="#architecture" class="mobile-nav-link">
                        <svg class="mobile-nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                        </svg>
                        系统架构
                    </a>
                    <a href="#contact" class="mobile-nav-link">
                        <svg class="mobile-nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                        </svg>
                        联系我们
                    </a>
                    <div class="mobile-menu-divider"></div>
                    <a href="/login" class="btn btn-primary btn-full">
                        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                        </svg>
                        管理员登录
                    </a>
                </div>
            </div>
        </nav>

        <!-- 现代化英雄区域 -->
        <section class="modern-hero-section">
            <div class="hero-background">
                <div class="hero-gradient-orb hero-orb-1"></div>
                <div class="hero-gradient-orb hero-orb-2"></div>
                <div class="hero-gradient-orb hero-orb-3"></div>
            </div>

            <div class="hero-container">
                <div class="hero-content">
                    <div class="hero-badge">
                        <svg class="badge-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                        <span>现代化验证解决方案</span>
                    </div>

                    <h1 class="modern-hero-title">
                        <span class="title-line">构建安全可靠的</span>
                        <span class="title-highlight">网络验证系统</span>
                        <span class="title-line">为您的业务护航</span>
                    </h1>

                    <p class="modern-hero-description">
                        基于Go语言开发的企业级网络验证平台，提供TLS加密通信、多级代理商管理、
                        实时数据监控、智能安全防护等全方位功能，助力您的数字化转型。
                    </p>

                    <div class="hero-stats">
                        <div class="stat-item">
                            <div class="stat-number" data-count="99.9">0</div>
                            <div class="stat-label">系统可用性</div>
                            <div class="stat-unit">%</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" data-count="1000">0</div>
                            <div class="stat-label">并发连接</div>
                            <div class="stat-unit">+</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" data-count="24">0</div>
                            <div class="stat-label">全天候服务</div>
                            <div class="stat-unit">h</div>
                        </div>
                    </div>

                    <div class="modern-hero-actions">
                        <a href="/login" class="btn btn-primary btn-xl hero-btn-primary">
                            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M13 7l5 5m0 0l-5 5m5-5H6"/>
                            </svg>
                            立即开始使用
                        </a>
                        <a href="#features" class="btn btn-outline btn-xl hero-btn-secondary">
                            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                            </svg>
                            探索功能特性
                        </a>
                    </div>
                </div>

                <div class="hero-visual">
                    <div class="hero-dashboard">
                        <div class="dashboard-header">
                            <div class="dashboard-title">
                                <div class="title-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                    </svg>
                                </div>
                                <span>实时监控面板</span>
                            </div>
                            <div class="dashboard-status">
                                <div class="status-dot status-online"></div>
                                <span>系统运行正常</span>
                            </div>
                        </div>

                        <div class="dashboard-metrics">
                            <div class="metric-card">
                                <div class="metric-icon metric-icon-users">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                                    </svg>
                                </div>
                                <div class="metric-value" id="demo-users">1,234</div>
                                <div class="metric-label">注册用户</div>
                                <div class="metric-trend metric-trend-up">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <polyline points="23,6 13.5,15.5 8.5,10.5 1,18"/>
                                        <polyline points="17,6 23,6 23,12"/>
                                    </svg>
                                    <span>+12%</span>
                                </div>
                            </div>

                            <div class="metric-card">
                                <div class="metric-icon metric-icon-online">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M8 14s1.5 2 4 2 4-2 4-2"/>
                                        <line x1="9" y1="9" x2="9.01" y2="9"/>
                                        <line x1="15" y1="9" x2="15.01" y2="9"/>
                                    </svg>
                                </div>
                                <div class="metric-value" id="demo-online">89</div>
                                <div class="metric-label">在线用户</div>
                                <div class="metric-trend metric-trend-up">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <polyline points="23,6 13.5,15.5 8.5,10.5 1,18"/>
                                        <polyline points="17,6 23,6 23,12"/>
                                    </svg>
                                    <span>+5%</span>
                                </div>
                            </div>

                            <div class="metric-card">
                                <div class="metric-icon metric-icon-agents">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                    </svg>
                                </div>
                                <div class="metric-value" id="demo-agents">45</div>
                                <div class="metric-label">代理商</div>
                                <div class="metric-trend metric-trend-up">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <polyline points="23,6 13.5,15.5 8.5,10.5 1,18"/>
                                        <polyline points="17,6 23,6 23,12"/>
                                    </svg>
                                    <span>+8%</span>
                                </div>
                            </div>
                        </div>

                        <div class="dashboard-chart">
                            <div class="chart-header">
                                <span>连接趋势</span>
                                <div class="chart-legend">
                                    <div class="legend-item">
                                        <div class="legend-color legend-primary"></div>
                                        <span>活跃连接</span>
                                    </div>
                                </div>
                            </div>
                            <div class="chart-area">
                                <svg class="chart-svg" viewBox="0 0 300 100">
                                    <defs>
                                        <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:rgba(102, 126, 234, 0.3);stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:rgba(102, 126, 234, 0);stop-opacity:0" />
                                        </linearGradient>
                                    </defs>
                                    <path class="chart-line" d="M0,80 Q75,20 150,40 T300,30" stroke="url(#chartGradient)" stroke-width="2" fill="none"/>
                                    <path class="chart-area" d="M0,80 Q75,20 150,40 T300,30 L300,100 L0,100 Z" fill="url(#chartGradient)"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 现代化功能特性 -->
        <section id="features" class="modern-features-section">
            <div class="section-container">
                <div class="modern-section-header">
                    <div class="section-badge">
                        <svg class="badge-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                        </svg>
                        <span>核心功能</span>
                    </div>
                    <h2 class="modern-section-title">
                        <span class="title-gradient">强大功能特性</span>
                        <br>
                        <span class="title-normal">满足企业级需求</span>
                    </h2>
                    <p class="modern-section-description">
                        集成多项先进技术，提供全方位的网络验证解决方案，
                        从安全认证到数据分析，一站式满足您的业务需求。
                    </p>
                </div>

                <div class="modern-features-grid">
                    <div class="modern-feature-card feature-card-primary">
                        <div class="feature-card-glow"></div>
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon feature-icon-security">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="feature-content">
                            <h3 class="feature-title">企业级安全认证</h3>
                            <p class="feature-description">
                                采用TLS 1.2+加密通信、AES-256数据加密、JWT令牌认证、
                                IP白名单限制等多层安全防护，确保系统固若金汤。
                            </p>
                            <div class="feature-highlights">
                                <div class="highlight-item">
                                    <svg class="highlight-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <polyline points="20,6 9,17 4,12"/>
                                    </svg>
                                    <span>TLS 1.2+ 加密</span>
                                </div>
                                <div class="highlight-item">
                                    <svg class="highlight-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <polyline points="20,6 9,17 4,12"/>
                                    </svg>
                                    <span>AES-256 数据保护</span>
                                </div>
                                <div class="highlight-item">
                                    <svg class="highlight-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <polyline points="20,6 9,17 4,12"/>
                                    </svg>
                                    <span>防暴力破解</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modern-feature-card">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon feature-icon-agents">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="feature-content">
                            <h3 class="feature-title">多级代理商体系</h3>
                            <p class="feature-description">
                                支持无限级代理商层级管理，灵活的佣金分配机制，
                                完整的权限控制和业绩统计系统。
                            </p>
                        </div>
                    </div>

                    <div class="modern-feature-card">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon feature-icon-monitor">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="feature-content">
                            <h3 class="feature-title">实时监控分析</h3>
                            <p class="feature-description">
                                WebSocket实时数据推送，系统性能监控，
                                智能异常告警，全方位掌控系统运行状态。
                            </p>
                        </div>
                    </div>

                    <div class="modern-feature-card">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon feature-icon-cards">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <rect x="1" y="4" width="22" height="16" rx="2" ry="2"/>
                                    <line x1="1" y1="10" x2="23" y2="10"/>
                                </svg>
                            </div>
                        </div>
                        <div class="feature-content">
                            <h3 class="feature-title">智能卡号管理</h3>
                            <p class="feature-description">
                                支持批量生成、导入导出、状态管理，
                                多种卡号类型和灵活的有效期设置。
                            </p>
                        </div>
                    </div>

                    <div class="modern-feature-card">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon feature-icon-audit">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="feature-content">
                            <h3 class="feature-title">完整日志审计</h3>
                            <p class="feature-description">
                                详细的操作日志记录、审计跟踪、
                                自动数据备份，确保业务合规性。
                            </p>
                        </div>
                    </div>

                    <div class="modern-feature-card">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon feature-icon-performance">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="feature-content">
                            <h3 class="feature-title">极致性能体验</h3>
                            <p class="feature-description">
                                基于Go语言高并发架构，支持千级并发连接，
                                毫秒级响应速度，稳定可靠。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 系统架构 -->
        <section id="about" class="architecture-section">
            <div class="section-container">
                <div class="section-header">
                    <h2 class="section-title">系统架构</h2>
                    <p class="section-description">
                        现代化的技术架构，确保系统的稳定性和可扩展性
                    </p>
                </div>
                
                <div class="architecture-diagram">
                    <div class="arch-layer">
                        <h3 class="arch-title">前端展示层</h3>
                        <div class="arch-components">
                            <div class="arch-component">响应式Web界面</div>
                            <div class="arch-component">WebSocket实时通信</div>
                            <div class="arch-component">RESTful API</div>
                        </div>
                    </div>
                    
                    <div class="arch-layer">
                        <h3 class="arch-title">业务逻辑层</h3>
                        <div class="arch-components">
                            <div class="arch-component">用户认证</div>
                            <div class="arch-component">代理商管理</div>
                            <div class="arch-component">卡号验证</div>
                            <div class="arch-component">权限控制</div>
                        </div>
                    </div>
                    
                    <div class="arch-layer">
                        <h3 class="arch-title">数据存储层</h3>
                        <div class="arch-components">
                            <div class="arch-component">SQLite数据库</div>
                            <div class="arch-component">数据加密</div>
                            <div class="arch-component">备份恢复</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系信息 -->
        <section id="contact" class="contact-section">
            <div class="section-container">
                <div class="section-header">
                    <h2 class="section-title">联系我们</h2>
                    <p class="section-description">
                        如有任何问题或需要技术支持，请随时联系我们
                    </p>
                </div>
                
                <div class="contact-grid">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                        </div>
                        <h3>邮箱支持</h3>
                        <p><EMAIL></p>
                    </div>
                    
                    <div class="contact-card">
                        <div class="contact-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                        </div>
                        <h3>技术文档</h3>
                        <p>查看完整的API文档和使用指南</p>
                    </div>
                    
                    <div class="contact-card">
                        <div class="contact-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                            </svg>
                        </div>
                        <h3>7×24 服务</h3>
                        <p>全天候技术支持服务</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 页脚 -->
        <footer class="home-footer">
            <div class="footer-container">
                <div class="footer-content">
                    <div class="footer-brand">
                        <div class="logo">
                            <svg class="logo-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                            </svg>
                            <span>Qweb验证系统</span>
                        </div>
                        <p class="footer-description">
                            安全可靠的网络验证解决方案
                        </p>
                    </div>
                    
                    <div class="footer-links">
                        <div class="footer-column">
                            <h4>产品</h4>
                            <ul>
                                <li><a href="#features">功能特性</a></li>
                                <li><a href="/login">管理后台</a></li>
                                <li><a href="#about">系统架构</a></li>
                            </ul>
                        </div>
                        
                        <div class="footer-column">
                            <h4>支持</h4>
                            <ul>
                                <li><a href="#contact">技术支持</a></li>
                                <li><a href="#contact">使用文档</a></li>
                                <li><a href="#contact">API接口</a></li>
                            </ul>
                        </div>
                        
                        <div class="footer-column">
                            <h4>联系方式</h4>
                            <ul>
                                <li>邮箱：<EMAIL></li>
                                <li>服务时间：7×24小时</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="footer-bottom">
                    <p>&copy; 2024 Qweb网络验证系统. 保留所有权利.</p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // 首页交互逻辑
        
        // 切换移动端菜单
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('show');
        }
        
        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // 滚动时导航栏背景变化
        window.addEventListener('scroll', function() {
            const nav = document.querySelector('.home-nav');
            if (window.scrollY > 50) {
                nav.classList.add('scrolled');
            } else {
                nav.classList.remove('scrolled');
            }
        });
        
        // 演示数据动画
        function animateNumbers() {
            const counters = [
                { element: document.getElementById('demo-users'), target: 1234, duration: 2000 },
                { element: document.getElementById('demo-online'), target: 89, duration: 1500 },
                { element: document.getElementById('demo-agents'), target: 45, duration: 1800 }
            ];
            
            counters.forEach(counter => {
                let current = 0;
                const increment = counter.target / (counter.duration / 50);
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= counter.target) {
                        current = counter.target;
                        clearInterval(timer);
                    }
                    counter.element.textContent = Math.floor(current).toLocaleString();
                }, 50);
            });
        }
        
        // 页面加载完成后启动数字动画
        window.addEventListener('load', function() {
            // 延迟启动动画以获得更好的视觉效果
            setTimeout(animateNumbers, 500);
        });
        
        // 移动端菜单点击关闭
        document.addEventListener('click', function(e) {
            const menu = document.getElementById('mobile-menu');
            const menuBtn = document.querySelector('.mobile-menu-btn');
            
            if (!menuBtn.contains(e.target) && !menu.contains(e.target)) {
                menu.classList.remove('show');
            }
        });
    </script>
</body>
</html>