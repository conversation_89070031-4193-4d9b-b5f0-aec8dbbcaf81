<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qweb网络验证系统</title>
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="shortcut icon" href="/static/img/favicon.ico">
</head>
<body>
    <!-- 首页容器 -->
    <div class="home-container">
        <!-- 导航栏 -->
        <nav class="home-nav">
            <div class="nav-container">
                <div class="nav-brand">
                    <div class="logo">
                        <svg class="logo-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                        </svg>
                        <span class="brand-text">Qweb验证系统</span>
                    </div>
                </div>
                
                <div class="nav-links">
                    <a href="#features" class="nav-link">功能特性</a>
                    <a href="#about" class="nav-link">关于系统</a>
                    <a href="#contact" class="nav-link">联系我们</a>
                    <a href="/login" class="btn btn-primary">管理员登录</a>
                </div>
                
                <!-- 移动端菜单按钮 -->
                <button class="mobile-menu-btn" onclick="toggleMobileMenu()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <line x1="3" y1="6" x2="21" y2="6"/>
                        <line x1="3" y1="12" x2="21" y2="12"/>
                        <line x1="3" y1="18" x2="21" y2="18"/>
                    </svg>
                </button>
            </div>
            
            <!-- 移动端菜单 -->
            <div class="mobile-menu" id="mobile-menu">
                <a href="#features" class="mobile-nav-link">功能特性</a>
                <a href="#about" class="mobile-nav-link">关于系统</a>
                <a href="#contact" class="mobile-nav-link">联系我们</a>
                <a href="/login" class="btn btn-primary btn-full">管理员登录</a>
            </div>
        </nav>

        <!-- 英雄区域 -->
        <section class="hero-section">
            <div class="hero-container">
                <div class="hero-content">
                    <h1 class="hero-title">
                        安全可靠的<br>
                        <span class="hero-highlight">网络验证系统</span>
                    </h1>
                    <p class="hero-description">
                        提供企业级的网络验证解决方案，支持多级代理商管理、实时监控、
                        安全加密等功能，为您的业务保驾护航。
                    </p>
                    <div class="hero-actions">
                        <a href="/login" class="btn btn-primary btn-lg">
                            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                            </svg>
                            进入管理后台
                        </a>
                        <a href="#features" class="btn btn-outline btn-lg">
                            了解更多功能
                        </a>
                    </div>
                </div>
                
                <div class="hero-visual">
                    <div class="hero-card">
                        <div class="card-header">
                            <div class="card-title">系统状态</div>
                            <div class="status-indicator">
                                <div class="status-dot status-success"></div>
                                <span>运行中</span>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="metric">
                                <div class="metric-value" id="demo-users">1,234</div>
                                <div class="metric-label">注册用户</div>
                            </div>
                            <div class="metric">
                                <div class="metric-value" id="demo-online">89</div>
                                <div class="metric-label">在线用户</div>
                            </div>
                            <div class="metric">
                                <div class="metric-value" id="demo-agents">45</div>
                                <div class="metric-label">代理商</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 功能特性 -->
        <section id="features" class="features-section">
            <div class="section-container">
                <div class="section-header">
                    <h2 class="section-title">核心功能特性</h2>
                    <p class="section-description">
                        全面的网络验证解决方案，满足各种业务需求
                    </p>
                </div>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">安全认证</h3>
                        <p class="feature-description">
                            采用AES-256加密、JWT令牌认证、IP限制等多重安全机制，
                            确保系统安全可靠。
                        </p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">多级代理</h3>
                        <p class="feature-description">
                            支持多级代理商管理体系，灵活的佣金分配机制，
                            完整的权限控制系统。
                        </p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">实时监控</h3>
                        <p class="feature-description">
                            WebSocket实时数据推送，系统性能监控，
                            异常告警，全面掌控系统运行状态。
                        </p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <rect x="1" y="4" width="22" height="16" rx="2" ry="2"/>
                                <line x1="1" y1="10" x2="23" y2="10"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">卡号管理</h3>
                        <p class="feature-description">
                            灵活的卡号生成、批量导入导出、状态管理，
                            支持多种卡号类型和有效期设置。
                        </p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">日志审计</h3>
                        <p class="feature-description">
                            完整的操作日志记录、审计跟踪、
                            数据备份恢复，确保业务合规性。
                        </p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">高性能</h3>
                        <p class="feature-description">
                            基于Go语言开发，支持高并发访问，
                            优化的数据库查询，快速响应用户请求。
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 系统架构 -->
        <section id="about" class="architecture-section">
            <div class="section-container">
                <div class="section-header">
                    <h2 class="section-title">系统架构</h2>
                    <p class="section-description">
                        现代化的技术架构，确保系统的稳定性和可扩展性
                    </p>
                </div>
                
                <div class="architecture-diagram">
                    <div class="arch-layer">
                        <h3 class="arch-title">前端展示层</h3>
                        <div class="arch-components">
                            <div class="arch-component">响应式Web界面</div>
                            <div class="arch-component">WebSocket实时通信</div>
                            <div class="arch-component">RESTful API</div>
                        </div>
                    </div>
                    
                    <div class="arch-layer">
                        <h3 class="arch-title">业务逻辑层</h3>
                        <div class="arch-components">
                            <div class="arch-component">用户认证</div>
                            <div class="arch-component">代理商管理</div>
                            <div class="arch-component">卡号验证</div>
                            <div class="arch-component">权限控制</div>
                        </div>
                    </div>
                    
                    <div class="arch-layer">
                        <h3 class="arch-title">数据存储层</h3>
                        <div class="arch-components">
                            <div class="arch-component">SQLite数据库</div>
                            <div class="arch-component">数据加密</div>
                            <div class="arch-component">备份恢复</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系信息 -->
        <section id="contact" class="contact-section">
            <div class="section-container">
                <div class="section-header">
                    <h2 class="section-title">联系我们</h2>
                    <p class="section-description">
                        如有任何问题或需要技术支持，请随时联系我们
                    </p>
                </div>
                
                <div class="contact-grid">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                        </div>
                        <h3>邮箱支持</h3>
                        <p><EMAIL></p>
                    </div>
                    
                    <div class="contact-card">
                        <div class="contact-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                        </div>
                        <h3>技术文档</h3>
                        <p>查看完整的API文档和使用指南</p>
                    </div>
                    
                    <div class="contact-card">
                        <div class="contact-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                            </svg>
                        </div>
                        <h3>7×24 服务</h3>
                        <p>全天候技术支持服务</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 页脚 -->
        <footer class="home-footer">
            <div class="footer-container">
                <div class="footer-content">
                    <div class="footer-brand">
                        <div class="logo">
                            <svg class="logo-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                            </svg>
                            <span>Qweb验证系统</span>
                        </div>
                        <p class="footer-description">
                            安全可靠的网络验证解决方案
                        </p>
                    </div>
                    
                    <div class="footer-links">
                        <div class="footer-column">
                            <h4>产品</h4>
                            <ul>
                                <li><a href="#features">功能特性</a></li>
                                <li><a href="/login">管理后台</a></li>
                                <li><a href="#about">系统架构</a></li>
                            </ul>
                        </div>
                        
                        <div class="footer-column">
                            <h4>支持</h4>
                            <ul>
                                <li><a href="#contact">技术支持</a></li>
                                <li><a href="#contact">使用文档</a></li>
                                <li><a href="#contact">API接口</a></li>
                            </ul>
                        </div>
                        
                        <div class="footer-column">
                            <h4>联系方式</h4>
                            <ul>
                                <li>邮箱：<EMAIL></li>
                                <li>服务时间：7×24小时</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="footer-bottom">
                    <p>&copy; 2024 Qweb网络验证系统. 保留所有权利.</p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // 首页交互逻辑
        
        // 切换移动端菜单
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('show');
        }
        
        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // 滚动时导航栏背景变化
        window.addEventListener('scroll', function() {
            const nav = document.querySelector('.home-nav');
            if (window.scrollY > 50) {
                nav.classList.add('scrolled');
            } else {
                nav.classList.remove('scrolled');
            }
        });
        
        // 演示数据动画
        function animateNumbers() {
            const counters = [
                { element: document.getElementById('demo-users'), target: 1234, duration: 2000 },
                { element: document.getElementById('demo-online'), target: 89, duration: 1500 },
                { element: document.getElementById('demo-agents'), target: 45, duration: 1800 }
            ];
            
            counters.forEach(counter => {
                let current = 0;
                const increment = counter.target / (counter.duration / 50);
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= counter.target) {
                        current = counter.target;
                        clearInterval(timer);
                    }
                    counter.element.textContent = Math.floor(current).toLocaleString();
                }, 50);
            });
        }
        
        // 页面加载完成后启动数字动画
        window.addEventListener('load', function() {
            // 延迟启动动画以获得更好的视觉效果
            setTimeout(animateNumbers, 500);
        });
        
        // 移动端菜单点击关闭
        document.addEventListener('click', function(e) {
            const menu = document.getElementById('mobile-menu');
            const menuBtn = document.querySelector('.mobile-menu-btn');
            
            if (!menuBtn.contains(e.target) && !menu.contains(e.target)) {
                menu.classList.remove('show');
            }
        });
    </script>
</body>
</html>