<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qweb网络验证系统 - 现代化安全验证解决方案</title>
    <meta name="description" content="基于Go语言开发的现代化网络验证系统，提供安全可靠的TCP Socket通信、多级代理商管理、实时监控等企业级功能">
    <link rel="stylesheet" href="/static/css/clean.css">
    <link rel="shortcut icon" href="/static/img/favicon.ico">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
</head>
<body>
    <!-- 全新简洁首页 -->
    <div class="clean-home-container">
        <!-- 简洁导航栏 -->
        <nav class="clean-nav">
            <div class="nav-container">
                <div class="nav-brand">
                    <h1 class="brand-title">🚀 Qweb验证系统</h1>
                </div>

                <div class="nav-links">
                    <a href="#features" class="nav-link">功能特性</a>
                    <a href="#about" class="nav-link">关于系统</a>
                    <a href="#contact" class="nav-link">联系我们</a>
                    <a href="/login" class="btn btn-primary">管理员登录</a>
                </div>

                <button class="mobile-menu-btn" onclick="toggleMobileMenu()">
                    菜单
                </button>
            </div>

            <div class="mobile-menu" id="mobile-menu">
                <a href="#features" class="mobile-nav-link">功能特性</a>
                <a href="#about" class="mobile-nav-link">关于系统</a>
                <a href="#contact" class="mobile-nav-link">联系我们</a>
                <a href="/login" class="btn btn-primary btn-full">管理员登录</a>
            </div>
        </nav>

        <!-- 简洁英雄区域 -->
        <section class="clean-hero-section">
            <div class="hero-container">
                <div class="hero-content">
                    <div class="hero-badge">
                        🚀 现代化验证解决方案
                    </div>

                    <h1 class="hero-title">
                        构建安全可靠的<br>
                        <span class="title-highlight">网络验证系统</span>
                    </h1>

                    <p class="hero-description">
                        基于Go语言开发的企业级网络验证平台，提供TLS加密通信、多级代理商管理、
                        实时数据监控、智能安全防护等全方位功能。
                    </p>

                    <div class="hero-stats">
                        <div class="stat-item">
                            <div class="stat-number">99.9%</div>
                            <div class="stat-label">系统可用性</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">1000+</div>
                            <div class="stat-label">并发连接</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">24/7</div>
                            <div class="stat-label">全天候服务</div>
                        </div>
                    </div>

                    <div class="hero-actions">
                        <a href="/login" class="btn btn-primary btn-lg">
                            立即开始使用 →
                        </a>
                        <a href="#features" class="btn btn-outline btn-lg">
                            了解更多功能
                        </a>
                    </div>
                </div>

                <div class="hero-visual">
                    <div class="status-card">
                        <div class="card-header">
                            <h3>📊 系统状态</h3>
                            <div class="status-indicator">
                                <span class="status-dot"></span>
                                <span>运行正常</span>
                            </div>
                        </div>

                        <div class="metrics-grid">
                            <div class="metric-item">
                                <div class="metric-icon">👥</div>
                                <div class="metric-info">
                                    <div class="metric-value">1,234</div>
                                    <div class="metric-label">注册用户</div>
                                </div>
                                <div class="metric-trend">+12%</div>
                            </div>

                            <div class="metric-item">
                                <div class="metric-icon">🟢</div>
                                <div class="metric-info">
                                    <div class="metric-value">89</div>
                                    <div class="metric-label">在线用户</div>
                                </div>
                                <div class="metric-trend">+5%</div>
                            </div>

                            <div class="metric-item">
                                <div class="metric-icon">🏢</div>
                                <div class="metric-info">
                                    <div class="metric-value">45</div>
                                    <div class="metric-label">代理商</div>
                                </div>
                                <div class="metric-trend">+8%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 简洁功能特性 -->
        <section id="features" class="clean-features-section">
            <div class="section-container">
                <div class="section-header">
                    <div class="section-badge">⭐ 核心功能</div>
                    <h2 class="section-title">强大功能特性</h2>
                    <p class="section-description">
                        集成多项先进技术，提供全方位的网络验证解决方案
                    </p>
                </div>

                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🔒</div>
                        <h3 class="feature-title">企业级安全认证</h3>
                        <p class="feature-description">
                            采用TLS 1.2+加密通信、AES-256数据加密、JWT令牌认证、
                            IP白名单限制等多层安全防护。
                        </p>
                        <ul class="feature-list">
                            <li>✅ TLS 1.2+ 加密</li>
                            <li>✅ AES-256 数据保护</li>
                            <li>✅ 防暴力破解</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">👥</div>
                        <h3 class="feature-title">多级代理商体系</h3>
                        <p class="feature-description">
                            支持无限级代理商层级管理，灵活的佣金分配机制，
                            完整的权限控制和业绩统计系统。
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <h3 class="feature-title">实时监控分析</h3>
                        <p class="feature-description">
                            WebSocket实时数据推送，系统性能监控，
                            智能异常告警，全方位掌控系统运行状态。
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">💳</div>
                        <h3 class="feature-title">智能卡号管理</h3>
                        <p class="feature-description">
                            支持批量生成、导入导出、状态管理，
                            多种卡号类型和灵活的有效期设置。
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">📋</div>
                        <h3 class="feature-title">完整日志审计</h3>
                        <p class="feature-description">
                            详细的操作日志记录、审计跟踪、
                            自动数据备份，确保业务合规性。
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <h3 class="feature-title">极致性能体验</h3>
                        <p class="feature-description">
                            基于Go语言高并发架构，支持千级并发连接，
                            毫秒级响应速度，稳定可靠。
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 关于系统 -->
        <section id="about" class="clean-about-section">
            <div class="section-container">
                <div class="section-header">
                    <h2 class="section-title">关于Qweb验证系统</h2>
                    <p class="section-description">
                        专业的网络验证解决方案，为您的业务提供安全可靠的保障
                    </p>
                </div>

                <div class="about-content">
                    <div class="about-text">
                        <h3>🎯 系统特点</h3>
                        <ul>
                            <li>基于Go语言开发，性能卓越</li>
                            <li>支持TCP Socket通信</li>
                            <li>完整的代理商管理体系</li>
                            <li>实时数据监控和分析</li>
                            <li>企业级安全防护</li>
                        </ul>
                    </div>

                    <div class="about-stats">
                        <div class="stat-card">
                            <div class="stat-number">5+</div>
                            <div class="stat-label">年开发经验</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">100+</div>
                            <div class="stat-label">企业客户</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">99.9%</div>
                            <div class="stat-label">系统稳定性</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系我们 -->
        <section id="contact" class="clean-contact-section">
            <div class="section-container">
                <div class="section-header">
                    <h2 class="section-title">联系我们</h2>
                    <p class="section-description">
                        有任何问题或需求，欢迎随时联系我们的技术团队
                    </p>
                </div>

                <div class="contact-info">
                    <div class="contact-item">
                        <div class="contact-icon">📧</div>
                        <div class="contact-details">
                            <h4>邮箱联系</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">💬</div>
                        <div class="contact-details">
                            <h4>在线客服</h4>
                            <p>7x24小时技术支持</p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">📚</div>
                        <div class="contact-details">
                            <h4>技术文档</h4>
                            <p>完整的API文档和使用指南</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 简洁页脚 -->
        <footer class="clean-footer">
            <div class="footer-container">
                <div class="footer-content">
                    <div class="footer-brand">
                        <h3>🚀 Qweb验证系统</h3>
                        <p>专业的网络验证解决方案</p>
                    </div>

                    <div class="footer-links">
                        <div class="footer-section">
                            <h4>产品</h4>
                            <ul>
                                <li><a href="#features">功能特性</a></li>
                                <li><a href="#about">关于系统</a></li>
                                <li><a href="/login">管理后台</a></li>
                            </ul>
                        </div>

                        <div class="footer-section">
                            <h4>支持</h4>
                            <ul>
                                <li><a href="#contact">联系我们</a></li>
                                <li><a href="#">技术文档</a></li>
                                <li><a href="#">常见问题</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="footer-bottom">
                    <p>&copy; 2024 Qweb网络验证系统. 保留所有权利.</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- JavaScript -->
    <script>
        // 移动端菜单切换
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('show');
        }

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 导航栏滚动效果
        window.addEventListener('scroll', function() {
            const nav = document.querySelector('.clean-nav');
            if (window.scrollY > 50) {
                nav.classList.add('scrolled');
            } else {
                nav.classList.remove('scrolled');
            }
        });
    </script>
</body>
</html>

        <!-- 系统架构 -->
        <section id="about" class="architecture-section">
            <div class="section-container">
                <div class="section-header">
                    <h2 class="section-title">系统架构</h2>
                    <p class="section-description">
                        现代化的技术架构，确保系统的稳定性和可扩展性
                    </p>
                </div>
                
                <div class="architecture-diagram">
                    <div class="arch-layer">
                        <h3 class="arch-title">前端展示层</h3>
                        <div class="arch-components">
                            <div class="arch-component">响应式Web界面</div>
                            <div class="arch-component">WebSocket实时通信</div>
                            <div class="arch-component">RESTful API</div>
                        </div>
                    </div>
                    
                    <div class="arch-layer">
                        <h3 class="arch-title">业务逻辑层</h3>
                        <div class="arch-components">
                            <div class="arch-component">用户认证</div>
                            <div class="arch-component">代理商管理</div>
                            <div class="arch-component">卡号验证</div>
                            <div class="arch-component">权限控制</div>
                        </div>
                    </div>
                    
                    <div class="arch-layer">
                        <h3 class="arch-title">数据存储层</h3>
                        <div class="arch-components">
                            <div class="arch-component">SQLite数据库</div>
                            <div class="arch-component">数据加密</div>
                            <div class="arch-component">备份恢复</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系信息 -->
        <section id="contact" class="contact-section">
            <div class="section-container">
                <div class="section-header">
                    <h2 class="section-title">联系我们</h2>
                    <p class="section-description">
                        如有任何问题或需要技术支持，请随时联系我们
                    </p>
                </div>
                
                <div class="contact-grid">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                        </div>
                        <h3>邮箱支持</h3>
                        <p><EMAIL></p>
                    </div>
                    
                    <div class="contact-card">
                        <div class="contact-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                        </div>
                        <h3>技术文档</h3>
                        <p>查看完整的API文档和使用指南</p>
                    </div>
                    
                    <div class="contact-card">
                        <div class="contact-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                            </svg>
                        </div>
                        <h3>7×24 服务</h3>
                        <p>全天候技术支持服务</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 页脚 -->
        <footer class="home-footer">
            <div class="footer-container">
                <div class="footer-content">
                    <div class="footer-brand">
                        <div class="logo">
                            <svg class="logo-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                            </svg>
                            <span>Qweb验证系统</span>
                        </div>
                        <p class="footer-description">
                            安全可靠的网络验证解决方案
                        </p>
                    </div>
                    
                    <div class="footer-links">
                        <div class="footer-column">
                            <h4>产品</h4>
                            <ul>
                                <li><a href="#features">功能特性</a></li>
                                <li><a href="/login">管理后台</a></li>
                                <li><a href="#about">系统架构</a></li>
                            </ul>
                        </div>
                        
                        <div class="footer-column">
                            <h4>支持</h4>
                            <ul>
                                <li><a href="#contact">技术支持</a></li>
                                <li><a href="#contact">使用文档</a></li>
                                <li><a href="#contact">API接口</a></li>
                            </ul>
                        </div>
                        
                        <div class="footer-column">
                            <h4>联系方式</h4>
                            <ul>
                                <li>邮箱：<EMAIL></li>
                                <li>服务时间：7×24小时</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="footer-bottom">
                    <p>&copy; 2024 Qweb网络验证系统. 保留所有权利.</p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // 首页交互逻辑
        
        // 切换移动端菜单
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('show');
        }
        
        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // 滚动时导航栏背景变化
        window.addEventListener('scroll', function() {
            const nav = document.querySelector('.home-nav');
            if (window.scrollY > 50) {
                nav.classList.add('scrolled');
            } else {
                nav.classList.remove('scrolled');
            }
        });
        
        // 演示数据动画
        function animateNumbers() {
            const counters = [
                { element: document.getElementById('demo-users'), target: 1234, duration: 2000 },
                { element: document.getElementById('demo-online'), target: 89, duration: 1500 },
                { element: document.getElementById('demo-agents'), target: 45, duration: 1800 }
            ];
            
            counters.forEach(counter => {
                let current = 0;
                const increment = counter.target / (counter.duration / 50);
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= counter.target) {
                        current = counter.target;
                        clearInterval(timer);
                    }
                    counter.element.textContent = Math.floor(current).toLocaleString();
                }, 50);
            });
        }
        
        // 页面加载完成后启动数字动画
        window.addEventListener('load', function() {
            // 延迟启动动画以获得更好的视觉效果
            setTimeout(animateNumbers, 500);
        });
        
        // 移动端菜单点击关闭
        document.addEventListener('click', function(e) {
            const menu = document.getElementById('mobile-menu');
            const menuBtn = document.querySelector('.mobile-menu-btn');
            
            if (!menuBtn.contains(e.target) && !menu.contains(e.target)) {
                menu.classList.remove('show');
            }
        });
    </script>
</body>
</html>