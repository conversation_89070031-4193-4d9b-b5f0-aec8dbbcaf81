package web

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"

	"qweb-verification/internal/agent"
	"qweb-verification/internal/auth"
	"qweb-verification/internal/database"
	"qweb-verification/internal/logger"
	"qweb-verification/internal/monitor"
	"qweb-verification/internal/security"
	"qweb-verification/internal/server"
)

// Config Web服务器配置
type Config struct {
	Port         int      `yaml:"port"`
	Host         string   `yaml:"host"`
	StaticPath   string   `yaml:"static_path"`
	TemplatePath string   `yaml:"template_path"`
	JWTSecret    string   `yaml:"jwt_secret"`
	CORSEnabled  bool     `yaml:"cors_enabled"`
	CORSOrigins  []string `yaml:"cors_origins"`
	Security     *SecurityConfig `yaml:"security"`
}

// Dependencies Web服务器依赖
type Dependencies struct {
	Database         *database.Database
	AuthService      *auth.AuthenticationService
	AgentService     agent.AgentService
	SecurityManager  *security.SecurityManager
	MetricsCollector monitor.MetricsCollector
	TCPServer        *server.TCPServer
}

// WebServer Web服务器
type WebServer struct {
	config       *Config
	deps         *Dependencies
	router       *gin.Engine
	server       *http.Server
	wsUpgrader   websocket.Upgrader
	adminAuth    *AdminAuthService
	apiV1        *APIV1Handler
	wsManager    *WebSocketManager
	wsPusher     *WebSocketPusher
	securityMW   *SecurityMiddleware
}

// NewWebServer 创建Web服务器
func NewWebServer(config *Config, deps *Dependencies) (*WebServer, error) {
	// 设置Gin模式
	if config.Host == "0.0.0.0" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	router := gin.New()
	
	// 创建安全中间件
	var securityMW *SecurityMiddleware
	if config.Security != nil {
		var err error
		securityMW, err = NewSecurityMiddleware(config.Security)
		if err != nil {
			return nil, fmt.Errorf("failed to create security middleware: %v", err)
		}
	}
	
	// 添加中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	
	// 添加安全中间件
	if securityMW != nil {
		router.Use(securityMW.SecurityHeadersMiddleware())
		router.Use(securityMW.IPFilterMiddleware())
		router.Use(securityMW.RateLimitMiddleware())
		router.Use(securityMW.BruteForceProtectionMiddleware())
	}
	
	// 创建管理员认证服务
	adminAuth := NewAdminAuthService(deps.Database, config.JWTSecret)
	
	// 设置安全中间件关联
	if securityMW != nil {
		adminAuth.SetSecurityMiddleware(securityMW)
	}
	
	// 创建WebSocket管理器
	wsManager := NewWebSocketManager()
	
	// 创建API处理器
	apiHandler := NewAPIV1Handler(deps, adminAuth, wsManager)
	
	// 创建WebSocket推送器
	wsPusher := NewWebSocketPusher(wsManager)
	
	ws := &WebServer{
		config: config,
		deps:   deps,
		router: router,
		wsUpgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // 生产环境中应该检查Origin
			},
		},
		adminAuth:  adminAuth,
		apiV1:     apiHandler,
		wsManager:  wsManager,
		wsPusher:   wsPusher,
		securityMW: securityMW,
	}

	// 设置路由
	ws.setupRoutes()

	// 创建HTTP服务器
	tlsConfig := &tls.Config{
		MinVersion: tls.VersionTLS12,
	}
	if securityMW != nil {
		tlsConfig = securityMW.GetTLSConfig()
	}
	
	ws.server = &http.Server{
		Addr:      fmt.Sprintf("%s:%d", config.Host, config.Port),
		Handler:   router,
		TLSConfig: tlsConfig,
	}

	return ws, nil
}

// Start 启动Web服务器
func (ws *WebServer) Start() error {
	logger.Info("Starting web server", 
		zap.String("address", ws.server.Addr),
		zap.Bool("tls_enabled", ws.config.Security != nil && ws.config.Security.EnableTLS),
	)
	
	// 启动WebSocket实时数据推送
	if ws.deps.MetricsCollector != nil {
		ws.wsPusher.StartRealTimeUpdates(ws.deps.MetricsCollector)
		logger.Info("WebSocket real-time updates started")
	}
	
	// 检查是否启用TLS
	if ws.config.Security != nil && ws.config.Security.EnableTLS {
		if ws.config.Security.TLSCertFile == "" || ws.config.Security.TLSKeyFile == "" {
			logger.Warn("TLS enabled but cert/key files not specified, using HTTP")
			return ws.server.ListenAndServe()
		}
		return ws.server.ListenAndServeTLS(ws.config.Security.TLSCertFile, ws.config.Security.TLSKeyFile)
	}
	
	// HTTP服务
	return ws.server.ListenAndServe()
}

// Shutdown 关闭Web服务器
func (ws *WebServer) Shutdown(ctx context.Context) error {
	logger.Info("Shutting down web server")
	return ws.server.Shutdown(ctx)
}

// GetWebSocketManager 获取WebSocket管理器
func (ws *WebServer) GetWebSocketManager() *WebSocketManager {
	return ws.wsManager
}

// GetWebSocketPusher 获取WebSocket推送器
func (ws *WebServer) GetWebSocketPusher() *WebSocketPusher {
	return ws.wsPusher
}

// GetRouter 获取Gin路由器
func (ws *WebServer) GetRouter() *gin.Engine {
	return ws.router
}

// setupRoutes 设置路由
func (ws *WebServer) setupRoutes() {
	// CORS中间件
	if ws.config.CORSEnabled {
		ws.router.Use(ws.corsMiddleware())
	}

	// 静态文件服务
	ws.router.Static("/static", ws.config.StaticPath)
	
	// 加载HTML模板（仅在非测试模式下）
	if gin.Mode() != gin.TestMode && ws.config.TemplatePath != "" {
		ws.router.LoadHTMLGlob(ws.config.TemplatePath + "/*")
	}

	// 主页
	ws.router.GET("/", ws.handleIndex)
	ws.router.GET("/admin", ws.handleAdmin)
	ws.router.GET("/login", ws.handleLogin)

	// API路由组
	api := ws.router.Group("/api")
	{
		// 认证相关
		auth := api.Group("/auth")
		{
			auth.POST("/login", ws.apiV1.AdminLogin)
			auth.POST("/logout", ws.apiV1.AdminLogout)
			auth.GET("/profile", ws.adminAuth.RequireAuth(), ws.apiV1.GetAdminProfile)
		}

		// v1 API
		v1 := api.Group("/v1")
		v1.Use(ws.adminAuth.RequireAuth()) // 需要认证
		{
			// 用户管理
			users := v1.Group("/users")
			{
				users.GET("", ws.apiV1.ListUsers)
				users.POST("", ws.apiV1.CreateUser)
				users.GET("/:id", ws.apiV1.GetUser)
				users.PUT("/:id", ws.apiV1.UpdateUser)
				users.DELETE("/:id", ws.apiV1.DeleteUser)
			}

			// 代理商管理
			agents := v1.Group("/agents")
			{
				agents.GET("", ws.apiV1.ListAgents)
				agents.POST("", ws.apiV1.CreateAgent)
				agents.GET("/:id", ws.apiV1.GetAgent)
				agents.PUT("/:id", ws.apiV1.UpdateAgent)
				agents.DELETE("/:id", ws.apiV1.DeleteAgent)
				agents.GET("/:id/stats", ws.apiV1.GetAgentStats)
				agents.GET("/:id/commissions", ws.apiV1.GetAgentCommissions)
			}

			// 卡号管理
			cards := v1.Group("/cards")
			{
				cards.GET("", ws.apiV1.ListCards)
				cards.POST("", ws.apiV1.CreateCard)
				cards.POST("/batch", ws.apiV1.BatchCreateCards)
				cards.GET("/:id", ws.apiV1.GetCard)
				cards.PUT("/:id", ws.apiV1.UpdateCard)
				cards.DELETE("/:id", ws.apiV1.DeleteCard)
			}

			// 系统管理
			system := v1.Group("/system")
			{
				system.GET("/stats", ws.apiV1.GetSystemStats)
				system.GET("/logs", ws.apiV1.GetLogs)
				system.GET("/config", ws.apiV1.GetConfig)
				system.PUT("/config", ws.apiV1.UpdateConfig)
				system.POST("/backup", ws.apiV1.BackupDatabase)
			}

			// 监控相关
			monitor := v1.Group("/monitor")
			{
				monitor.GET("/metrics", ws.apiV1.GetMetrics)
				monitor.GET("/alerts", ws.apiV1.GetAlerts)
				monitor.POST("/alerts", ws.apiV1.CreateAlert)
				monitor.PUT("/alerts/:id", ws.apiV1.UpdateAlert)
				monitor.DELETE("/alerts/:id", ws.apiV1.DeleteAlert)
				monitor.GET("/security", ws.apiV1.GetSecurityStats)
			}
		}
	}

	// WebSocket端点
	ws.router.GET("/ws", ws.handleWebSocket)
}

// 页面处理器
func (ws *WebServer) handleIndex(c *gin.Context) {
	c.HTML(http.StatusOK, "index.html", gin.H{
		"title": "Qweb网络验证系统",
	})
}

func (ws *WebServer) handleAdmin(c *gin.Context) {
	c.HTML(http.StatusOK, "admin.html", gin.H{
		"title": "管理后台",
	})
}

func (ws *WebServer) handleLogin(c *gin.Context) {
	c.HTML(http.StatusOK, "login.html", gin.H{
		"title": "管理员登录",
	})
}

// WebSocket处理器
func (ws *WebServer) handleWebSocket(c *gin.Context) {
	conn, err := ws.wsUpgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		logger.Error("WebSocket upgrade failed", zap.Error(err))
		return
	}

	client := NewWebSocketClient(conn, c.ClientIP())
	ws.wsManager.AddClient(client)

	go client.HandleMessages(ws.wsManager, ws.adminAuth)
}

// CORS中间件
func (ws *WebServer) corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		
		// 检查允许的源
		allowed := false
		for _, allowedOrigin := range ws.config.CORSOrigins {
			if allowedOrigin == "*" || allowedOrigin == origin {
				allowed = true
				break
			}
		}

		if allowed {
			c.Header("Access-Control-Allow-Origin", origin)
		}
		
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		c.Header("Access-Control-Max-Age", "86400")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// 错误处理器
func (ws *WebServer) handleError(c *gin.Context, err error, message string) {
	logger.Error(message, 
		zap.Error(err),
		zap.String("path", c.Request.URL.Path),
		zap.String("method", c.Request.Method),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusInternalServerError, gin.H{
		"error":   true,
		"message": message,
	})
}

// 成功响应
func (ws *WebServer) success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
	})
}

// 分页响应
func (ws *WebServer) paginated(c *gin.Context, data interface{}, total int64, page, size int) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
		"pagination": gin.H{
			"total": total,
			"page":  page,
			"size":  size,
			"pages": (total + int64(size) - 1) / int64(size),
		},
	})
}
