package models

import (
	"time"
	"gorm.io/gorm"
)

// Session 会话表模型
type Session struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	UserID       uint           `json:"user_id" gorm:"not null;index"`
	User         User           `json:"user,omitempty" gorm:"foreignKey:UserID"`
	SessionToken string         `json:"session_token" gorm:"uniqueIndex;not null;size:128"`
	IPAddress    string         `json:"ip_address" gorm:"not null;size:45"`
	UserAgent    string         `json:"user_agent" gorm:"size:255"`
	ExpiresAt    time.Time      `json:"expires_at" gorm:"not null;index"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (Session) TableName() string {
	return "sessions"
}

// IsExpired 检查会话是否过期
func (s *Session) IsExpired() bool {
	return s.ExpiresAt.Before(time.Now())
}

// IsValid 检查会话是否有效
func (s *Session) IsValid() bool {
	return !s.IsExpired() && s.DeletedAt.Time.IsZero()
}

// BeforeCreate GORM钩子：创建前
func (s *Session) BeforeCreate(tx *gorm.DB) error {
	s.CreatedAt = time.Now()
	s.UpdatedAt = time.Now()
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (s *Session) BeforeUpdate(tx *gorm.DB) error {
	s.UpdatedAt = time.Now()
	return nil
}