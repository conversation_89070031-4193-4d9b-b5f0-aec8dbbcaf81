# Qweb网络验证系统

基于Go语言开发的安全网络验证系统，采用TCP Socket通信，支持多级代理商分销、用户管理、卡号管理等功能。

## 功能特性

- 🔒 安全的TCP Socket通信（TLS 1.2+加密）
- 🏗️ 多线程并发处理
- 💾 SQLite3数据库存储
- 🌐 Web管理界面
- 👥 多级代理商分销系统
- 📊 完善的日志系统和监控告警
- 🛡️ 多重安全防护机制

## 技术栈

- **后端语言**: Go 1.19+
- **数据库**: SQLite3
- **Web框架**: Gin
- **ORM**: GORM
- **加密**: TLS 1.2+, AES-256
- **前端**: HTML/CSS/JavaScript

## 安全特性

### 连接安全
- TLS 1.2+ 加密所有通信
- IP 限制和连接频率控制  
- 防暴力破解机制，多次失败后临时封禁IP

### 数据安全
- 敏感数据加密存储
- 数据库定期备份
- 参数化查询防止SQL注入

### 认证安全
- 消息签名机制防止数据篡改
- 时间戳 + 随机数防止重放攻击
- 会话超时机制

## 项目结构

```
qweb-verification/
├── cmd/                    # 命令行工具
│   ├── server/            # 服务器启动
│   └── client/            # 客户端工具
├── internal/              # 内部包
│   ├── config/           # 配置管理
│   ├── database/         # 数据库层
│   ├── models/           # 数据模型
│   ├── security/         # 安全模块
│   ├── server/           # TCP服务器
│   ├── auth/             # 认证系统
│   ├── agent/            # 代理商系统
│   ├── web/              # Web服务
│   ├── logger/           # 日志系统
│   └── monitor/          # 监控告警
├── pkg/                   # 公共包
│   └── sdk/              # 客户端SDK
├── web/                   # Web前端资源
├── configs/               # 配置文件
├── scripts/               # 脚本文件
├── docs/                  # 文档
└── tests/                 # 测试文件
```

## 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/your-repo/qweb-verification.git
cd qweb-verification
```

### 2. 安装依赖
```bash
go mod tidy
```

### 3. 初始化数据库
```bash
go run cmd/server/main.go --init-db
```

### 4. 启动服务器
```bash
go run cmd/server/main.go
```

### 5. 访问Web管理界面
```
http://localhost:8080
```

## API文档

详细的API文档请参考 [API.md](docs/API.md)

## 客户端SDK

支持多种编程语言的客户端SDK：

- [Go SDK](pkg/sdk/go/)
- [C# SDK](pkg/sdk/csharp/)
- [Python SDK](pkg/sdk/python/)

## 配置说明

系统配置文件位于 `configs/config.yaml`，主要配置项：

```yaml
server:
  tcp_port: 9999
  web_port: 8080
  tls_cert: "certs/server.crt"
  tls_key: "certs/server.key"

database:
  path: "data/verification.db"
  
security:
  max_failed_attempts: 5
  ban_duration: "30m"
  session_timeout: "24h"
```

## 开发指南

### 1. 环境要求
- Go 1.19+
- SQLite3

### 2. 开发工具
- IDE: VS Code, GoLand等
- 调试工具: Delve
- 测试工具: go test

### 3. 代码规范
- 遵循Go官方代码规范
- 使用gofmt格式化代码
- 添加适当的注释和文档

## 贡献指南

欢迎提交Issue和Pull Request来帮助改进项目。

## 许可证

本项目采用MIT许可证，详情请参考 [LICENSE](LICENSE) 文件。